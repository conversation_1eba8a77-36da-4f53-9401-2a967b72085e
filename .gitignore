# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IntelliJ IDEA
.idea
*.iml
*.ipr
*.iws

# Compiled class file
*.class

# tmp
tmp

.cache
.classpath
.metadata
out/

# HBuilder X
.hbuilderx
node_modules
unpackage
package-lock.json

编译之后的微信小程序代码
