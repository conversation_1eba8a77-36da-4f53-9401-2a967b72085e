import App from './App'
import store from './store'

// #ifdef MP-WEIXIN
// 神策数据 SDK
import sensors from 'sa-sdk-miniprogram/dist/wechat/sensorsdata.esm.js';
sensors.init({
	name: 'sensors',
	server_url: 'https://logs.xiaomaicloud.com/sa?token=yunju&project=yunju',
	// 全埋点控制开关
	autoTrack: {
		appLaunch: true, // 默认为 true，false 则关闭 $MPLaunch 事件采集
		appShow: true, // 默认为 true，false 则关闭 $MPShow 事件采集
		appHide: true, // 默认为 true，false 则关闭 $MPHide 事件采集
		pageShow: true, // 默认为 true，false 则关闭 $MPViewScreen 事件采集
		pageShare: true, // 默认为 true，false 则关闭 $MPShare 事件采集
		mpClick: true, // 默认为 false，true 则开启 $MPClick 事件采集
		mpFavorite: true, // 默认为 true，false 则关闭 $MPAddFavorites 事件采集
		pageLeave: true // 默认为 false， true 则开启 $MPPageLeave事件采集
	},
	// 是否允许控制台打印查看埋点数据(建议开启查看)
	show_log: false
});
// #endif

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
Vue.prototype.$store = store
const app = new Vue({
	store,
    ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  // app.use(store)
  app.config.globalProperties.$store = store;
  return {
    app
  }
}
// #endif

// 设置微信小商店【店铺按钮】仅适用于调试库2.25.4以下版本
// // #ifdef MP-WEIXIN
// {
// 	const miniShopPlugin = requirePlugin('mini-shop-plugin');
// 	miniShopPlugin.initApp(getApp(), wx)
// 	if (miniShopPlugin) {
// 		miniShopPlugin.initHomePath('/pages/wxmall/index/index');
// 	}
// }
// // #endif
