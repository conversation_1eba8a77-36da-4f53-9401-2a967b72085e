{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages		
		{
			"path": "pages/tabs/index/index",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "首页",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/tabs/forum/forum",
			"style": {
				"navigationBarTitleText": "全部分类",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/tabs/create/create",
			"style": {
				"navigationBarTitleText": "发帖",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/tabs/notice/notice",
			"style": {
				"navigationBarTitleText": "消息",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/tabs/mine/mine",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom",
				"navigationBarTextStyle": "white",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/recognize/recognize",
			"style": {
				"navigationBarTitleText": "识别君"
			}
		},
		{
			"path": "pages/plant/plant",
			"style": {
				"navigationBarTitleText": "植物识别"
			}
		},
		{
			"path": "pages/animal/animal",
			"style": {
				"navigationBarTitleText": "动物识别"
			}
		},
		{
			"path": "pages/diet/diet",
			"style": {
				"navigationBarTitleText": "AI减肥相机"
			}
		}
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "云居助手小程序",
		"navigationBarBackgroundColor": "#F5F5F5",
		"backgroundColor": "#F5F5F5"
	},
	"tabBar": {
		"borderStyle": "white",
		"backgroundColor": "#FFFFFF",
		"color": "#8F8F94",
		"selectedColor": "#000000",
		// #ifndef MP-WEIXIN
		"iconWidth": "28px",
		// #endif
		"list": [{
				"pagePath": "pages/tabs/index/index",
				"iconPath": "static/tabbar/index.png",
				"selectedIconPath": "static/tabbar/index_p.png",
				"text": "首页"
			},
			{
				"pagePath": "pages/tabs/forum/forum",
				"iconPath": "static/tabbar/discover.png",
				"selectedIconPath": "static/tabbar/discover_p.png",
				"text": "分类"
			},
			// #ifdef MP-WEIXIN
			{
				"pagePath": "pages/tabs/create/create",
				"iconPath": "static/tabbar/plus.png",
				"selectedIconPath": "static/tabbar/plus_p.png",
				"text": ""
			},
			// #endif
			// #ifdef MP-BAIDU || H5
			{
				"pagePath": "pages/tabs/create/create",
				"iconPath": "static/tabbar/plus.png",
				"selectedIconPath": "static/tabbar/plus_p.png",
				"text": " "
			},
			// #endif
			{
				"pagePath": "pages/tabs/notice/notice",
				"iconPath": "static/tabbar/notice.png",
				"selectedIconPath": "static/tabbar/notice_p.png",
				"text": "消息"
			},
			{
				"pagePath": "pages/tabs/mine/mine",
				"iconPath": "static/tabbar/mine.png",
				"selectedIconPath": "static/tabbar/mine_p.png",
				"text": "我的"
			}
		]
	},
	"subPackages": [{
			"root": "pages/base",
			"pages": [{
				"path": "about/about",
				"style": {
					"navigationBarTitleText": "关于",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "page/page",
				"style": {
					"navigationBarTitleText": "单页",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "search/search",
				"style": {
					"navigationBarTitleText": "搜索",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "list/list",
				"style": {
					"navigationBarTitleText": "列表",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "webview/webview",
				"style": {
					"navigationBarTitleText": "",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "notice_list/notice_list",
				"style": {
					"navigationBarTitleText": "通知",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "channel_video/channel_video",
				"style": {
					"navigationBarTitleText": "视频播放",
					"enablePullDownRefresh": false
				}
			}]
		},
		{
			"root": "pages/user",
			"pages": [{
				"path": "friend/friend",
				"style": {
					"navigationBarTitleText": "关注/粉丝",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "home/home",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "login/login",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "verify/verify",
				"style": {
					"navigationBarTitleText": "完善资料",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "atlist/atlist",
				"style": {
					"navigationBarTitleText": "选择好友",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "atmsg/atmsg",
				"style": {
					"navigationBarTitleText": "at 消息",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "list/list",
				"style": {
					"navigationBarTitleText": "用户搜索",
					"enablePullDownRefresh": false
				}

			}, {
				"path": "logout/logout",
				"style": {
					"navigationBarTitleText": "账号注销",
					"enablePullDownRefresh": false
				}

			}]
		},
		{
			"root": "pages/bbs",
			"pages": [{
				"path": "detail/detail",
				"style": {
					"navigationBarTitleText": "详情",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "forum/forum",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "list/list",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "forum-select/forum-select",
				"style": {
					"navigationBarTitleText": "选择分类",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "forum-users/forum-users",
				"style": {
					"navigationBarTitleText": "分类成员",
					"enablePullDownRefresh": false
				}
			}]
		},
		{
			"root": "pages/business-card/",
			"pages": [{
				"path": "index/index",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "商家名片",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "list/list",
				"style": {
					"navigationBarTitleText": "列表",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "classify/classify",
				"style": {
					"navigationBarTitleText": "分类",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "detail/detail",
				"style": {
					"navigationBarTitleText": "详情",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "post/post",
				"style": {
					"navigationBarTitleText": "发布名片",
					"enablePullDownRefresh": false
				}
			}]
		},
		{
			"root": "pages/activity/",
			"pages": [{
				"path": "index/index",
				"style": {
					"navigationBarTitleText": "活动",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "detail/detail",
				"style": {
					"navigationBarTitleText": "活动详情",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "form/form",
				"style": {
					"navigationBarTitleText": "提交信息",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "my-act/my-act",
				"style": {
					"navigationBarTitleText": "我的活动",
					"enablePullDownRefresh": true
				}
			}]
		},
		{
			"root": "pages/cms/",
			"pages": [{
				"path": "index/index",
				"style": {
					"navigationBarTitleText": "资讯",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "detail/detail",
				"style": {
					"navigationBarTitleText": "详情",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "list/list",
				"style": {
					"navigationBarTitleText": "列表",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "classify/classify",
				"style": {
					"navigationBarTitleText": "分类",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "tags/tags",
				"style": {
					"navigationBarTitleText": "标签",
					"enablePullDownRefresh": false
				}
			}]
		}, {
			"root": "pages/idle-shop/",
			"pages": [{
				"path": "index/index",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "首页",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "list/list",
				"style": {
					"navigationBarTitleText": "列表",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "classify/classify",
				"style": {
					"navigationBarTitleText": "分类",
					"enablePullDownRefresh": false
				}
			}, {
				"path": "detail/detail",
				"style": {
					"navigationBarTitleText": "详情",
					"enablePullDownRefresh": true
				}
			}, {
				"path": "post/post",
				"style": {
					"navigationBarTitleText": "发布商品",
					"enablePullDownRefresh": false
				}
			}]
		}, {
			"root": "pages/plugins/",
			"pages": [{
				"path": "lottery/lottery",
				"style": {
					"navigationBarTitleText": "抽奖",
					"navigationStyle": "custom",
					"navigationBarTextStyle": "white",
					"enablePullDownRefresh": false
				}
			}]
		}
	]
}