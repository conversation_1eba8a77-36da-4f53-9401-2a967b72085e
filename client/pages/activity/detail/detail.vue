<template>
	<view class="content">
		<!-- 活动横幅 -->
		<view class="zhuige-act-banner" v-if="activity">
			<image mode="aspectFill" :src="activity.thumbnail"></image>
		</view>
		
		<view class="zhuige-act">
			<!-- 活动信息 -->
			<view class="zhuige-act-info">
				<view class="zhuige-act-title">{{activity.title}}</view>
				<view class="zhuige-act-tags">
					<view v-for="(badge, index) in activity.property.badges" :key="index">{{badge}}</view>
				</view>
				
				<!-- 活动时间 -->
				<view class="zhuige-act-line">
					<view class="zhuige-act-line-caption">
						<image mode="aspectFill" src="../static/calendar.png"></image>
						<text>活动时间</text>
					</view>
					<view class="zhuige-act-line-side">
						<text>{{activity.property.time.from}}</text>
						<text>-</text>
						<text>{{activity.property.time.to}}</text>
					</view>
				</view>
				
				<!-- 举办地址 -->
				<view class="zhuige-act-line">
					<view class="zhuige-act-line-caption">
						<image mode="aspectFill" src="../static/road.png"></image>
						<text>举办地址</text>
					</view>
					<view class="zhuige-act-line-side act-local" @click="clickAddress">
						<view>{{activity.property.address.address}}</view>
						<uni-icons type="right" size="16"></uni-icons>
					</view>
				</view>
				
				<!-- 活动费用 -->
				<view class="zhuige-act-line">
					<view class="zhuige-act-line-caption">
						<image mode="aspectFill" src="../static/money.png"></image>
						<text>活动费用</text>
					</view>
					<view class="zhuige-act-line-side act-pay">
						<text>￥</text>
						<text>{{activity.property.cost}}</text>
					</view>
				</view>
			</view>
			
			<!-- 近期参与 -->
			<view class="zhuige-block" v-if="activity">
				<view class="zhuige-block-head">
					<view>近期参与</view>
					<view>已有{{activity.enroll_count !== undefined ? activity.enroll_count : (activity.users ? activity.users.length : 0)}}人报名</view>
				</view>
				<view class="zhuige-detail-share-user">
					<view class="zhuige-detail-share-user-list">
						<view v-for="(user, index) in activity.users" :key="index" @click="clickLink('/pages/user/home/<USER>'+user.id)">
							<image mode="aspectFill" :src="user.avatar"></image>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 推荐内容 -->
			<view class="zhuige-act-detail-scroll">
				<zhuige-scroll-ad :title="activity.rec_title || '相关推荐'" :items="activity.recs || []" v-if="activity"></zhuige-scroll-ad>
			</view>

			<!-- 活动介绍 -->
			<view class="zhuige-act-detail">
				<view class="zhuige-block-head">
					<view>活动介绍</view>
				</view>
				<view class="zhuige-act-detail-info">
					<mp-html :content="activity.content"></mp-html>
				</view>
			</view>
		</view>

		<!-- 底部按钮 -->
		<view class="zhuige-act-btn">
			<view @click="clickLink('/pages/tabs/index/index')">
				<image mode="aspectFill" src="../static/homea.png"></image>
				<text>返回首页</text>
			</view>
			<view>
				<button open-type="share">
					<image mode="aspectFill" src="../static/sharea.png"></image>
					<text>分享好友</text>
				</button>
			</view>
			<view @click="clickEnroll">
				<image mode="aspectFill" src="../static/editing.png"></image>
				<text>{{activity.is_end?'已结束':activity.my_enroll?'已报名':'立即报名'+(activity.property.cost>0?'(￥'+activity.property.cost+')':'')}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 活动详情页面
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeScrollAd from "@/components/zhuige-scroll-ad";

	export default {
		components: {
		    ZhuigeScrollAd
		},
		data() {
			return {
				act_id: undefined,
				activity: undefined
			}
		},
		onLoad(option) {
			if (option.id) {
				option.act_id = option.id
			}
			if (option.act_id) {
				this.act_id = option.act_id
				Util.addShareScore(option.source)
				uni.$on('linktap', this.onMPHtmlLink)
			} else {
				uni.reLaunch({
					url: '/pages/tabs/index/index'
				})
			}
		},
		onShow() {
			this.loadData()
		},
		onUnload() {
			uni.$off('linktap', this.onMPHtmlLink)
		},
		onShareAppMessage() {
			let share = {
				title: this.activity.title + '-' + getApp().globalData.appName,
				path: Util.addShareSource('pages/activity/detail/detail?act_id=' + this.act_id)
			}
			if (this.activity && this.activity.thumbnail) {
				share.imageUrl = this.activity.thumbnail
			}
			return share
		},
		onShareTimeline() {
			let share = {
				title: this.activity.title + '-' + getApp().globalData.appName
			}
			if (this.activity && this.activity.thumbnail) {
				share.imageUrl = this.activity.thumbnail
			}
			return share
		},
		onPullDownRefresh() {
			this.loadData()
		},
		methods: {
			onMPHtmlLink(e) {
				if (e['data-link']) {
					Util.openLink(e['data-link'])
				}
			},
			clickLink(link) {
				Util.openLink(link)
			},
			clickEnroll() {
				let that = this
				if (this.activity.is_end) {
					Alert.toast('报名已结束')
					return
				}

				if (this.activity.enroll_link) {
					Util.openLink(this.activity.enroll_link)
					return
				}

				Rest.post(Api.URL('activity', 'wx_enroll_pay'), {
					act_id: this.act_id
				}).then(res => {
					if (res.code == 0) {
						if (res.data.free == 1 || res.data.pay_finish == 1) {
							// 免费活动或已支付，直接跳转报名表单
							Util.openLink('/pages/activity/form/form?act_id=' + that.act_id)
						} else if (res.data.success) {
							// 需要支付
							uni.requestPayment({
								timeStamp: res.data.timeStamp,
								nonceStr: res.data.nonceStr,
								package: res.data.package,
								signType: 'MD5',
								paySign: res.data.paySign,
								success: function(res) {
									Alert.toast('支付成功')
									setTimeout(() => {
										Util.openLink('/pages/activity/form/form?act_id=' + that.act_id)
									}, 1500)
								},
								fail: function(res) {
									Alert.toast('未支付')
								}
							})
						} else {
							if (res.data.return_msg) {
								Alert.toast(res.data.return_msg)
							} else {
								Alert.toast('不明异常~')
							}
						}
					} else {
						Alert.toast(res.message)
					}
				}, err => {
					console.log(err)
				})
			},
			clickAddress() {
				uni.openLocation({
					address: this.activity.property.address.address,
					latitude: Number.parseFloat(this.activity.property.address.latitude),
					longitude: Number.parseFloat(this.activity.property.address.longitude)
				})
			},
			loadData() {
				let that = this
				Rest.post(Api.URL('activity', 'detail'), {
					act_id: this.act_id
				}).then(res => {
					uni.stopPullDownRefresh()
					if (res.code == 0) {
						that.activity = res.data
					} else {
						Alert.toast(res.message)
					}
				}, err => {
					console.log(err)
				})
			}
		}
	}
</script>

<style lang="scss">

.content,page {
    background: #f7f7f7;
}

.zhuige-act-banner {
    height: 280px;
    overflow: hidden;
    position: relative;
    z-index: 1;
}

.zhuige-act-banner image {
    height: 280px;
    width: 100%;
}

.zhuige-act {
    margin-top: -120rpx;
    padding: 20rpx 20rpx 160rpx;
    position: relative;
    z-index: 3;
}

.zhuige-act-detail,.zhuige-act-info,.zhuige-act-join,.zhuige-guess-scroll {
    background: #fff;
    border-radius: 12rpx;
    margin-bottom: 24rpx;
    padding: 30rpx;
}

.zhuige-act-title {
    font-size: 36rpx;
    font-weight: 600;
    margin-bottom: 16rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.zhuige-act-tags {
    align-items: center;
    display: flex;
    padding-bottom: 30rpx;
}

.zhuige-act-tags view {
    background: #f2f2f2;
    border-radius: 6rpx;
    color: #333;
    font-size: 20rpx;
    font-weight: 300;
    height: 36rpx;
    line-height: 36rpx;
    margin-right: 12rpx;
    padding: 0 24rpx;
}

.zhuige-act-tags view:first-child {
    background: #fff1ed;
    color: #ff6146;
}

.zhuige-act-line {
    align-items: center;
    border-top: 1rpx solid #eee;
    display: flex;
    justify-content: space-between;
    padding: 30rpx 0;
}

.zhuige-act-line:last-of-type {
    padding-bottom: 0;
}

.zhuige-act-line-caption {
    align-items: center;
    display: flex;
}

.zhuige-act-line-caption image {
    height: 40rpx;
    margin-right: 12rpx;
    width: 40rpx;
}

.zhuige-act-line-caption text {
    font-size: 30rpx;
    font-weight: 500;
}

.zhuige-act-line-side {
    align-items: center;
    display: flex;
}

.act-pay text:nth-child(1) {
    color: #f3473f;
    font-size: 22rpx;
}

.act-pay text:nth-child(2) {
    color: #f3473f;
    font-size: 36rpx;
    font-weight: 500;
}

.act-local {
    justify-content: flex-end;
    width: 66%;
}

.act-local,.act-local view {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.zhuige-act-line-side text {
    color: #666;
    font-size: 28rpx;
    font-weight: 300;
    margin-right: 8rpx;
}

.zhuige-act-btn {
    align-items: center;
    background: #fff;
    bottom: 0;
    box-shadow: 0rpx 0rpx 6rpx rgba(0,0,0,.1);
    display: flex;
    height: 140rpx;
    left: 0;
    position: fixed;
    width: 100%;
    z-index: 99;
}

.zhuige-act-btn view {
    line-height: 1.8em;
    opacity: .7;
    text-align: center;
    width: 25%;
}

.zhuige-act-btn view button {
    background: none;
    line-height: 1.4em;
    margin: 0;
    padding: 0;
}

.zhuige-act-btn view button::after {
    border: none;
}

.zhuige-act-btn view image {
    height: 40rpx;
    width: 40rpx;
}

.zhuige-act-btn view:nth-child(1) image,.zhuige-act-btn view:nth-child(2) button image {
    margin-bottom: -20rpx;
}

.zhuige-act-btn view:nth-child(1) text,.zhuige-act-btn view:nth-child(2) text {
    color: #333;
    display: block;
    font-size: 26rpx;
    margin-bottom: 20rpx;
    width: 100%;
}

.zhuige-act-btn view:nth-child(3) {
    align-items: center;
    background: #010101;
    display: flex;
    height: 140rpx;
    justify-content: center;
    opacity: 1;
    width: 50%;
}

.zhuige-act-btn view:nth-child(3) text {
    color: #fff;
    margin-left: 12rpx;
}

.zhuige-detail-share-user-list {
    border: none;
}

.zhuige-act-detail-scroll {
    margin-bottom: 24rpx;
}

.zhuige-act-detail-info {
    font-size: 30rpx;
}

.zhuige-act-detail-info channel-video {
    border-radius: 12rpx;
    height: 480rpx;
    width: 100%;
}
</style>
