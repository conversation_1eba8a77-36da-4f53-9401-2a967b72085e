<template>
	<view class="content">
		<view class="zhuige-act">
			<!-- 活动信息 -->
			<view class="zhuige-act-img" v-if="thumbnail">
				<view class="zhuige-act-img-title">{{title}}</view>
				<image mode="aspectFill" :src="thumbnail"></image>
			</view>

			<!-- 报名表单 -->
			<view class="zhuige-act-form">
				<view class="zhuige-act-form-line" v-for="(item, index) in labels" :key="index">
					<view>{{item.label}}</view>
					<input type="text" :placeholder="'请输入'+item.label" placeholder-class="act-place"
						v-model="item.value" />
				</view>
			</view>
		</view>

		<!-- 提交按钮 -->
		<view class="zhuige-base-button" @click="clickSubmit">
			<view>提交</view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 活动报名表单
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	export default {
		components: {},
		data() {
			return {
				act_id: undefined,
				title: '',
				thumbnail: undefined,
				labels: []
			}
		},
		onLoad(option) {
			if (option.act_id) {
				this.act_id = option.act_id
				Util.addShareScore(option.source)

				Rest.post(Api.URL('activity', 'form'), {
					act_id: this.act_id
				}).then(res => {
					this.title = res.data.title
					this.thumbnail = res.data.thumbnail
					this.labels = res.data.labels
				}, err => {
					console.log(err)
				})
			} else {
				uni.reLaunch({
					url: '/pages/tabs/index/index'
				})
			}
		},
		methods: {
			clickSubmit() {
				// 验证表单
				for (let i = 0; i < this.labels.length; i++) {
					if (this.labels[i].value.length == 0) {
						Alert.toast('请填写' + this.labels[i].label)
						return
					}
				}

				// 提交报名
				Rest.post(Api.URL('activity', 'enroll'), {
					act_id: this.act_id,
					form: JSON.stringify(this.labels)
				}).then(res => {
					if (res.code == 0) {
						Alert.toast('提交成功')
						setTimeout(() => {
							Util.navigateBack()
						}, 1500)
					} else {
						Alert.toast(res.message)
					}
				}, err => {
					console.log(err)
				})
			}
		}
	}
</script>

<style lang="scss">
	.content {
		background: #f7f7f7;
		height: 100%;
		overflow-y: scroll;
		position: fixed;
		width: 100%;
	}

	.zhuige-act {
		padding: 0rpx 30rpx 100rpx;
	}

	.zhuige-act-img {
		border-radius: 12rpx 12rpx 0 0;
		height: 240px;
		overflow: hidden;
		position: relative;
	}

	.zhuige-act-img image {
		border-radius: 12rpx 12rpx 0 0;
		height: 100%;
		width: 100%;
	}

	.zhuige-act-img-title {
		background: linear-gradient(180deg, transparent, rgba(0, 0, 0, .6));
		bottom: 0;
		color: #fff;
		font-size: 30rpx;
		font-weight: 600;
		height: 160rpx;
		left: 0;
		line-height: 200rpx;
		overflow: hidden;
		padding: 0 20rpx;
		position: absolute;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 654rpx;
		z-index: 3;
	}

	.zhuige-act-form {
		background: #fff;
		border-radius: 0 0 12rpx 12rpx;
		margin-bottom: 30rpx;
		padding: 30rpx;
	}

	.zhuige-act-form-line {
		border-bottom: 1rpx solid #ddd;
		padding: 20rpx 0;
	}

	.zhuige-act-form-line:first-child {
		padding-top: 0;
	}

	.zhuige-act-form-line:last-child {
		border: none;
		padding-bottom: 0;
	}

	.zhuige-act-form-line view {
		font-size: 30rpx;
		font-weight: 500;
		padding-bottom: 16rpx;
	}

	.zhuige-act-form-line input {
		font-size: 26rpx;
		font-weight: 300;
		height: 1.4em;
		line-height: 1.4em;
	}

	.act-place {
		color: #aaa;
	}
</style>