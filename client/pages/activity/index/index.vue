<template>
	<view class="content">
		<!-- 轮播图 -->
		<zhuige-swiper v-if="slides && slides.length > 0" :items="slides" type="zhuige-height-swiper"></zhuige-swiper>

		<view class="zhuige-act">
			<!-- 分类标签 -->
			<view class="zhuige-act-tab">
				<zhuige-tab type :tabs="nav_cats" :curTab="cur_cat" @clickTab="clickTab"></zhuige-tab>
			</view>

			<!-- 活动列表 -->
			<view class="zhuige-act-timeline" v-if="list && list.length > 0">
				<view class="zhuige-act-time-block" v-for="(activity, index) in list" :key="index"
					@click="clickActivity(activity.id)">
					<view class="zhuige-act-timeline-title">
						<view class="zhuige-act-timeline-group">
							<image mode="aspectFill" :src="activity.logo"></image>
						</view>
						<view class="zhuige-act-timeline-text">
							<view>{{activity.title}}</view>
							<view>
								<text>{{activity.time.from}}</text>
								<text>-</text>
								<text>{{activity.time.to}}</text>
							</view>
						</view>
					</view>
					<view class="zhuige-act-timeline-info">
						<view class="zhuige-act-timeline-cover">
							<image mode="aspectFill" :src="activity.thumbnail"></image>
						</view>
						<view class="zhuige-act-timeline-sub">
							<view class="zhuige-act-tags">
								<text v-for="(badge, badgeIndex) in activity.badges" :key="badgeIndex">{{badge}}</text>
							</view>
							<view
								:class="['zhuige-act-btn', activity.is_end?'act-end':activity.my_enroll?'act-enroll':'']">
								{{activity.is_end?'已结束':activity.my_enroll?'已报名':( '立即报名' + (activity.cost>0?'(￥'+activity.cost+')':'') )}}
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<uni-load-more v-if="list.length > 0" :status="loadMore"></uni-load-more>
		</view>

		<!-- 悬浮按钮 -->
		<uni-fab ref="fab" :pattern="pattern" :content="content" :horizontal="horizontal" :vertical="vertical"
			:direction="direction" @trigger="fabTrigger" @fabClick="fabClick">
		</uni-fab>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 活动报名首页
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeSwiper from "@/components/zhuige-swiper";
	import ZhuigeTab from "@/components/zhuige-tab";
	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeSwiper,
			ZhuigeTab,
			ZhuigeNodata
		},
		data() {
			return {
				slides: [],
				nav_cats: [],
				cur_cat: undefined,
				share_img: undefined,
				list: [],
				loadMore: 'more',
				loaded: false,

				// 悬浮按钮配置
				title: 'uni-fab',
				directionStr: '垂直',
				horizontal: 'right',
				vertical: 'bottom',
				direction: 'horizontal',
				pattern: {
					color: '#7A7E83',
					backgroundColor: '#fff',
					selectedColor: '#010101',
					buttonColor: '#010101',
					iconColor: '#fff'
				},
				is_color_type: false,
				content: [{
					iconPath: '/pages/activity/static/home_f.png',
					selectedIconPath: '/pages/activity/static/home_f.png',
					text: '首页',
					active: false
				}, {
					iconPath: '/pages/activity/static/post_f.png',
					selectedIconPath: '/pages/activity/static/post_f.png',
					text: '我的',
					active: false
				}]
			}
		},
		onLoad(option) {
			Util.addShareScore(option.source)
			this.loadSetting()
		},
		onShow() {

		},
		onShareAppMessage() {
			let share = {
				title: '活动报名-' + getApp().globalData.appName,
				path: Util.addShareSource('pages/activity/index/index?n=n')
			}
			if (this.share_img) {
				share.imageUrl = this.share_img
			}
			return share
		},
		onShareTimeline() {
			return {
				title: '活动报名-' + getApp().globalData.appName
			}
		},
		onReachBottom() {
			if (this.loadMore == 'more') {
				this.loadActivity(false)
			}
		},
		onPullDownRefresh() {
			this.loadSetting()
		},
		methods: {
			clickTab(tab) {
				this.cur_cat = tab.id
				this.loadActivity(true)
			},
			fabTrigger(e) {
				if (e.index == 0) {
					uni.reLaunch({
						url: '/pages/tabs/index/index'
					})
				} else if (e.index == 1) {
					Util.openLink('/pages/activity/my-act/my-act')
				}
			},
			fabClick() {
				console.log('fabClick')
			},
			clickActivity(actId) {
				Util.openLink('/pages/activity/detail/detail?act_id=' + actId)
			},
			loadSetting() {
				let that = this
				Rest.post(Api.URL('activity', 'setting'), {}).then(res => {
					that.slides = res.data.slides
					that.nav_cats = res.data.nav_cats
					if (that.nav_cats.length > 0) {
						that.cur_cat = that.nav_cats[0].id
						that.loadActivity(true)
					}
					if (res.data.share_img) {
						that.share_img = res.data.share_img
					}
					uni.stopPullDownRefresh()
				}, err => {
					console.log(err)
				})
			},
			loadActivity(refresh) {
				let that = this
				let url = Api.URL('activity', 'last')
				if (this.cur_cat) {
					url = Api.URL('activity', 'cat')
				}
				Rest.post(url, {
					cat_id: this.cur_cat,
					offset: refresh ? 0 : this.list.length
				}).then(res => {
					that.list = refresh ? res.data.list : that.list.concat(res.data.list)
					that.loadMore = res.data.more
					that.loaded = true
				}, err => {
					console.log(err)
				})
			}
		}
	}
</script>

<style lang="scss">
	.content,
	page {
		background: #f7f7f7;
	}

	.zhuige-act {
		padding: 20rpx 20rpx 100rpx;
	}

	.zhuige-act-timeline {
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden;
		padding: 20rpx;
	}

	.zhuige-act-time-block {
		padding: 20rpx 0;
		position: relative;
	}

	.zhuige-act-time-block:first-of-type {
		padding-top: 0;
	}

	.zhuige-act-time-block:last-of-type {
		padding-bottom: 0;
	}

	.zhuige-act-time-block::after {
		border: 1rpx solid #ddd;
		content: "";
		height: 100%;
		left: 44rpx;
		position: absolute;
		top: 22rpx;
		z-index: 1;
	}

	.zhuige-act-timeline-title {
		align-items: center;
		display: flex;
	}

	.zhuige-act-timeline-group {
		flex: 0 0 90rpx;
		height: 90rpx;
		position: relative;
		width: 90rpx;
	}

	.zhuige-act-timeline-group::after {
		background: #fff;
		border-color: #ddd #fff #fff #ddd;
		border-radius: 50%;
		border-style: solid;
		border-width: 1px;
		content: "";
		height: 104rpx;
		left: -8rpx;
		position: absolute;
		top: -8rpx;
		transform: rotate(-45deg);
		width: 104rpx;
		z-index: 2;
	}

	.zhuige-act-timeline-group::before {
		background: rgba(0, 0, 0, .3);
		border-radius: 50%;
		content: "";
		height: 22rpx;
		left: 34rpx;
		position: absolute;
		top: 116rpx;
		width: 22rpx;
		z-index: 3;
	}

	.zhuige-act-timeline-group image {
		border-radius: 50%;
		height: 100%;
		position: relative;
		width: 100%;
		z-index: 3;
	}

	.zhuige-act-timeline-text {
		flex: 1;
		overflow: hidden;
		padding-left: 10px;
		text-overflow: ellipsis;
		width: 100%;
	}

	.zhuige-act-timeline-text view:nth-child(1) {
		font-size: 32rpx;
		font-weight: 600;
		height: 1.6em;
		line-height: 1.8em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-act-timeline-text view:nth-child(2) {
		font-size: 26rpx;
	}

	.zhuige-act-timeline-text view:nth-child(2) text {
		color: #666;
		font-weight: 300;
		margin-right: 8rpx;
	}

	.zhuige-act-timeline-info {
		border-bottom: 1rpx solid #ddd;
		margin-left: 56px;
		padding: 10rpx 0;
	}

	.zhuige-act-time-block:last-of-type .zhuige-act-timeline-info {
		border: none;
		padding-bottom: 0;
	}

	.zhuige-act-timeline-cover {
		height: 180px;
	}

	.zhuige-act-timeline-cover image {
		border-radius: 12rpx;
		height: 100%;
		width: 100%;
	}

	.zhuige-act-timeline-sub {
		justify-content: space-between;
		padding: 20rpx 0;
	}

	.zhuige-act-tags,
	.zhuige-act-timeline-sub {
		align-items: center;
		display: flex;
	}

	.zhuige-act-tags text {
		background: #f2f2f2;
		border-radius: 6rpx;
		color: #333;
		font-size: 20rpx;
		font-weight: 400;
		height: 36rpx;
		line-height: 36rpx;
		margin-right: 12rpx;
		padding: 0 18rpx;
	}

	.zhuige-act-tags text:first-child {
		background: #fff1ed;
		color: #ff6146;
	}

	.zhuige-act-btn {
		min-width: 120rpx;
		padding: 0 10px;
		white-space: nowrap;
		width: auto;
	}

	.act-end {
		background: #ccc;
	}

	.act-enroll {
		background: #010101;
	}

	.zhuige-nomore {
		color: #999;
		font-size: 24rpx;
		font-weight: 300;
		padding: 20px;
		text-align: center;
	}

	.zhuige-height-swiper {
		height: 360rpx !important;
	}

	.zhuige-act-tab {
		margin-bottom: 24rpx;
	}

	.zhuige-act-tab .zhuige-tab-nav .view:first-of-type {
		padding-left: 0;
	}
</style>