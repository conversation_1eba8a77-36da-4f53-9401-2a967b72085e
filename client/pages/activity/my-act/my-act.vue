<template>
	<view class="content">
		<view class="zhuige-act">
			<block v-if="list.length > 0">
				<view class="zhuige-act-list" v-for="(item, index) in list" :key="index"
					@click="clickActivity(item.id)">
					<image mode="aspectFill" :src="item.thumbnail"></image>
					<view class="zhuige-act-list-info">
						<view class="zhuige-act-list-info-text">
							<view class="act-list-title">{{item.title}}</view>
							<view class="act-list-time">
								<text>{{item.time.from}}</text>
								<text>-</text>
								<text>{{item.time.to}}</text>
							</view>
						</view>
						<view class="zhuige-act-list-opt act-end" v-if="item.is_end==1">已结束</view>
						<view class="zhuige-act-list-opt act-on" v-else>已报名</view>
					</view>
				</view>
				<uni-load-more :status="loadMore"></uni-load-more>
			</block>
			<block v-else>
				<zhuige-nodata v-if="loaded"></zhuige-nodata>
			</block>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 我的活动页面
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeNodata
		},
		data() {
			return {
				list: [],
				loadMore: 'more',
				loaded: false
			}
		},
		onLoad(option) {
			Util.addShareScore(option.source)
			this.loadData(true)
		},
		onShow() {

		},
		onReachBottom() {
			if (this.loadMore == 'more') {
				this.loadData(false)
			}
		},
		onPullDownRefresh() {
			this.loadData(true)
		},
		methods: {
			clickActivity(actId) {
				Util.openLink('/pages/activity/detail/detail?act_id=' + actId)
			},
			loadData(refresh) {
				let that = this
				Rest.post(Api.URL('activity', 'my_act'), {
					offset: refresh ? 0 : this.list.length
				}).then(res => {
					if (refresh) {
						uni.stopPullDownRefresh()
					}
					if (res.code == 0) {
						that.list = refresh ? res.data.list : that.list.concat(res.data.list)
						that.loadMore = res.data.more
						that.loaded = true
					} else {
						Alert.toast(res.message)
					}
				}, err => {
					console.log(err)
					if (refresh) {
						uni.stopPullDownRefresh()
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	.content,
	page {
		background: #f7f7f7;
	}

	.zhuige-act {
		padding: 0rpx 20rpx 100rpx;
	}

	.zhuige-act-list {
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
	}

	.zhuige-act-list image {
		border-radius: 12rpx;
		height: 180px;
		margin-bottom: 12rpx;
		width: 100%;
	}

	.zhuige-act-list-info {
		align-items: center;
		display: flex;
		flex-wrap: nowrap;
		justify-content: space-between;
		width: 100%;
	}

	.zhuige-act-list-info-text {
		overflow: hidden;
		text-overflow: ellipsis;
		width: 480rpx;
	}

	.act-list-title {
		font-size: 32rpx;
		font-weight: 600;
		height: 1em;
		line-height: 1em;
		margin-bottom: 8rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.act-list-time {
		display: flex;
	}

	.act-list-time text {
		color: #555;
		font-size: 26rpx;
		font-weight: 300;
		margin-right: 8rpx;
	}

	.zhuige-act-list-opt {
		background: #ff6348;
		border-radius: 12rpx;
		color: #fff;
		font-size: 26rpx;
		font-weight: 300;
		height: 60rpx;
		line-height: 60rpx;
		text-align: center;
		width: 140rpx;
	}

	.act-end {
		background: #ccc;
	}

	.act-on {
		background: #010101;
	}

	.zhuige-nomore {
		color: #999;
		font-size: 24rpx;
		font-weight: 300;
		padding: 20px;
		text-align: center;
	}
</style>