<template>
	<view class="container">
		<view class="upload-section">
			<view class="upload-box" :class="{ 'has-image': imagePath }" @tap="previewImage(imagePath)">
				<image v-if="imagePath" :src="imagePath" class="preview-image" mode="aspectFill"></image>
				<view v-else class="upload-placeholder">
					<text class="upload-icon">🐾</text>
					<text class="upload-text">点击上传或拍摄动物图片</text>
				</view>
			</view>
			<view class="action-buttons">
				<button @tap="chooseImage" class="action-btn photo-btn">
					<text class="btn-icon">🖼️</text>
					<text class="btn-text">相册选择</text>
				</button>
				<button @tap="takePhoto" class="action-btn camera-btn">
					<text class="btn-icon">📸</text>
					<text class="btn-text">拍照识别</text>
				</button>
			</view>
		</view>

		<!-- 识别结果 -->
		<scroll-view v-if="results.length > 0" class="result-scroll" scroll-y>
			<view class="result-card" v-for="(item, index) in results" :key="index">
				<view class="card-header">
					<text class="plant-name" @tap="copyText(item.name)">{{ item.name }}</text>
					<text v-if="item.scientific_name" class="scientific-name"
						@tap="copyText(item.scientific_name)">({{ item.scientific_name }})</text>
				</view>

				<view class="card-body">
					<view class="progress-container">
						<text class="progress-label">识别置信度</text>
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: (item.score * 100) + '%' }"></view>
							<text class="progress-text">{{ (item.score * 100).toFixed(1) }}%</text>
						</view>
					</view>

					<view v-if="item.description" class="description-box">
						<text class="description-title">🐾 形态特征</text>
						<view class="description-content">
							<image v-if="item.image_url" :src="item.image_url" class="image-url" mode="widthFix"
								:lazy-load="true" @error="handleImageError(index)" @load="handleImageLoad(index)"
								@tap.stop="previewImage(item.image_url)">
								<view v-if="item.image_url && !imageLoaded[index]" class="image-loading">🐾 努力加载中...
								</view>
								<view v-if="item.image_url && imageError[index]" class="image-error">
									<text>图片加载失败</text>
								</view>
							</image>
							<view class="description-container" @tap.stop="toggleDescription(index)">
								<text class="description-text" :class="{'expanded': item.isExpanded}">
									{{ item.description }}
								</text>
							</view>
							<view v-if="item.baike_url" class="source-link" @tap="copyBaikeUrl(item.baike_url)">
								<text class="link-icon">🌐</text>
								<text class="link-text">百科来源</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 广告容器 -->
		<view class="ad-container" v-if="adUnitId">
			<view class="ad-header">
				<text class="ad-icon">🐾</text>
				<text class="ad-title">发现更多动物知识</text>
			</view>
			<view class="ad-content">
				<ad :unit-id="adUnitId" @error="onAdError" class="ad-component"></ad>
			</view>
			<view class="ad-footer">
				<text class="ad-tip">我是一个有温度的广告</text>
			</view>
		</view>

		<!-- 广告列表容器 -->
		<view class="ad-list-container" v-if="adList && adList.length > 0">
			<view class="ad-list-header">
				<text class="ad-list-icon">🎯</text>
				<text class="ad-list-title">精选推荐</text>
			</view>
			<view class="ad-list-content">
				<swiper class="ad-swiper" :indicator-dots="true" :autoplay="true" :interval="5000" :duration="500"
					circular>
					<swiper-item v-for="(item, index) in adList" :key="index" class="ad-swiper-item"
						@tap="jumpToMiniProgram(item)">
						<image :src="item.imgUrl" mode="aspectFill" class="ad-swiper-image"></image>
					</swiper-item>
				</swiper>
			</view>
			<view class="ad-list-footer">
				<text class="ad-list-tip">更多精彩内容等你发现</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		BAIDU_AI_CONFIG
	} from '@/utils/config.js';

	export default {
		data() {
			return {
				imagePath: '',
				results: [],
				imageLoaded: {},
				imageError: {},
				adUnitId: BAIDU_AI_CONFIG.AD_UNIT_ID,
				adList: [] // 广告列表数据
			}
		},
		mounted() {
			this.fetchAdUnitId();
			this.fetchAdList(); // 获取广告列表
		},
		methods: {
			copyBaikeUrl(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success',
							duration: 2000
						});
					}
				});
			},
			async chooseImage() {
				try {
					const res = await uni.chooseImage({
						count: 1,
						sizeType: ['compressed']
					})
					this.imagePath = res.tempFilePaths[0]
					this.recognizeAnimal()
				} catch (err) {
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			},

			async takePhoto() {
				try {
					const res = await uni.chooseImage({
						sourceType: ['camera'],
						sizeType: ['compressed']
					})
					this.imagePath = res.tempFilePaths[0]
					this.recognizeAnimal()
				} catch (err) {
					uni.showToast({
						title: '拍照失败',
						icon: 'none'
					})
				}
			},

			async getAccessToken() {
				try {
					// 优先尝试从后端接口获取token
					const backendRes = await uni.request({
						url: BAIDU_AI_CONFIG.TOKEN_URL
					});
					return backendRes.data.access_token;
				} catch (backendError) {
					console.log('后端接口不可用，降级使用前端配置');
					// 降级方案：使用前端配置直接获取
					const url =
						`https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${BAIDU_AI_CONFIG.API_KEY}&client_secret=${BAIDU_AI_CONFIG.SECRET_KEY}`;
					const res = await uni.request({
						url
					});
					return res.data.access_token;
				}
			},

			async recognizeAnimal() {
				uni.showLoading({
					title: '识别中...'
				})

				try {
					const access_token = await this.getAccessToken()
					const fileContent = await this.getFileBase64(this.imagePath)

					const res = await uni.request({
						url: BAIDU_AI_CONFIG.ANIMAL_URL,
						method: 'POST',
						header: {
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						data: {
							access_token,
							image: fileContent,
							baike_num: 5
						}
					})

					if (res.data.result) {
						this.results = res.data.result.map(item => ({
							name: item.name,
							scientific_name: item.baike_info?.scientific_name || '',
							score: item.score || 0,
							description: item.baike_info?.description || '暂无描述信息',
							image_url: item.baike_info?.image_url || '',
							baike_url: item.baike_info?.baike_url || '',
							isExpanded: false,
							showToggle: item.baike_info?.description?.length > 150
						}))

						// 如果有识别结果，使用第一个结果的名称作为关键词获取广告
						if (this.results.length > 0) {
							const keyword = this.results[0].name;
							this.fetchAdList(keyword);
						}
					}
				} catch (err) {
					uni.showToast({
						title: '识别失败',
						icon: 'none'
					})
				}

				uni.hideLoading()
			},

			async fetchAdUnitId() {
				try {
					const res = await uni.request({
						url: BAIDU_AI_CONFIG.AD_UNIT_URL
					});
					if (res.data.ad_unit_id) {
						this.adUnitId = res.data.ad_unit_id;
					} else {
						this.adUnitId = BAIDU_AI_CONFIG.AD_UNIT_ID;
					}
				} catch (err) {
					console.log('广告接口异常:', err);
				}
			},

			onAdError(e) {
				console.error('广告加载失败:', e);
				this.adUnitId = '';
			},

			getFileBase64(filePath) {
				return new Promise((resolve) => {
					const fileManager = uni.getFileSystemManager()
					fileManager.readFile({
						filePath,
						encoding: 'base64',
						success: res => resolve(res.data)
					})
				})
			},

			handleImageLoad(index) {
				this.$set(this.imageLoaded, index, true);
				this.$set(this.imageError, index, false);
			},

			handleImageError(index) {
				this.$set(this.imageError, index, true);
				this.$set(this.imageLoaded, index, false);
			},

			toggleDescription(index) {
				this.$set(this.results[index], 'isExpanded', !this.results[index].isExpanded);
			},

			previewImage(url) {
				if (!url) return;
				uni.previewImage({
					urls: [url],
					current: url,
					indicator: 'number'
				});
			},

			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						uni.showToast({
							title: '名称已复制',
							icon: 'success',
							duration: 1500
						});
					}
				});
			},

			// 获取广告列表
			async fetchAdList(keyword = '') {
				try {
					let url = `${BAIDU_AI_CONFIG.CUSTOM_AD_URL}?sourceType=animal`;
					if (keyword) {
						url += `&keyword=${encodeURIComponent(keyword)}`;
					}
					const res = await uni.request({
						url
					});
					if (res.data && Array.isArray(res.data)) {
						this.adList = res.data;
					}
				} catch (err) {
					console.log('广告列表获取失败:', err);
				}
			},

			// 跳转到指定小程序
			jumpToMiniProgram(ad) {
				if (ad.jumpAppid && ad.jumpPagePath) {
					uni.navigateToMiniProgram({
						appId: ad.jumpAppid,
						path: ad.jumpPagePath,
						success(res) {
							console.log('跳转成功');
						},
						fail(err) {
							console.log('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				}
			}
		}
	}
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		background: linear-gradient(180deg, #FFF5F5 0%, #FFFFFF 100%);
		min-height: 100vh;
	}

	.upload-section {
		margin: 30rpx 0;
		padding: 20rpx;
	}

	.upload-box {
		width: 100%;
		height: 400rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.08);
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		margin-bottom: 30rpx;

		&.has-image {
			height: 500rpx;

			.preview-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.3s ease;

				&:active {
					transform: scale(1.02);
				}
			}
		}
	}

	.upload-placeholder {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: rgba(255, 107, 107, 0.05);

		.upload-icon {
			font-size: 80rpx;
			margin-bottom: 20rpx;
		}

		.upload-text {
			font-size: 28rpx;
			color: #FF6B6B;
			opacity: 0.8;
		}
	}

	.action-buttons {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		margin-top: 20rpx;
	}

	.action-btn {
		flex: 1;
		margin: 0 15rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 44rpx;
		border: none;
		transition: all 0.3s ease;

		&.photo-btn {
			background: linear-gradient(135deg, #FF9A8B 0%, #FF6B6B 100%);
		}

		&.camera-btn {
			background: linear-gradient(135deg, #FF6B6B 0%, #FF4B4B 100%);
		}

		&:active {
			transform: translateY(2rpx);
			opacity: 0.9;
		}

		.btn-icon {
			font-size: 36rpx;
			margin-right: 12rpx;
		}

		.btn-text {
			color: #FFFFFF;
			font-size: 28rpx;
			font-weight: 500;
		}
	}

	.result-card {
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;
		margin: 20rpx 30rpx;
		box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.08);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.12);
		}

		.card-header {
			position: relative;
			padding: 0 0 24rpx 88rpx;
			border-bottom: 2rpx solid #FFF0F0;
			margin-bottom: 24rpx;

			&::before {
				content: '🐾';
				position: absolute;
				left: 0;
				top: -8rpx;
				font-size: 48rpx;
				width: 72rpx;
				height: 72rpx;
				background: rgba(255, 107, 107, 0.1);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.plant-name {
				font-size: 36rpx;
				color: #333;
				font-weight: 600;
				margin-right: 16rpx;
				user-select: text;
				padding: 4rpx 8rpx;
				border-radius: 4rpx;
				transition: all 0.3s ease;

				&:active {
					background: rgba(255, 107, 107, 0.1);
				}
			}

			.scientific-name {
				font-size: 28rpx;
				color: #FF6B6B;
				opacity: 0.8;
				user-select: text;
				padding: 4rpx 8rpx;
				border-radius: 4rpx;
				transition: all 0.3s ease;

				&:active {
					background: rgba(255, 107, 107, 0.1);
				}
			}
		}

		.progress-container {
			margin: 32rpx 0;

			.progress-label {
				display: block;
				font-size: 28rpx;
				color: #666;
				margin-bottom: 16rpx;
			}

			.progress-bar {
				height: 24rpx;
				background: #FFF0F0;
				border-radius: 12rpx;
				position: relative;
				overflow: hidden;

				.progress-fill {
					height: 100%;
					background: linear-gradient(90deg, #FF9A8B 0%, #FF6B6B 100%);
					border-radius: 12rpx;
					transition: width 0.6s ease-out;
				}

				.progress-text {
					position: absolute;
					right: 24rpx;
					top: 50%;
					transform: translateY(-50%);
					font-size: 24rpx;
					color: #FFFFFF;
					text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.12);
				}
			}
		}

		.description-box {
			background: #FFF9F9;
			border-radius: 16rpx;
			padding: 24rpx;
			margin-top: 32rpx;

			.description-title {
				display: block;
				font-size: 30rpx;
				color: #FF6B6B;
				font-weight: 500;
				margin-bottom: 16rpx;
			}

			.description-content {
				position: relative;

				.image-url {
					max-width: 100%;
					width: auto;
					height: auto;
					border-radius: 16rpx;
					margin: 20rpx 0;
					display: block;
					animation: fadeIn 0.6s ease-in-out;
					opacity: 0;
					animation-fill-mode: forwards;
					overflow: hidden;
				}

				@keyframes fadeIn {
					from {
						opacity: 0;
						transform: translateY(20rpx);
					}

					to {
						opacity: 1;
						transform: translateY(0);
					}
				}

				.image-loading {
					color: #FF6B6B;
					font-size: 28rpx;
					padding: 20rpx;
				}

				.description-container {
					display: flex;
					align-items: flex-end;
					flex-wrap: nowrap;
				}

				.description-text {
					font-size: 28rpx;
					color: #666;
					line-height: 1.6;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 3;
					overflow: hidden;
					transition: all 0.3s;
					user-select: text;

					&.expanded {
						-webkit-line-clamp: unset;
					}
				}

				.source-link {
					margin-top: 20rpx;
					padding: 12rpx 20rpx;
					background: rgba(255, 107, 107, 0.1);
					border-radius: 8rpx;
					display: inline-flex;
					align-items: center;

					.link-icon {
						font-size: 24rpx;
						margin-right: 8rpx;
					}

					.link-text {
						font-size: 24rpx;
						color: #FF6B6B;
					}
				}
			}
		}
	}

	/* 广告样式 */
	.ad-container {
		margin: 20rpx;
		padding: 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.06);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.08);
		}

		.ad-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			padding: 0 8rpx;

			.ad-icon {
				font-size: 32rpx;
				margin-right: 8rpx;
			}

			.ad-title {
				font-size: 28rpx;
				color: #FF6B6B;
				font-weight: 500;
			}
		}

		.ad-content {
			background: #FFF9F9;
			border-radius: 12rpx;
			overflow: hidden;
			margin: 0;
			padding: 12rpx;
			min-height: 200rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.ad-component {
				width: 100%;
				height: 100%;
			}
		}

		.ad-footer {
			margin-top: 12rpx;
			text-align: center;

			.ad-tip {
				font-size: 22rpx;
				color: #999;
				opacity: 0.8;
			}
		}
	}

	/* 新增广告列表样式 */
	.ad-list-container {
		margin: 20rpx;
		padding: 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.06);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.08);
		}

		.ad-list-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			padding: 0 8rpx;

			.ad-list-icon {
				font-size: 32rpx;
				margin-right: 8rpx;
			}

			.ad-list-title {
				font-size: 28rpx;
				color: #FF6B6B;
				font-weight: 500;
			}
		}

		.ad-list-content {
			background: #FFF9F9;
			border-radius: 12rpx;
			overflow: hidden;
			margin: 0;
			padding: 12rpx;

			.ad-swiper {
				width: 100%;
				height: 600rpx;
				border-radius: 12rpx;
				overflow: hidden;

				.ad-swiper-item {
					width: 100%;
					height: 100%;

					.ad-swiper-image {
						width: 100%;
						height: 100%;
						object-fit: cover;
						border-radius: 12rpx;
					}
				}
			}
		}

		.ad-list-footer {
			margin-top: 12rpx;
			text-align: center;

			.ad-list-tip {
				font-size: 22rpx;
				color: #999;
				opacity: 0.8;
			}
		}
	}
</style>