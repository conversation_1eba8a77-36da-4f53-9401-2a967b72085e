<template>
	<view class="content">
		<view class="zhuige-classify">
			<scroll-view class="zhuige-classify-key" scroll-y scroll-with-animation>
				<view v-for="(cat, index) in cats" :key="index"
					:class="cur_cat && cur_cat.id === cat.id ? 'active' : ''" @click="clickCat(cat)">
					<text>{{ cat.title }}</text>
				</view>
			</scroll-view>
			<scroll-view class="zhuige-classify-side" scroll-y scroll-with-animation :scroll-top="scrollTop">
				<view class="zhuige-classify-swiper" v-if="slides && slides.length > 0">
					<zhuige-swiper :items="slides" type="zhuige-mini-swiper"></zhuige-swiper>
				</view>
				<view class="zhuige-classify-side-list">
					<view class="zhuige-classify-side-title" v-if="cur_cat">
						<view>{{ cur_cat.title }}</view>
						<text @click="clickMore">更多</text>
					</view>
					<view class="zhuige-block" v-if="list && list.length > 0">
						<view class="zhuige-classify-block" v-for="(card, index) in list" :key="index"
							@click="clickCard(card)">
							<view>
								<image mode="aspectFill" :src="card.logo"></image>
							</view>
							<view class="zhuige-classify-info">
								<view class="zhuige-classify-title">
									<text class="tag" v-if="card.stick">推广</text>
									<text class="sptitle">{{ card.title }}</text>
								</view>
								<view>{{ card.excerpt }}</view>
								<view class="zhuige-sp-star">
									<uni-rate :value="card.score" size="14" active-color="#FF6600" readonly>
									</uni-rate>
								</view>
							</view>
						</view>
					</view>
					<zhuige-nodata v-else-if="loaded"></zhuige-nodata>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 商家名片分类页
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeSwiper from "@/components/zhuige-swiper";
	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeSwiper,
			ZhuigeNodata
		},

		data() {
			return {
				cats: [],
				cur_cat: undefined,
				slides: [],
				list: [],
				loaded: false,
				scrollTop: 0
			};
		},

		onLoad(options) {
			Util.addShareScore(options.source);
			this.loadSetting();
		},

		onShareAppMessage() {
			return {
				title: '商家名片分类-' + getApp().globalData.appName,
				path: Util.addShareSource('pages/business-card/classify/classify?n=n')
			};
		},

		onShareTimeline() {
			return {
				title: '商家名片分类-' + getApp().globalData.appName
			};
		},

		methods: {
			clickCat(cat) {
				this.cur_cat = cat;
				this.loadCards();
			},

			clickMore() {
				if (this.cur_cat) {
					Util.openLink(`/pages/business-card/list/list?title=分类【${this.cur_cat.title}】&cat_id=${this.cur_cat.id}`);
				}
			},

			clickCard(card) {
				if (card.driect_link_switch === '1') {
					Util.openLink(card.driect_link);
				} else {
					Util.openLink(`/pages/business-card/detail/detail?id=${card.id}`);
				}
			},

			loadSetting() {
				Rest.post(Api.URL('bcard', 'setting_cat'), {}).then(res => {
					this.cats = res.data.nav_cats;
					if (this.cats.length > 0) {
						this.cur_cat = this.cats[0];
					}
					this.slides = res.data.slides;
					this.loadCards();
				}, err => {
					console.log(err);
				});
			},

			loadCards() {
				if (!this.cur_cat) return;

				Rest.post(Api.URL('bcard', 'cat'), {
					cat_id: this.cur_cat.id
				}).then(res => {
					this.list = res.data.list;
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	.content {
		height: 100%;
		position: fixed;
		width: 100%;
	}

	.zhuige-classify {
		display: flex;
		height: 100%;
		overflow-y: scroll;
		width: 100%;
	}

	.zhuige-classify-key {
		background: #fff;
		border-radius: 0 12rpx 12rpx 0;
		width: 21%;
	}

	.zhuige-classify-key::-webkit-scrollbar {
		display: none;
	}

	.zhuige-classify-key view {
		align-items: center;
		display: flex;
		font-size: 28rpx;
		font-weight: 500;
		height: 120rpx;
		justify-content: left;
		padding-left: 20rpx;
	}

	.zhuige-classify-key view.active {
		background: #f5f5f5;
	}

	.zhuige-classify-key view text {
		color: #555;
		display: block;
		font-weight: 400;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-classify-key view.active text {
		color: #010101;
		font-size: 32rpx;
		font-weight: 600;
	}

	.zhuige-classify-side {
		background: #f5f5f5;
		height: 100%;
		overflow-y: scroll;
		width: 79%;
	}

	.zhuige-classify-swiper {
		padding: 0 20rpx 20rpx;
	}

	.zhuige-classify-block>view:nth-child(1) {
		flex: 0 0 144rpx;
		height: 144rpx;
		width: 144rpx;
	}

	.zhuige-classify-side-title {
		display: flex;
		justify-content: space-between;
		padding: 10rpx 0 20rpx;
	}

	.zhuige-classify-side-title view {
		font-size: 32rpx;
		font-weight: 500;
	}

	.zhuige-classify-side-title text {
		color: #555;
		font-size: 24rpx;
		font-weight: 400;
	}

	.zhuige-classify-title {
		align-items: center;
		display: flex;
		flex-wrap: nowrap;
		height: 2em;
		line-height: 1.4em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-classify-title text.sptitle {
		font-size: 32rpx;
		font-weight: 500;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 100%;
	}

	.zhuige-classify-title text.tag {
		background: #ff6146;
		border-radius: 4rpx;
		color: #fff;
		font-size: 22rpx;
		font-weight: 300;
		line-height: 1rem;
		margin-right: 8rpx;
		padding: 1rpx 8rpx;
	}

	.zhuige-classify-info {
		padding-left: 20rpx;
		width: 370rpx;
	}

	view.zhuige-classify-title+view {
		color: #555;
		font-size: 26rpx;
		height: 1.6em;
		line-height: 1.3em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>