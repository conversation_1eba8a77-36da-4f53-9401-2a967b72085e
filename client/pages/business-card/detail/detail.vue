<template>
	<view class="content" v-if="card">
		<!-- 轮播图 -->
		<view class="zhuige-page-cover">
			<zhuige-swiper :items="card.images" type="zhuige-cover-swiper"></zhuige-swiper>
		</view>

		<!-- 基本信息 -->
		<view class="zhuige-pages-info">
			<view class="zhuige-pages-header">
				<view>
					<image :src="card.logo" mode="aspectFill"></image>
				</view>
				<text v-if="card.is_promotion">推广</text>
			</view>

			<view class="zhuige-pages-title">
				<view class="title-line">{{ card.title }}</view>
				<view class="exc-line">
					<mp-html :content="card.content"></mp-html>
				</view>
				<view>
					<text v-for="(tag, index) in card.tags" :key="index" @click="clickTag(tag)">
						{{ tag.name }}
					</text>
				</view>
			</view>

			<view class="zhuige-pages-count">
				<view class="zhuige-pages-count-num">
					<view>{{ card.views }}</view>
					<text>浏览数</text>
				</view>
				<view class="zhuige-pages-count-rate">
					<view class="zhuige-sp-star">
						<uni-rate :value="card.score" size="18" active-color="#ff6600" readonly>
						</uni-rate>
					</view>
					<text>评分（{{ card.score }}）</text>
				</view>
				<view class="zhuige-pages-count-num">
					<view>{{ card.like_list ? card.like_list.length : 0 }}</view>
					<text>点赞数</text>
				</view>
			</view>
		</view>

		<!-- 右侧按钮 -->
		<view class="zhuige-right-side-btn" style="top:800rpx;" v-if="card.is_show_promotion === 1"
			@click="clickPromotion">
			<text>付费推广</text>
		</view>

		<view class="zhuige-right-side-btn" style="top:960rpx;" v-if="card.is_show_edit" @click="clickEdit">
			<text>信息修改</text>
		</view>

		<!-- 名片信息 -->
		<block v-if="card.card">
			<!-- 迷你名片样式 -->
			<view class="zhuige-pages-card-mini" v-if="card.card.cardui === '1'">
				<view class="zhuige-card-block">
					<view class="zhuige-card-uinfo">
						<view>
							<view>{{ card.card.contact }}</view>
							<text>{{ card.card.position }}</text>
						</view>
						<view @click="clickQrcode">
							<image :src="card.card.qrcode.url" mode="aspectFill"></image>
						</view>
					</view>
					<view class="zhuige-card-cinfo">
						<view>{{ card.card.company }}</view>
						<view v-if="card.card.phone" @click="clickPhone">
							<text>电话</text>
							<text>{{ card.card.phone }}</text>
						</view>
						<view v-if="card.card.location && card.card.location.address" @click="clickAddress">
							<text>地址</text>
							<text>{{ card.card.location.address }}</text>
						</view>
						<view v-if="card.card.web" @click="clickWeb">
							<text>网址</text>
							<text>{{ card.card.web }}</text>
						</view>
					</view>
				</view>
				<view class="zhuige-card-bline">
					<view></view>
				</view>
			</view>

			<!-- 标准名片样式 -->
			<view class="zhuige-pages-card" v-else>
				<view class="zhuige-pages-card-line" v-if="card.card.contact">
					<view>
						<image src="../static/ic_usr.png" mode="aspectFill"></image>
						<text>联系人</text>
					</view>
					<view>
						<text>{{ card.card.contact }}</text>
					</view>
				</view>

				<view class="zhuige-pages-card-line" v-if="card.card.position">
					<view>
						<image src="../static/ic_job.png" mode="aspectFill"></image>
						<text>职位</text>
					</view>
					<view>
						<text>{{ card.card.position }}</text>
					</view>
				</view>

				<view class="zhuige-pages-card-line" v-if="card.card.company">
					<view>
						<image src="../static/ic_cop.png" mode="aspectFill"></image>
						<text>公司名称</text>
					</view>
					<view>
						<text>{{ card.card.company }}</text>
					</view>
				</view>

				<view class="zhuige-pages-card-line" v-if="card.card.phone" @click="clickPhone">
					<view>
						<image src="../static/ic_tel.png" mode="aspectFill"></image>
						<text>联系电话</text>
					</view>
					<view>
						<text>{{ card.card.phone }}</text>
					</view>
				</view>

				<view class="zhuige-pages-card-line" v-if="card.card.location && card.card.location.address" @click="clickAddress">
					<view>
						<image src="../static/ic_loc.png" mode="aspectFill"></image>
						<text>地址</text>
					</view>
					<view>
						<text>{{ card.card.location.address }}</text>
					</view>
				</view>

				<view class="zhuige-pages-card-line" v-if="card.card.web" @click="clickWeb">
					<view>
						<image src="../static/ic_web.png" mode="aspectFill"></image>
						<text>网址</text>
					</view>
					<view>
						<text>{{ card.card.web }}</text>
						<text>（点击复制）</text>
					</view>
				</view>
			</view>
		</block>

		<!-- 点赞分享 -->
		<view v-if="card && card.comment_switch" class="zhuige-detail-share">
			<view class="zhuige-detail-share-user">
				<view class="zhuige-detail-share-title">
					- {{ card.like_list ? card.like_list.length : 0 }}人点赞 -
				</view>
				<view class="zhuige-detail-share-user-list">
					<view v-for="(user, index) in card.like_list" :key="index" @click="clickUser(user.user_id)">
						<image :src="user.avatar" mode="aspectFill"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- 相关推荐 -->
		<view class="zhuige-detail-recom-list" :class="{ 'no-comment-spacing': !card || !card.comment_switch }" v-if="card.recs && card.recs.length > 0">
			<view class="zhuige-block-head">
				<view>相关推荐</view>
				<text @click="clickRecCat">查看更多</text>
			</view>
			<view class="zhuige-classify-block" v-for="(item, index) in card.recs" :key="index" @click="clickCard(item)">
				<view>
					<image :src="item.logo" mode="aspectFill"></image>
				</view>
				<view class="zhuige-classify-info">
					<view class="zhuige-classify-title">
						<text>{{ item.title }}</text>
					</view>
					<view>{{ item.excerpt }}</view>
					<view class="zhuige-sp-star">
						<uni-rate :value="item.score" size="14" active-color="#FF6600" readonly>
						</uni-rate>
					</view>
				</view>
			</view>
		</view>

		<!-- 评论区域 -->
		<view v-if="card && card.comment_switch" class="zhuige-block">
			<view class="zhuige-block-head">
				<view>近期回复</view>
				<view @click="clickAllComments">查看全部</view>
			</view>
			<block v-if="comments && comments.length > 0">
				<zhuige-reply v-for="(comment, index) in comments" :key="index" :item="comment"
					@clickReply="clickReply">
				</zhuige-reply>
			</block>
			<block v-else>
				<view class="zhuige-none-tips" v-if="loaded">
					<image src="@/static/404.png" mode="aspectFill"></image>
					<view>暂无评论，抢个沙发</view>
				</view>
			</block>
		</view>

		<!-- 底部操作栏 -->
		<view class="zhuige-detail-comment">
			<view v-if="card && card.comment_switch" class="zhuige-detail-opt">
				<view @click="openComment(0)">
					<uni-badge :text="card.comment_count.toString()" absolute="rightTop" :inverted="false"
						:offset="[0, 12]" type="error" :customStyle="{ backgroundColor: '#ff6146' }">
						<uni-icons :type="card.is_comment === 1 ? 'chat-filled' : 'chat'"
							:color="card.is_comment === 1 ? '#ff6146' : '#444444'" size="24">
						</uni-icons>
					</uni-badge>
				</view>
				<view @click="clickLike">
					<uni-badge :text="card.like_list ? card.like_list.length.toString() : ''" absolute="rightTop"
						:inverted="false" :offset="[0, 12]" type="error" :customStyle="{ backgroundColor: '#ff6146' }">
						<uni-icons :type="card.is_like === 1 ? 'hand-up-filled' : 'hand-up'"
							:color="card.is_like === 1 ? '#ff6146' : '#444444'" size="24">
						</uni-icons>
					</uni-badge>
				</view>
			</view>
			<view class="zhuige-detail-btn" :class="{ 'full-width': !card || !card.comment_switch }">
				<view @click="clickQrcode">联系微信</view>
				<view @click="clickPoster">分享名片</view>
			</view>
		</view>

		<!-- 海报组件 -->
		<!-- #ifdef MP-WEIXIN || H5 -->
		<l-painter v-if="isShowPainter" isCanvasToTempFilePath custom-style="position: fixed; left: 200%;" :board="base"
			@success="onPainterSuccess" />
		<!-- #endif -->

		<!-- 评论弹窗 -->
		<uni-popup ref="popupComment" type="bottom">
			<view class="zhuige-sp-appraise-box" :style="{ marginBottom: comment_bottom + 'px' }">
				<view class="zhuige-sp-appraise">
					<view class="zhuige-sp-appraise-title" v-if="parent_comment_id === 0">
						<view>用户评价</view>
						<uni-rate v-model="comment_score" size="20" active-color="#FF6146">
						</uni-rate>
					</view>
					<view class="zhuige-sp-appraise-text">
						<textarea v-model="comment_content" placeholder="说点什么吧..." maxlength="140" fixed
							@keyboardheightchange="keyboardheightchange">
						</textarea>
					</view>
					<view class="zhuige-sp-appraise-btn" @click="clickCommentOK">
						<view>提交</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 二维码弹窗 -->
		<uni-popup ref="popupQrcode" type="center">
			<view class="zhuige-pop-qr">
				<view>
					<image :src="card.card.qrcode.url" mode="aspectFill" show-menu-by-longpress></image>
				</view>
				<view>扫一扫/长按加好友</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 商家名片详情页
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeSwiper from "@/components/zhuige-swiper";
	import ZhuigeReply from "@/components/zhuige-reply";

	export default {
		components: {
			ZhuigeSwiper,
			ZhuigeReply
		},

		data() {
			return {
				card_id: undefined,
				loginReload: false,
				acode: undefined,
				reply_user_id: 0,
				card: undefined,
				comment_score: 5,
				comment_content: '',
				comment_bottom: 0,
				comments: [],
				loadMore: 'more',
				loaded: false,
				parent_comment_id: 0,
				comment_focus: false,
				isShowPainter: false,
				painterImage: '',
				base: undefined
			};
		},

		onLoad(options) {
			if (options.id) {
				this.card_id = options.id;
			} else if (options.scene) {
				this.card_id = options.scene;
			}

			if (this.card_id) {
				Util.addShareScore(options.source);
				this.loadCard();

				uni.$on('linktap', this.onMPHtmlLink);
				uni.$on('zhuige_event_user_login', this.onSetReload);
			} else {
				uni.reLaunch({
					url: '/pages/tabs/index/index'
				});
			}
		},

		onShow() {
			if (this.loginReload) {
				this.loginReload = false;
				this.loadCard();
			}
		},

		onUnload() {
			uni.$off('zhuige_event_user_login', this.onSetReload);
			uni.$off('linktap', this.onMPHtmlLink);
		},

		onPullDownRefresh() {
			this.loadCard();
		},

		onShareAppMessage() {
			const shareData = {
				path: Util.addShareSource(`pages/business-card/detail/detail?id=${this.card_id}`)
			};

			if (this.card) {
				if (this.card.title) {
					shareData.title = this.card.title + '-' + getApp().globalData.appName;
				}
				if (this.card.images && this.card.images.length > 0) {
					shareData.imageUrl = this.card.images[0].image;
				}
			}

			return shareData;
		},

		onShareTimeline() {
			const shareData = {};

			if (this.card) {
				if (this.card.title) {
					shareData.title = this.card.title + '-' + getApp().globalData.appName;
				}
				if (this.card.logo) {
					shareData.imageUrl = this.card.logo;
				}
			}

			return shareData;
		},

		methods: {
			onSetReload() {
				this.loginReload = true;
			},

			onMPHtmlLink(e) {
				if (e['data-link']) {
					Util.openLink(e['data-link']);
				}
			},

			clickPhone() {
				uni.makePhoneCall({
					phoneNumber: this.card.card.phone
				});
			},

			clickAddress() {
				uni.openLocation({
					latitude: parseFloat(this.card.card.location.latitude),
					longitude: parseFloat(this.card.card.location.longitude),
					name: this.card.card.location.marker,
					address: this.card.card.location.address,
					success: (res) => {
						console.log(res);
					},
					fail: (err) => {
						console.log(err);
					}
				});
			},

			clickWeb() {
				uni.setClipboardData({
					data: this.card.card.web,
					fail: (err) => {
						if (err.errMsg && err.errMsg.indexOf('cancel') < 0) {
							Alert.error(err.errMsg);
						}
					}
				});
			},

			clickPromotion() {
				Util.openLink(`/pages/promotion/pay/pay?id=${this.card_id}`);
			},

			clickEdit() {
				Util.openLink(`/pages/business-card/post/post?card_id=${this.card_id}`);
			},

			clickQrcode() {
				this.$refs.popupQrcode.open('center');
			},

			clickTag(tag) {
				Util.openLink(`/pages/business-card/list/list?title=标签【${tag.name}】&tag_id=${tag.id}`);
			},

			keyboardheightchange(e) {
				this.comment_bottom = e.detail.height;
			},

			checkComment() {
				if (!this.card.comment_switch) {
					Alert.error('评论已关闭');
					return false;
				}

				if (this.card.comment_require_mobile2) {
					Util.openLink('/pages/user/login/login?type=mobile&tip=评论');
					return false;
				}

				if (this.card.comment_require_avatar2) {
					Util.openLink('/pages/user/verify/verify?tip=评论');
					return false;
				}

				return true;
			},

			openComment(parentId) {
				if (this.checkComment()) {
					this.parent_comment_id = parentId;
					this.reply_user_id = 0;
					this.$refs.popupComment.open('bottom');
				}
			},

			clickReply(comment) {
				if (this.checkComment()) {
					this.parent_comment_id = comment.comment_id;
					this.reply_user_id = comment.user_id;
					this.$refs.popupComment.open('bottom');
				}
			},

			clickUser(userId) {
				Util.openLink(`/pages/user/home/<USER>
			},

			clickCard(item) {
				if (item.driect_link_switch === '1') {
					Util.openLink(item.driect_link);
				} else {
					Util.openLink(`/pages/business-card/detail/detail?id=${item.id}`);
				}
			},

			clickAllComments() {
				Util.openLink(`/pages/base/comments/comments?post_id=${this.card_id}`);
			},

			clickRecCat() {
				Util.openLink(`/pages/business-card/list/list?title=分类【${this.card.rec_cat.name}】&cat_id=${this.card.rec_cat.id}`);
			},

			loadCard() {
				Rest.post(Api.URL('bcard', 'detail'), {
					card_id: this.card_id
				}).then(res => {
					uni.stopPullDownRefresh();

					if (res.code === 0) {
						this.card = res.data;
						this.loadACode();
						this.loadComments();
					} else {
						Alert.toast(res.message);
					}
				}, err => {
					console.log(err);
				});
			},

			loadComments() {
				if (!this.card || !this.card.comment_switch) {
					this.loaded = true;
					return;
				}

				if (this.loadMore === 'loading') return;

				this.loadMore = 'loading';

				Rest.post(Api.URL('comment', 'index'), {
					post_id: this.card_id,
					offset: this.comments.length
				}).then(res => {
					this.comments = this.comments.concat(res.data.comments);
					this.loadMore = res.data.length > 0 ? 'more' : 'nomore';
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			},

			clickLike() {
				Rest.post(Api.URL('user', 'like'), {
					post_id: this.card_id
				}).then(res => {
					this.card.is_like = res.data.is_like;

					if (res.data.is_like) {
						this.card.like_list.unshift(res.data.user);
					} else {
						const newList = [];
						this.card.like_list.forEach(user => {
							if (user.user_id !== res.data.user.user_id) {
								newList.push(user);
							}
						});
						this.card.like_list = newList;
					}
				}, err => {
					console.log(err);
				});
			},

			clickCommentOK() {
				Rest.post(Api.URL('comment', 'add'), {
					post_id: this.card_id,
					parent_id: this.parent_comment_id,
					reply_id: this.reply_user_id,
					content: this.comment_content,
					score: this.comment_score
				}).then(res => {
					if (res.code === 0) {
						Alert.toast('审核后，他人可见');
						this.comment_id = 0;
						this.parent_comment_id = 0;
						this.reply_user_id = 0;
						this.comment_content = '';
						this.card.is_comment = 1;
						this.$refs.popupComment.close();
					} else if (res.code === 'require_mobile') {
						Util.openLink('/pages/user/login/login?type=mobile&tip=评论');
					} else if (res.code === 'require_avatar') {
						Util.openLink('/pages/user/verify/verify?tip=评论');
					} else {
						Alert.toast(res.message);
					}
				}, err => {
					console.log(err);
				});
			},

			clickPoster() {
				if (this.painterImage) {
					uni.previewImage({
						urls: [this.painterImage]
					});
				} else {
					uni.showLoading({
						title: '海报生成中……'
					});
					this.isShowPainter = true;

					// 构建海报数据
					const views = [];

					// 添加背景图（如果有配置）
					if (this.card.poster && this.card.poster.background) {
						views.push({
							type: 'image',
							src: this.card.poster.background,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '0rpx',
								top: '0rpx',
								width: '750rpx',
								height: '490rpx'
							}
						});
					}

					// 添加联系人姓名
					views.push({
						type: 'text',
						text: this.card.card.contact,
						css: {
							position: 'absolute',
							left: '30rpx',
							top: '40rpx',
							width: '525rpx',
							color: '#FF6146',
							fontSize: '48rpx',
							fontWeight: 'bold',
							textAlign: 'left',
							lineClamp: 1
						}
					});

					// 添加职位
					views.push({
						type: 'text',
						text: this.card.card.position,
						css: {
							position: 'absolute',
							left: '30rpx',
							top: '110rpx',
							width: '525rpx',
							color: '#555555',
							fontSize: '28rpx',
							textAlign: 'left',
							lineClamp: 1
						}
					});

					// 添加二维码
					views.push({
						type: 'image',
						src: this.acode,
						mode: 'aspectFill',
						css: {
							position: 'absolute',
							left: '560rpx',
							top: '40rpx',
							width: '160rpx',
							height: '160rpx',
							borderRadius: '10%',
							border: '1rpx solid rgb(255,255,255)'
						}
					});

					// 添加公司名称
					views.push({
						type: 'text',
						text: this.card.card.company,
						css: {
							position: 'absolute',
							left: '30rpx',
							top: '210rpx',
							width: '525rpx',
							color: '#000000',
							fontSize: '36rpx',
							fontWeight: 'bold',
							textAlign: 'left',
							lineClamp: 1
						}
					});

					// 添加电话信息
					if (this.card.card.phone) {
						views.push({
							type: 'text',
							text: '电话',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '275rpx',
								width: '525rpx',
								color: '#000000',
								fontSize: '28rpx',
								fontWeight: 'bold',
								textAlign: 'left',
								lineClamp: 1
							}
						});
						views.push({
							type: 'text',
							text: this.card.card.phone,
							css: {
								position: 'absolute',
								left: '110rpx',
								top: '275rpx',
								width: '525rpx',
								color: '#555555',
								fontSize: '28rpx',
								textAlign: 'left',
								lineClamp: 1
							}
						});
					}

					// 添加地址信息
					if (this.card.card.location && this.card.card.location.address) {
						views.push({
							type: 'text',
							text: '地址',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '325rpx',
								width: '525rpx',
								color: '#000000',
								fontSize: '28rpx',
								fontWeight: 'bold',
								textAlign: 'left',
								lineClamp: 1
							}
						});
						views.push({
							type: 'text',
							text: this.card.card.location.address,
							css: {
								position: 'absolute',
								left: '110rpx',
								top: '325rpx',
								width: '525rpx',
								color: '#555555',
								fontSize: '28rpx',
								textAlign: 'left',
								lineClamp: 1
							}
						});
					}

					// 添加网址信息
					if (this.card.card.web) {
						views.push({
							type: 'text',
							text: '网址',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '375rpx',
								width: '525rpx',
								color: '#000000',
								fontSize: '28rpx',
								fontWeight: 'bold',
								textAlign: 'left',
								lineClamp: 1
							}
						});
						views.push({
							type: 'text',
							text: this.card.card.web,
							css: {
								position: 'absolute',
								left: '110rpx',
								top: '375rpx',
								width: '525rpx',
								color: '#555555',
								fontSize: '28rpx',
								textAlign: 'left',
								lineClamp: 1
							}
						});
					}

					// 添加装饰条
					views.push({
						type: 'view',
						css: {
							position: 'absolute',
							left: '0rpx',
							top: '466rpx',
							width: '500rpx',
							height: '25rpx',
							background: '#FF6146'
						}
					});
					views.push({
						type: 'view',
						css: {
							position: 'absolute',
							left: '500rpx',
							top: '466rpx',
							width: '250rpx',
							height: '25rpx',
							background: '#363b5b'
						}
					});

					this.base = {
						css: {
							width: '750rpx',
							height: '490rpx',
							backgroundColor: '#FFFFFF'
						},
						views: views
					};
				}
			},

			onPainterSuccess(imagePath) {
				this.painterImage = imagePath;
				uni.previewImage({
					urls: [imagePath]
				});
				uni.hideLoading();
			},

			loadACode() {
				this.acode = this.card.card.qrcode.url
			}
		}
	}
</script>

<style lang="scss">
	@import "@/style/main.css";
	@import "@/style/list.css";

	/* 商家名片详情页专用样式 */
	.content {
		background: #f5f5f5;
		padding-bottom: 140rpx;
	}

	.zhuige-page-cover {
		min-height: 480rpx;
	}

	.zhuige-pages-info {
		background: #fff;
		border-bottom: 20rpx solid #f5f5f5;
		border-radius: 30rpx 30rpx 0 0;
		margin-top: -30rpx;
		padding: 30rpx;
		position: relative;
		z-index: 9;
	}

	.zhuige-pages-header {
		display: flex;
		flex-direction: row-reverse;
		justify-content: space-between;
		position: relative;
	}

	.zhuige-pages-header text {
		background: #ff6147;
		border-radius: 3px;
		color: #fff;
		font-size: 20rpx;
		font-weight: 400;
		height: 2em;
		line-height: 2em;
		padding: 0 8px;
	}

	.zhuige-pages-header image {
		border-radius: 50%;
		height: 160rpx;
		margin-top: -120rpx;
		width: 160rpx;
	}

	.zhuige-pages-title {
		border-bottom: 1px solid #eee;
	}

	.zhuige-pages-title view {
		margin-bottom: 30rpx;
	}

	.title-line {
		font-size: 36rpx;
		height: 1.6em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-weight: 500;
		line-height: 1.6em;
	}

	.exc-line {
		color: #888;
		font-size: 26rpx;
		font-weight: 500;
		line-height: 1.6em;
	}

	.zhuige-pages-title view:nth-child(3) {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
	}

	.zhuige-pages-title view:nth-child(3) text {
		background: #f5f5f5;
		border-radius: 6rpx;
		font-size: 22rpx;
		font-weight: 400;
		height: 2em;
		line-height: 2em;
		margin-right: 8rpx;
		padding: 0 20rpx;
	}

	.zhuige-pages-count {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding: 40rpx 0 10rpx;
	}

	.zhuige-pages-count-num {
		border-right: 1px solid #eee;
		text-align: center;
		width: 180rpx;
	}

	.zhuige-pages-count-num:last-of-type {
		border-left: 1px solid #eee;
		border-right: 0;
	}

	.zhuige-pages-count-num view {
		font-size: 40rpx;
		font-weight: 600;
		height: 1.8em;
		line-height: 1.8em;
	}

	.zhuige-pages-count-num text {
		color: #888;
		font-size: 24rpx;
		font-weight: 500;
	}

	.zhuige-pages-count-rate {
		text-align: center;
	}

	.zhuige-pages-count-rate text:last-of-type {
		color: #888;
		font-size: 24rpx;
		font-weight: 500;
		line-height: 2em;
	}

	.zhuige-pages-card {
		background: #fff;
		border-bottom: 20rpx solid #f5f5f5;
		padding: 30rpx;
	}

	.zhuige-pages-card-line {
		align-items: center;
		border-bottom: 1px solid #eee;
		display: flex;
		justify-content: space-between;
		padding: 30rpx 0;
	}

	.zhuige-pages-card-line:last-of-type {
		border: none;
	}

	.zhuige-pages-card-line view:nth-child(1) {
		align-items: center;
		display: flex;
		max-width: 180rpx;
	}

	.zhuige-pages-card-line view:nth-child(1) image {
		height: 36rpx;
		margin-right: 8rpx;
		width: 36rpx;
	}

	.zhuige-pages-card-line view:nth-child(1) text {
		font-size: 30rpx;
		font-weight: 500;
	}

	.zhuige-pages-card-line view:nth-child(2) text {
		color: #888;
		font-size: 28rpx;
		font-weight: 400;
	}

	.zhuige-pages-card-line view:nth-child(2) text:nth-child(2) {
		color: #333;
	}

	.zhuige-pages-card-mini {
		background: #fff;
	}

	.zhuige-pages-card-mini .zhuige-block-head {
		padding: 30rpx 30rpx 0;
	}

	.zhuige-detail-share {
		background: #fff;
		border-bottom: 20rpx solid #f5f5f5;
		padding: 30rpx;
	}

	.zhuige-detail-share-user-list {
		border: none;
	}

	.zhuige-detail-share-opt {
		justify-content: center;
		padding: 50rpx 0;
		align-items: center;
		display: flex;
	}

	.zhuige-detail-share-opt view {
		align-items: center;
		display: flex;
		border: 1rpx solid #ddd;
		border-radius: 72rpx;
		height: 72rpx;
		line-height: 72rpx;
		margin: 0 20rpx;
		padding: 0 32rpx;
	}

	.zhuige-detail-share-opt view text {
		font-size: 28rpx;
		font-weight: 400;
	}

	.zhuige-detail-share-opt view image {
		height: 32rpx;
		margin-left: 12rpx;
		width: 32rpx;
	}

	.zhuige-classify-block {
		padding: 30rpx 0;
	}

	.zhuige-classify-block>view:nth-child(1) {
		flex: 0 0 180rpx;
		height: 180rpx;
		width: 180rpx;
	}

	.zhuige-classify-title {
		align-items: center;
		display: flex;
		flex-wrap: nowrap;
		height: 2em;
		line-height: 1.4em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-classify-title text {
		font-size: 32rpx;
		font-weight: 500;
	}

	.zhuige-classify-info {
		overflow: hidden;
		padding-left: 20rpx;
		width: 560rpx;
	}

	view.zhuige-classify-title+view {
		color: #555;
		font-size: 26rpx;
		height: 2.2em;
		line-height: 2.2em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-detail-recom-list {
		background: #fff;
		border-bottom: 20rpx solid #f5f5f5;
		padding: 10rpx 30rpx;
	}

	.zhuige-detail-recom-list.no-comment-spacing {
		border-top: 10rpx solid #f5f5f5;
		margin-top: 10rpx;
	}

	.zhuige-detail-comment {
		background: #fff;
		bottom: 0;
		box-shadow: 0rpx 0rpx 6rpx rgba(99, 99, 99, .1);
		height: 140rpx;
		justify-content: space-between;
		left: 0;
		padding-bottom: 20rpx;
		position: fixed;
		width: 100%;
		z-index: 20;
		align-items: center;
		display: flex;
	}

	.zhuige-detail-opt {
		align-items: center;
		display: flex;
		padding: 0 30rpx;
	}

	.zhuige-detail-opt>view {
		margin: 0 32rpx;
	}

	.zhuige-detail-btn {
		align-items: center;
		display: flex;
		padding: 0 30rpx;
	}

	.zhuige-detail-btn view {
		background: #ff6147;
		color: #fff;
		font-size: 28rpx;
		font-weight: 400;
		height: 90rpx;
		line-height: 90rpx;
		padding: 0 40rpx;
	}

	.zhuige-detail-btn view:nth-child(1) {
		border-radius: 90rpx 0 0 90rpx;
		padding-left: 60rpx;
	}

	.zhuige-detail-btn view:nth-child(2) {
		background: #363b51;
		border-radius: 0 90rpx 90rpx 0;
		padding-right: 60rpx;
	}

	.zhuige-detail-btn.full-width {
		width: 100%;
	}

	.zhuige-detail-btn.full-width view {
		width: 50%;
	}

	.zhuige-detail-btn.full-width view:nth-child(1) {
		border-radius: 90rpx 0 0 90rpx;
	}

	.zhuige-detail-btn.full-width view:nth-child(2) {
		border-radius: 0 90rpx 90rpx 0;
	}

	.zhuige-right-side-btn {
		background: hsla(0, 0%, 100%, .9);
		border-radius: 64rpx 0 0 64rpx;
		box-shadow: 0 10rpx 16rpx -8rpx rgba(79, 125, 183, .3);
		color: #777;
		font-size: 24rpx;
		font-weight: 400;
		height: 64rpx;
		line-height: 64rpx;
		padding: 0 32rpx 0 42rpx;
		position: fixed;
		right: 0;
		z-index: 99;
	}

	.zhuige-card-block {
		background: #fff;
	}

	.zhuige-card-uinfo {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding: 30rpx 30rpx 10rpx;
	}

	.zhuige-card-uinfo view:nth-child(1) view {
		color: #ff6146;
		font-size: 40rpx;
		font-weight: 600;
		line-height: 1.2em;
	}

	.zhuige-card-uinfo view:nth-child(1) text {
		color: #666;
		font-size: 26rpx;
		font-weight: 400;
	}

	.zhuige-card-uinfo view:nth-child(2) {
		height: 128rpx;
		width: 128rpx;
	}

	.zhuige-card-uinfo view:nth-child(2) image {
		height: 100%;
		width: 100%;
	}

	.zhuige-card-cinfo {
		padding: 30rpx;
	}

	.zhuige-card-cinfo view {
		line-height: 1.6em;
	}

	.zhuige-card-cinfo view:nth-child(1) {
		font-size: 32rpx;
		font-weight: 600;
	}

	.zhuige-card-cinfo view text:nth-child(1) {
		font-weight: 500;
		margin-right: 12rpx;
	}

	.zhuige-card-cinfo view text:nth-child(2) {
		color: #666;
		font-weight: 400;
	}

	.zhuige-card-bline {
		background: #363b51;
		display: flex;
		height: 30rpx;
		line-height: 30rpx;
	}

	.zhuige-card-bline view {
		background: #ff6146;
		width: 70%;
	}

	.zhuige-sp-appraise-box {
		background: #f7f7f7;
		border-radius: 12rpx 12rpx 0 0;
		bottom: 0;
		position: fixed;
		width: 100%;
	}

	.zhuige-sp-appraise {
		padding: 30rpx;
	}

	.zhuige-sp-appraise-title {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding-bottom: 20rpx;
	}

	.zhuige-sp-appraise-text {
		background: #fff;
		border-radius: 12rpx;
		height: 240rpx;
		margin-bottom: 40rpx;
		padding: 30rpx;
	}

	.zhuige-sp-appraise-text textarea {
		height: 240rpx;
		line-height: normal;
		width: 100%;
	}

	.zhuige-sp-appraise-btn {
		align-items: center;
		display: flex;
		justify-content: center;
		padding: 0 30rpx 30rpx;
	}

	.zhuige-sp-appraise-btn view {
		background: #ff6146;
		border-radius: 12rpx;
		color: #fff;
		font-size: 26rpx;
		font-weight: 300;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		width: 60%;
	}

	.zhuige-cover-swiper .zhuige-swiper-group {
		border-radius: 0 !important;
	}

	.zhuige-block {
		padding: 20rpx 30rpx;
	}

	.zhuige-block-head {
		margin: 0;
	}

	.zhuige-block-head text {
		color: #888;
	}

	.zhuige-pop-qr {
		background: #fff;
		border-radius: 20rpx;
	}

	.zhuige-pop-qr view:nth-child(1) {
		padding: 20rpx;
	}

	.zhuige-pop-qr image {
		height: 200px;
		width: 200px;
	}

	.zhuige-pop-qr view:nth-child(2) {
		background: #ff6146;
		border-radius: 0 0 20rpx 20rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: 400;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
	}

	.zhuige-cover-swiper {
		height: 480rpx !important;
	}

	/* 组件样式覆盖 */
	.content .zhuige-block zhuige-reply:first-of-type .zhuige-reply-block {
		border: none !important;
	}

	.content .zhuige-page-cover .zhuige-dots-left .wx-swiper-dots.wx-swiper-dots-horizontal {
		bottom: 60rpx !important;
	}

	.content .zhuige-cover-swiper .zhuige-swiper-group {
		border-radius: 0 !important;
	}
</style>