<template>
	<view class="content">
		<!-- 自定义导航栏 -->
		<uni-nav-bar title="商家名片" left-icon="back" @clickLeft="clickBack" :opacity="nav_opacity" :fixed="true"
			:status-bar="true" :placeholder="false">
		</uni-nav-bar>

		<!-- 主体内容 -->
		<view class="zhuige-yp-box"
			:style="background ? 'background: url(' + background + ') no-repeat top; background-size: 200%;' : ''">
			<!-- LOGO -->
			<view class="zhuige-yp-logo" v-if="logo">
				<image mode="aspectFit" :src="logo"></image>
			</view>

			<!-- 搜索框 -->
			<view class="zhuige-yp-search">
				<view>
					<uni-icons type="search" size="20" color="#666666"></uni-icons>
					<input v-model="search" placeholder="请输入关键词..." type="text" confirm-type="search"
						@confirm="confirmSearch" />
				</view>
				<view @click="clickSearch">搜索</view>
			</view>

			<!-- 轮播图 -->
			<view class="zhuige-yp-swiper">
				<zhuige-swiper :items="slides" v-if="slides && slides.length > 0"></zhuige-swiper>
			</view>

			<!-- 图标导航 -->
			<view class="zhuige-block" v-if="icon && icon.items && icon.items.length > 0">
				<view class="zhuige-block-head">
					<view>{{ icon.title }}</view>
					<text>{{ icon.subtitle }}</text>
				</view>
				<view class="zhuige-yp-icon">
					<view v-for="(item, index) in icon.items" :key="index" @click="clickLink(item.link)">
						<image mode="aspectFill" :src="item.logo"></image>
						<view>{{ item.title }}</view>
					</view>
				</view>
			</view>

			<!-- 分类标签 -->
			<view class="zhuige-yp-tab-box">
				<zhuige-tab :tabs="nav_cats" :curTab="cur_cat" type="scroll" :opt="true" @clickTab="clickCat"
					@clickTabOpt="clickTabOpt">
				</zhuige-tab>
			</view>

			<!-- 商家名片列表 -->
			<block v-if="list && list.length > 0">
				<view class="zhuige-sp-list-block sp-left" v-for="(item, index) in list" :key="index"
					@click="clickCard(item)">
					<view class="zhuige-sp-img">
						<image mode="aspectFill" :src="item.logo"></image>
					</view>
					<view class="zhuige-sp-text">
						<view class="zhuige-sp-title">
							<text class="tag" v-if="item.stick">推广</text>
							<text class="sptitle">{{ item.title }}</text>
						</view>
						<view class="zhuige-sp-info">
							<text>{{ item.excerpt }}</text>
						</view>
						<view class="zhuige-sp-opt">
							<view class="zhuige-sp-star">
								<uni-rate :value="item.score" size="12" active-color="#ff6146" readonly>
								</uni-rate>
							</view>
							<view class="zhuige-sp-tags">
								<text v-for="(tag, tagIndex) in item.tags" :key="tagIndex" @click.stop="clickTag(tag)">
									{{ tag.name }}
								</text>
							</view>
						</view>
					</view>
				</view>
				<uni-load-more :status="loadMore" v-if="list.length > 0"></uni-load-more>
			</block>
			<block v-else>
				<zhuige-nodata v-if="loaded"></zhuige-nodata>
			</block>
		</view>

		<!-- 底部菜单 -->
		<view class="zhuge-y_pages-tab" :class="'res-tab' + bottom_menu.length"
			v-if="bottom_menu && bottom_menu.length > 0">
			<view class="zhuge-y_pages-tab-icon" v-for="(item, index) in bottom_menu" :key="index"
				@click="clickLink(item.link)">
				<image :src="item.image"></image>
				<view>{{ item.title }}</view>
			</view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 商家名片首页
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeSwiper from "@/components/zhuige-swiper";
	import ZhuigeTab from "@/components/zhuige-tab";
	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeSwiper,
			ZhuigeTab,
			ZhuigeNodata
		},

		data() {
			return {
				logo: undefined,
				background: undefined,
				search: '',
				icon: undefined,
				slides: [],
				nav_cats: [],
				cur_cat: undefined,
				bottom_menu: [],
				share_img: undefined,
				list: [],
				loadMore: 'more',
				loaded: false,
				nav_opacity: 0
			};
		},

		onLoad(options) {
			Util.addShareScore(options.source);
			this.loadSetting();
		},

		onPageScroll(e) {
			this.nav_opacity = (e.scrollTop > 255 ? 255 : e.scrollTop) / 255;

			if (e.scrollTop > 20) {
				uni.setNavigationBarColor({
					frontColor: '#000000',
					backgroundColor: '#ffffff'
				});
			} else {
				uni.setNavigationBarColor({
					frontColor: '#ffffff',
					backgroundColor: '#ffffff'
				});
			}
		},

		onShareAppMessage() {
			let shareData = {
				title: '商家名片-' + getApp().globalData.appName,
				path: Util.addShareSource('pages/business-card/index/index?n=n')
			};

			if (this.share_img) {
				shareData.imageUrl = this.share_img;
			}

			return shareData;
		},

		onShareTimeline() {
			return {
				title: '商家名片-' + getApp().globalData.appName
			};
		},

		onReachBottom() {
			if (this.loadMore === 'more') {
				this.loadCard(false);
			}
		},

		onPullDownRefresh() {
			this.loadSetting();
		},

		methods: {
			clickBack() {
				Util.navigateBack();
			},

			confirmSearch() {
				this.dosearch();
			},

			clickSearch() {
				this.dosearch();
			},

			dosearch() {
				if (this.search) {
					Util.openLink(`/pages/business-card/list/list?title=搜索【${this.search}】&search=${this.search}`);
				} else {
					Alert.toast('请输入搜索关键词');
				}
			},

			clickCat(cat) {
				if (this.cur_cat !== cat.id) {
					this.cur_cat = cat.id;
					this.loadCard(true);
				}
			},

			clickTabOpt() {
				Util.openLink('/pages/business-card/classify/classify');
			},

			clickLink(link) {
				Util.openLink(link);
			},

			clickCard(item) {
				if (item.driect_link_switch === '1') {
					Util.openLink(item.driect_link);
				} else {
					Util.openLink(`/pages/business-card/detail/detail?id=${item.id}`);
				}
			},

			clickTag(tag) {
				Util.openLink(`/pages/business-card/list/list?title=标签【${tag.name}】&tag_id=${tag.id}`);
			},

			loadSetting() {
				Rest.post(Api.URL('bcard', 'setting'), {}).then(res => {
					if (res.data.logo) {
						this.logo = res.data.logo;
					}
					if (res.data.background) {
						this.background = res.data.background;
					}
					if (res.data.icon) {
						this.icon = res.data.icon;
					}

					this.slides = res.data.slides;
					this.nav_cats = res.data.nav_cats;

					if (this.nav_cats.length > 0) {
						this.cur_cat = this.nav_cats[0].id;
						this.loadCard(true);
					}

					this.bottom_menu = res.data.bottom_menu;

					if (res.data.share_img) {
						this.share_img = res.data.share_img;
					}

					uni.stopPullDownRefresh();
				}, err => {
					console.log(err);
				});
			},

			loadCard(refresh) {
				let url = Api.URL('bcard', 'last');
				if (this.cur_cat) {
					url = Api.URL('bcard', 'cat');
				}

				Rest.post(url, {
					cat_id: this.cur_cat,
					offset: refresh ? 0 : this.list.length
				}).then(res => {
					this.list = refresh ? res.data.list : this.list.concat(res.data.list);
					this.loadMore = res.data.more;
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	.content {
		background: #f5f5f5;
	}

	.zhuige-yp-box {
		padding: 180rpx 30rpx;
	}

	.zhuige-yp-logo {
		display: flex;
		justify-content: center;
	}

	.zhuige-yp-logo image {
		height: 256rpx;
		width: 256rpx;
	}

	.zhuige-yp-search {
		border: 2rpx solid #ff6146;
		border-radius: 40rpx;
		justify-content: space-between;
		margin-bottom: 20rpx;
		width: 100%;
	}

	.zhuige-yp-search,
	.zhuige-yp-search view:nth-child(1) {
		align-items: center;
		display: flex;
		height: 76rpx;
	}

	.zhuige-yp-search view:nth-child(1) {
		padding-left: 20rpx;
	}

	.zhuige-yp-search view:nth-child(1) input {
		background: transparent;
		color: #333;
		font-size: 28rpx;
		font-weight: 300;
		height: 74rpx;
		line-height: 74rpx;
		margin-left: 12rpx;
		width: 400rpx;
	}

	.zhuige-yp-search view:nth-child(2) {
		background: #ff6146;
		border-radius: 0 36rpx 36rpx 0;
		color: #fff;
		font-size: 28rpx;
		font-weight: 400;
		height: 76rpx;
		line-height: 76rpx;
		text-align: center;
		width: 140rpx;
	}

	.zhuige-yp-icon {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
	}

	.zhuige-yp-icon>view {
		margin-right: 3%;
		width: 22.75%;
	}

	.zhuige-yp-icon>view:nth-child(4n) {
		margin-right: 0;
	}

	.zhuige-yp-icon>view image {
		border-radius: 6rpx;
		height: 152rpx;
		width: 100%;
	}

	.zhuige-yp-icon>view view {
		color: #666;
		font-size: 26rpx;
		font-weight: 400;
		text-align: center;
	}

	.zhuige-sp-list-block {
		align-items: center;
		background: #fff;
		border-radius: 12rpx;
		display: flex;
		margin-bottom: 20rpx;
		padding: 24rpx;
	}

	.zhuige-sp-list-block:last-of-type {
		border: none;
	}

	.zhuige-sp-title {
		align-items: center;
		display: flex;
		flex-wrap: nowrap;
		height: 2em;
		line-height: 1.4em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-sp-title text {
		font-size: 32rpx;
		font-weight: 500;
	}

	.zhuige-sp-title text.tag {
		background: #ff6146;
		border-radius: 4rpx;
		color: #fff;
		font-size: 20rpx;
		font-weight: 400;
		line-height: 1rem;
		margin-right: 8rpx;
		padding: 1rpx 8rpx;
	}

	.zhuige-sp-title text.sptitle {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 100%;
	}

	.zhuige-sp-info {
		color: #666;
		font-weight: 400;
		height: 2em;
		line-height: 2em;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.zhuige-sp-tags {
		align-items: center;
		display: flex;
		overflow: hidden;
	}

	.zhuige-sp-tags text {
		background: #f5f5f5;
		border-radius: 6rpx;
		font-size: 20rpx;
		font-weight: 300;
		height: 36rpx;
		line-height: 36rpx;
		margin-left: 8rpx;
		padding: 0 12rpx;
		white-space: nowrap;
	}

	.sp-left {
		flex: 0 0 180rpx;
	}

	.sp-left .zhuige-sp-img {
		height: 180rpx;
		position: relative;
		width: 180rpx;
	}

	.sp-left .zhuige-sp-img image {
		border-radius: 6rpx;
		height: 180rpx;
		width: 180rpx;
	}

	.sp-left .zhuige-sp-text {
		overflow: hidden;
		padding-left: 20rpx;
		width: 100%;
	}

	.zhuige-sp-opt {
		justify-content: space-between;
		padding-top: 10rpx;
	}

	.zhuge-y_pages-tab,
	.zhuige-sp-opt,
	.zhuige-sp-star {
		align-items: center;
		display: flex;
	}

	.zhuge-y_pages-tab {
		backdrop-filter: blur(5px);
		background: hsla(0, 0%, 100%, .6);
		border-radius: 128rpx;
		bottom: 60rpx;
		box-shadow: 0 20rpx 42rpx 8rpx rgba(79, 125, 183, .3);
		flex-wrap: nowrap;
		height: 128rpx;
		justify-content: center;
		position: fixed;
		z-index: 999;
	}

	.zhuge-y_pages-tab-icon {
		text-align: center;
		width: 20%;
	}

	.res-tab1 {
		margin: 0 35%;
		padding: 0 4%;
		width: 22%;
	}

	.res-tab2 {
		margin: 0 26%;
		padding: 0 4%;
		width: 40%;
	}

	.res-tab3 {
		margin: 0 16%;
		padding: 0 4%;
		width: 60%;
	}

	.res-tab4 {
		margin: 0 9%;
		padding: 0 4%;
		width: 74%;
	}

	.res-tab5 {
		margin: 0 3%;
		padding: 0 4%;
		width: 86%;
	}

	.res-tab1 .zhuge-y_pages-tab-icon {
		width: 100%;
	}

	.res-tab2 .zhuge-y_pages-tab-icon {
		width: 50%;
	}

	.res-tab3 .zhuge-y_pages-tab-icon {
		width: 33.3%;
	}

	.res-tab4 .zhuge-y_pages-tab-icon {
		width: 25%;
	}

	.res-tab5 .zhuge-y_pages-tab-icon {
		width: 20%;
	}

	.zhuge-y_pages-tab-icon image {
		height: 64rpx;
		margin: -8rpx auto 0;
		width: 64rpx;
	}

	.zhuge-y_pages-tab-icon view {
		color: #666;
		font-size: 24rpx;
		font-weight: 400;
		height: 1em;
		line-height: 1em;
		margin-top: -16rpx;
	}

	.zhuige-yp-swiper {
		margin-bottom: 20rpx;
	}

	.zhuige-yp-swiper .zhuige-swiper {
		height: 300rpx !important;
	}

	.zhuige-yp-tab-box {
		margin-bottom: 20rpx;
	}

	.zhuige-block-head text {
		color: #666;
		font-size: 26rpx;
		font-weight: 400;
	}

	.zhuige-tab-box:first-of-type {
		padding-left: 16rpx !important;
	}
</style>