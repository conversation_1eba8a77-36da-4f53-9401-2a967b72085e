<template>
	<view class="content">
		<!-- 商家名片列表 -->
		<view class="zhuige-sp-news-list">
			<block v-if="cards && cards.length > 0">
				<view class="zhuige-sp-list-block sp-left" v-for="(card, index) in cards" :key="index"
					@click="clickCard(card)">
					<view class="zhuige-sp-img">
						<image mode="aspectFill" :src="card.logo"></image>
					</view>
					<view class="zhuige-sp-text">
						<view class="zhuige-sp-title">
							<text class="tag" v-if="card.stick">推广</text>
							<text class="sptitle">{{ card.title }}</text>
						</view>
						<view class="zhuige-sp-info">
							<text>{{ card.excerpt }}</text>
						</view>
						<view class="zhuige-sp-opt">
							<view class="zhuige-sp-star">
								<uni-rate :value="card.score" size="12" active-color="#ff6146" readonly>
								</uni-rate>
							</view>
							<view class="zhuige-sp-tags">
								<text v-for="(tag, tagIndex) in card.tags" :key="tagIndex">
									{{ tag.name }}
								</text>
							</view>
						</view>
					</view>
				</view>
				<uni-load-more :status="loadMore"></uni-load-more>
			</block>
			<block v-else>
				<zhuige-nodata v-if="loaded"></zhuige-nodata>
			</block>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 商家名片列表页
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeNodata
		},

		data() {
			return {
				title: '',
				cat_id: undefined,
				tag_id: undefined,
				search: undefined,
				cards: [],
				loadMore: 'more',
				loaded: false
			};
		},

		onLoad(options) {
			Util.addShareScore(options.source);

			if (options.title) {
				this.title = options.title;
				uni.setNavigationBarTitle({
					title: decodeURIComponent(options.title)
				});
			}

			if (options.cat_id) {
				this.cat_id = options.cat_id;
			} else if (options.tag_id) {
				this.tag_id = options.tag_id;
			} else if (options.search) {
				this.search = options.search;
			}

			this.loadCards(true);
		},

		onShareAppMessage() {
			let path = `pages/business-card/list/list?title=${this.title}`;

			if (this.cat_id) {
				path += `&cat_id=${this.cat_id}`;
			} else if (this.tag_id) {
				path += `&tag_id=${this.tag_id}`;
			} else if (this.search !== undefined) {
				path += `&search=${this.search}`;
			}

			return {
				title: this.title + '-' + getApp().globalData.appName,
				path: Util.addShareSource(path)
			};
		},

		onShareTimeline() {
			return {
				title: this.title + '-' + getApp().globalData.appName
			};
		},

		onReachBottom() {
			if (this.loadMore === 'more') {
				this.loadCards(false);
			}
		},

		methods: {
			clickCard(item) {
				if (item.driect_link_switch === '1') {
					Util.openLink(item.driect_link);
				} else {
					Util.openLink(`/pages/business-card/detail/detail?id=${item.id}`);
				}
			},

			loadCards(refresh) {
				if (this.loadMore === 'loading') return;

				this.loadMore = 'loading';

				let url = '';
				const data = {
					offset: this.cards.length
				};

				if (this.cat_id !== undefined) {
					url = Api.URL('bcard', 'cat');
					data.cat_id = this.cat_id;
				} else if (this.tag_id !== undefined) {
					url = Api.URL('bcard', 'tag');
					data.tag_id = this.tag_id;
				} else if (this.search !== undefined) {
					url = Api.URL('bcard', 'search');
					data.search = this.search;
				} else {
					url = Api.URL('bcard', 'last');
				}

				Rest.post(url, data).then(res => {
					this.cards = refresh ? res.data.list : this.cards.concat(res.data.list);
					this.loadMore = res.data.more;
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	.content {
		background: #f5f5f5;
		height: 100%;
		overflow-y: scroll;
		position: fixed;
		width: 100%;
	}

	.zhuige-sp-news-list {
		padding: 0 30rpx;
	}

	.zhuige-sp-list-block {
		align-items: center;
		background: #fff;
		border-radius: 12rpx;
		display: flex;
		margin-bottom: 30rpx;
		padding: 24rpx;
	}

	.zhuige-sp-list-block:last-of-type {
		border: none;
	}

	.zhuige-sp-title {
		align-items: center;
		display: flex;
		flex-wrap: nowrap;
		height: 2em;
		line-height: 1.4em;
		margin-bottom: 10rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-sp-title text.tag {
		background: #ff6146;
		border-radius: 4rpx;
		color: #fff;
		font-size: 22rpx;
		font-weight: 300;
		line-height: 1rem;
		margin-right: 8rpx;
		padding: 1rpx 8rpx;
	}

	.zhuige-sp-title text.sptitle {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		width: 100%;
	}

	.zhuige-sp-info {
		color: #666;
		font-weight: 400;
		height: 1.8em;
		line-height: 1.8em;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.zhuige-sp-tags {
		align-items: center;
		display: flex;
		overflow: hidden;
	}

	.zhuige-sp-tags text {
		background: #f5f5f5;
		border-radius: 6rpx;
		font-size: 20rpx;
		font-weight: 300;
		height: 36rpx;
		line-height: 36rpx;
		margin-left: 8rpx;
		padding: 0 12rpx;
		white-space: nowrap;
	}

	.sp-left {
		flex: 0 0 180rpx;
	}

	.sp-left .zhuige-sp-img {
		height: 180rpx;
		position: relative;
		width: 180rpx;
	}

	.sp-left .zhuige-sp-img image {
		border-radius: 6rpx;
		height: 180rpx;
		width: 180rpx;
	}

	.sp-left .zhuige-sp-text {
		overflow: hidden;
		padding-left: 20rpx;
		width: 100%;
	}

	.zhuige-sp-opt {
		justify-content: space-between;
		padding-top: 10rpx;
	}

	.zhuige-sp-opt,
	.zhuige-sp-star {
		align-items: center;
		display: flex;
	}
</style>