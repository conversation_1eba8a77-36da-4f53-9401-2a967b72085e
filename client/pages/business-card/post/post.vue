<template>
	<view class="content">
		<!-- 基础信息标题 -->
		<view class="zhuige-post-header">
			<view>基础信息</view>
			<text>（越详细越有机会被推荐哦）</text>
		</view>

		<!-- LOGO上传 -->
		<view class="zhuige-post-box">
			<view class="zhuige-upload-set">
				<view :class="{ 'loaded': logo }" @click="clickLogo">
					<template v-if="logo">
						<uni-icons type="clear" size="24" color="#FD6531" @click.stop="clickClearLogo"></uni-icons>
						<image :src="logo.url" mode="aspectFill"></image>
					</template>
					<template v-else>
						<uni-icons type="plusempty" size="30" color="#777777"></uni-icons>
						<view>图片</view>
					</template>
				</view>
			</view>
		</view>

		<!-- 产品名称 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-title-line">
					<input v-model="title" placeholder="请输入产品/店铺/服务名称" type="text" />
				</view>
			</view>
		</view>

		<!-- 详细介绍 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-input">
					<textarea v-model="content" placeholder="详细介绍有利于被推荐哦…" maxlength="140">
					</textarea>
				</view>
			</view>
		</view>

		<!-- 图片上传 -->
		<view class="zhuige-post-box">
			<view class="zhuige-upload-set">
				<view @click="clickImage" v-if="images.length < 9">
					<uni-icons type="plusempty" size="30" color="#777777"></uni-icons>
					<view>图片</view>
				</view>
				<view class="loaded" v-for="(image, index) in images" :key="index">
					<uni-icons type="clear" size="24" color="#FD6531" @click="clickDelImage(index)"></uni-icons>
					<image :src="image.image.url" mode="aspectFill"></image>
				</view>
			</view>
		</view>

		<!-- 选择分类 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-check">
					<view>选择分类：</view>
					<view>
						<picker :range="cat_names" :value="cur_cat" @change="onCatChange">
							<view>
								<view class="picker">{{ cats && cats.length > 0 ? cats[cur_cat].name : '' }}</view>
								<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>

		<!-- 公司信息标题 -->
		<view class="zhuige-post-header">
			<view>公司信息</view>
			<text>（便于生成名片信息哦，也方便用户了解你的业务）</text>
		</view>

		<!-- 联系人 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line">
					<view>联系人：</view>
					<view>
						<input v-model="contact" placeholder="请输入联系人" type="text" />
					</view>
				</view>
			</view>
		</view>

		<!-- 联系电话 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line">
					<view>联系电话：</view>
					<view>
						<input v-model="phone" placeholder="请输入电话" type="tel" />
					</view>
				</view>
			</view>
		</view>

		<!-- 地址 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line">
					<view>地址：</view>
					<view @click="clickAddress">
						<view>{{ marker || '请选择位置' }}</view>
						<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 职位 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line">
					<view>职位：</view>
					<view>
						<input v-model="position" placeholder="请输入职位" type="text" />
					</view>
				</view>
			</view>
		</view>

		<!-- 网址 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line">
					<view>网址：</view>
					<view>
						<input v-model="web" placeholder="格式如 www.zhuige.com" type="url" />
					</view>
				</view>
			</view>
		</view>

		<!-- 公司名称 -->
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line">
					<view>公司名称：</view>
					<view>
						<input v-model="company" placeholder="请输入公司名称" type="text" />
					</view>
				</view>
			</view>
		</view>

		<!-- 微信二维码 -->
		<view class="zhuige-post-box">
			<view class="zhuige-upload-set">
				<view :class="{ 'loaded': qrcode }" @click="clickQrcode">
					<template v-if="qrcode">
						<uni-icons type="clear" size="24" color="#FD6531" @click.stop="clickClearQrcode"></uni-icons>
						<image :src="qrcode.url" mode="aspectFill"></image>
					</template>
					<template v-else>
						<uni-icons type="plusempty" size="30" color="#777777"></uni-icons>
						<view>微信二维码</view>
					</template>
				</view>
			</view>
		</view>

		<!-- 发布协议 -->
		<view class="zhuige-creat-opt" v-if="!card_id && fbxy">
			<label @click="clickCheckFbxy">
				<radio :checked="checkFbxy"></radio>我已阅读并同意<text @click.stop="clickFbxy">《发布协议》</text>
			</label>
		</view>

		<!-- 发布按钮 -->
		<view class="zhuige-base-button" @click="clickOK">
			<view>确定</view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序 - 商家名片发布页
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	export default {
		data() {
			return {
				card_id: undefined,
				cats: [],
				cat_names: [],
				cur_cat: 0,
				logo: undefined,
				title: '',
				content: '',
				images: [],
				contact: '',
				phone: '',
				longitude: '',
				latitude: '',
				marker: '',
				address: '',
				position: '',
				web: '',
				company: '',
				qrcode: undefined,
				checkFbxy: false,
				fbxy: undefined,
				requesting: false
			};
		},

		onLoad(options) {
			Util.addShareScore(options.source);

			if (options.card_id) {
				this.card_id = options.card_id;
				uni.setNavigationBarTitle({
					title: '编辑名片'
				});
			}

			this.preCreate();
		},

		onShareAppMessage() {
			return {
				title: this.shareTitle(),
				path: Util.addShareSource('pages/business-card/post/post?n=n')
			};
		},

		onShareTimeline() {
			return {
				title: this.shareTitle()
			};
		},

		methods: {
			shareTitle() {
				return (this.card_id ? '编辑名片' : '发布名片') + getApp().globalData.appName;
			},

			clickLogo() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						Rest.upload(Api.URL('other', 'upload2'), tempFilePath).then(res => {
							if (res.code === 0) {
								this.logo = res.data;
							} else {
								Alert.error(res.message);
							}
						}, err => {
							Alert.error(err);
						});
					},
					fail: (err) => {
						if (err.errMsg && err.errMsg.indexOf('cancel') < 0) {
							Alert.error(err.errMsg);
						}
					}
				});
			},

			clickClearLogo() {
				this.logo = undefined;
			},

			clickImage() {
				uni.chooseImage({
					count: 9 - this.images.length,
					sizeType: ['compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						Rest.upload(Api.URL('other', 'upload2'), tempFilePath).then(res => {
							if (res.code === 0) {
								this.images.push({
									image: res.data
								});
							} else {
								Alert.error(res.message);
							}
						}, err => {
							Alert.error(err);
						});
					},
					fail: (err) => {
						if (err.errMsg && err.errMsg.indexOf('cancel') < 0) {
							Alert.error(err.errMsg);
						}
					}
				});
			},

			clickDelImage(index) {
				if (index >= 0 && index < this.images.length) {
					this.images.splice(index, 1);
				}
			},

			clickQrcode() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					success: (res) => {
						const tempFilePath = res.tempFilePaths[0];
						Rest.upload(Api.URL('other', 'upload2'), tempFilePath).then(res => {
							if (res.code === 0) {
								this.qrcode = res.data;
							} else {
								Alert.error(res.message);
							}
						}, err => {
							Alert.error(err);
						});
					},
					fail: (err) => {
						if (err.errMsg && err.errMsg.indexOf('cancel') < 0) {
							Alert.error(err.errMsg);
						}
					}
				});
			},

			clickClearQrcode() {
				this.qrcode = undefined;
			},

			clickAddress() {
				const options = {
					success: (res) => {
						this.marker = res.name;
						this.address = res.address;
						this.longitude = res.longitude;
						this.latitude = res.latitude;
					},
					fail: (err) => {
						console.log(err);
					}
				};

				if (this.latitude) {
					options.latitude = this.latitude;
				}
				if (this.longitude) {
					options.longitude = this.longitude;
				}

				uni.chooseLocation(options);
			},

			onCatChange(e) {
				this.cur_cat = parseInt(e.detail.value);
			},

			preCreate() {
				Rest.post(Api.URL('bcard', 'create_pre'), {
					card_id: this.card_id
				}).then(res => {
					if (res.code === 0) {
						this.cats = res.data.cats;

						if (res.data.fbxy) {
							this.fbxy = res.data.fbxy;
						}

						// 构建分类名称数组
						for (let i = 0; i < this.cats.length; i++) {
							this.cat_names.push(this.cats[i].name);
						}

						// 如果是编辑模式，填充数据
						if (res.data.card) {
							const card = res.data.card;

							// 设置分类
							if (card.cat_id) {
								for (let i = 0; i < this.cats.length; i++) {
									if (card.cat_id === this.cats[i].id) {
										this.cur_cat = i;
										break;
									}
								}
							}

							// 填充其他数据
							if (card.logo) this.logo = card.logo;
							if (card.title) this.title = card.title;
							if (card.content) this.content = card.content;
							if (card.images) this.images = card.images;
							if (card.contact) this.contact = card.contact;
							if (card.phone) this.phone = card.phone;
							if (card.longitude) this.longitude = card.longitude;
							if (card.latitude) this.latitude = card.latitude;
							if (card.marker) this.marker = card.marker;
							if (card.address) this.address = card.address;
							if (card.position) this.position = card.position;
							if (card.web) this.web = card.web;
							if (card.company) this.company = card.company;
							if (card.qrcode) this.qrcode = card.qrcode;
						}
					} else if (res.code === 'require_mobile') {
						uni.redirectTo({
							url: '/pages/user/login/login?type=mobile&tip=发商家名片'
						});
					} else if (res.code === 'require_avatar') {
						uni.redirectTo({
							url: '/pages/user/verify/verify?tip=发商家名片'
						});
					} else {
						Alert.error(res.message);
						setTimeout(() => {
							Util.navigateBack();
						}, 1500);
					}
				}, err => {
					console.log(err);
				});
			},

			clickCheckFbxy() {
				this.checkFbxy = !this.checkFbxy;
			},

			clickFbxy() {
				Util.openLink(this.fbxy);
			},

			clickOK() {
				// 检查发布协议
				if (!this.card_id && this.fbxy && !this.checkFbxy) {
					Alert.toast('请阅读并同意发布协议');
					return;
				}

				// 检查必填项
				if (!this.logo) {
					Alert.toast('请设置LOGO');
					return;
				}

				if (this.requesting) return;

				// 处理内容换行
				const content = this.content.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ');

				const data = {
					logo: JSON.stringify(this.logo),
					title: this.title,
					content: Util.htmlEncode(content),
					images: JSON.stringify(this.images),
					contact: this.contact,
					phone: this.phone,
					latitude: this.latitude,
					longitude: this.longitude,
					marker: this.marker,
					address: this.address,
					position: this.position,
					web: this.web,
					company: this.company,
					qrcode: JSON.stringify(this.qrcode),
					cat_id: this.cats[this.cur_cat].id
				};

				if (this.card_id) {
					data.card_id = this.card_id;
				}

				this.requesting = true;

				Rest.post(Api.URL('bcard', 'create'), data).then(res => {
					this.requesting = false;

					if (res.code === 0) {
						Alert.toast('审核后，他人可见');
						setTimeout(() => {
							Util.navigateBack();
						}, 1500);
					} else if (res.code === 'require_mobile') {
						Util.openLink('/pages/user/login/login?type=mobile&tip=发商家名片');
					} else if (res.code === 'require_avatar') {
						Util.openLink('/pages/user/verify/verify?tip=发商家名片');
					} else {
						Alert.error(res.message);
					}
				}, err => {
					this.requesting = false;
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	.content {
		height: 100%;
		overflow-y: scroll;
		position: fixed;
		width: 100%;
	}

	.zhuige-post-box {
		padding: 0 20rpx;
	}

	.zhuige-post-video-box {
		display: flex;
		flex-wrap: wrap;
		padding: 0 20rpx;
	}

	.zhuige-post-video-box video {
		margin-bottom: 20rpx;
		width: 100%;
	}

	.content .zhuige-post-box:nth-last-child(2) {
		margin-bottom: 180rpx;
	}

	.zhuige-post-line {
		align-items: center;
		display: flex;
		height: 2.2em;
		line-height: 2.2em;
	}

	.zhuige-post-line>view:nth-child(1) {
		font-size: 30rpx;
		font-weight: 400;
		width: 156rpx;
	}

	.zhuige-post-line>view:nth-child(2) {
		align-items: center;
		display: flex;
		justify-content: space-between;
		width: 500rpx;
	}

	.zhuige-post-line view:nth-child(2) view {
		color: #777;
		font-size: 28rpx;
		font-weight: 400;
		height: 1.6rem;
		line-height: 1.6rem;
		margin-right: 12rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-post-line view:nth-child(2) input {
		width: 480rpx;
	}

	.zhuige-upload-set {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
	}

	.zhuige-upload-set>view {
		background: #fff;
		border-radius: 12rpx;
		height: 154rpx;
		margin: 0 20rpx 20rpx 0;
		position: relative;
		text-align: center;
		width: 154rpx;
	}

	.zhuige-upload-set>view view {
		color: #777;
		font-size: 28rpx;
		font-weight: 400;
		height: 1em;
		line-height: 1em;
		margin-top: -28rpx;
	}

	.zhuige-upload-set view.loaded image {
		border-radius: 12rpx;
		height: 100%;
		width: 100%;
	}

	.zhuige-upload-set>view.loaded uni-icons {
		position: absolute;
		right: -20rpx;
		top: -46rpx;
		z-index: 3;
	}

	.zhuige-post-input textarea {
		height: 240rpx;
		line-height: normal;
		padding: 20rpx 0;
	}

	.zhuige-post-plug {
		align-items: center;
		justify-content: space-between;
	}

	.zhuige-post-opt,
	.zhuige-post-opt view,
	.zhuige-post-plug {
		display: flex;
		flex-wrap: nowrap;
	}

	.zhuige-post-opt view {
		align-items: center;
		line-height: 1.8em;
		margin-right: 8rpx;
		opacity: .5;
	}

	.zhuige-post-plug view.active {
		opacity: 1;
	}

	.zhuige-post-plug view image {
		height: 28rpx;
		margin-right: 8rpx;
		width: 28rpx;
	}

	.zhuige-post-plug view text {
		font-size: 28rpx;
		font-weight: 300;
		margin-left: 8rpx;
		white-space: nowrap;
	}

	.zhuige-post-count {
		color: #999;
	}

	.zhuige-post-title-line,
	.zhuige-post-title-line input {
		width: 100%;
	}

	.zhuige-creat-opt {
		align-items: center;
		display: flex;
		justify-content: center;
		padding: 30rpx 30rpx 160rpx;
	}

	.zhuige-creat-tips {
		font-size: 26rpx;
		padding: 25rpx;
		text-align: center;
	}

	.zhuige-creat-tips text {
		color: #1d9ffc;
		padding: 10rpx;
	}

	.zhuige-post-header {
		align-items: center;
		display: flex;
		padding: 0 20rpx 24rpx;
	}

	.zhuige-post-header view {
		font-size: 32rpx;
		font-weight: 500;
	}

	.zhuige-post-header text {
		font-size: 24rpx;
		font-weight: 300;
	}

	.line-check>view:nth-child(2) {
		justify-content: space-between;
	}

	.line-check view:nth-child(2) {
		width: 80%;
	}
</style>