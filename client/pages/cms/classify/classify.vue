<template>
	<view class="content">
		<view class="zhuige-classify">
			<!-- 左侧分类导航 -->
			<scroll-view class="zhuige-classify-key" :scroll-with-animation="true" :scroll-y="true">
				<view v-for="(cat, index) in cats" :key="index" :class="cur_cat === cat.id ? 'active' : ''"
					@click="clickCatNav(cat.id)">
					<text>{{cat.name}}</text>
				</view>
			</scroll-view>

			<!-- 右侧内容区域 -->
			<scroll-view class="zhuige-classify-side" scroll-top="scrollTop" :scroll-with-animation="true"
				:scroll-y="true">
				<view class="zhuige-classify-cms-list" v-for="(scat, sinx) in cats" :key="sinx"
					v-if="scat.id === cur_cat">
					<block v-if="scat.cats && scat.cats.length > 0">
						<view class="zhuige-classify-cms-block" v-for="(cat, index) in scat.cats" :key="index"
							@click="clickCat(cat)">
							<image mode="aspectFill" :src="cat.cover"></image>
							<view class="zhuige-classify-cms-text">
								<view>
									<text>{{cat.name}}</text>
									<text>{{'总计' + cat.count + '篇'}}</text>
								</view>
								<view>{{cat.description}}</view>
							</view>
						</view>
					</block>
					<zhuige-nodata v-else></zhuige-nodata>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeNodata
		},
		data() {
			return {
				cats: [],
				cur_cat: undefined
			};
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			this.loadCategories(0);
		},
		methods: {
			clickCatNav(catId) {
				this.cur_cat = catId;
			},
			clickCat(cat) {
				Util.openLink('/pages/cms/list/list?cat_id=' + cat.id + '&title=' + cat.name);
			},
			loadCategories(parent) {
				Rest.post(Api.URL('cms', 'cats'), {
					parent: parent
				}).then(res => {
					this.cats = res.data.cats;
					if (this.cats && this.cats.length > 0) {
						this.cur_cat = this.cats[0].id;
					}
				});
			}
		}
	};
</script>

<style lang="scss">
	.content {
		height: 100%;
		position: fixed;
		width: 100%;
	}

	.zhuige-classify {
		display: flex;
		height: 100%;
		overflow-y: scroll;
		width: 100%;
	}

	.zhuige-classify-key {
		background: #fff;
		border-radius: 0 12rpx 12rpx 0;
		width: 30%;
	}

	.zhuige-classify-key::-webkit-scrollbar {
		display: none;
	}

	.zhuige-classify-key view {
		align-items: center;
		display: flex;
		font-size: 28rpx;
		font-weight: 400;
		height: 120rpx;
		justify-content: left;
		padding-left: 40rpx;
	}

	.zhuige-classify-key view.active {
		background: #f5f5f5;
	}

	.zhuige-classify-key view text {
		color: #555;
		font-size: 28rpx;
	}

	.zhuige-classify-key view.active text {
		color: #010101;
		font-size: 32rpx;
		font-weight: 600;
	}

	.zhuige-classify-side {
		background: #f5f5f5;
		height: 100%;
		overflow-y: scroll;
		width: 70%;
	}

	.zhuige-classify-cms-list {
		padding: 0 20rpx;
	}

	.zhuige-classify-cms-block {
		margin-bottom: 20rpx;
	}

	.zhuige-classify-cms-block image {
		border-radius: 12rpx 12rpx 0 0;
		display: inherit;
		height: 200rpx;
		width: 100%;
	}

	.zhuige-classify-cms-text {
		background: #fff;
		border-radius: 0 0 12rpx 12rpx;
		padding: 20rpx;
	}

	.zhuige-classify-cms-text view:nth-child(1) {
		align-items: center;
		display: flex;
	}

	.zhuige-classify-cms-text view:nth-child(1) text:nth-child(1) {
		font-size: 30rpx;
		font-weight: 600;
	}

	.zhuige-classify-cms-text view:nth-child(1) text:nth-child(2) {
		background: #ffeae3;
		border-radius: 24rpx;
		color: #ff6348;
		font-size: 24rpx;
		font-weight: 300;
		height: 40rpx;
		line-height: 40rpx;
		margin-left: 12rpx;
		padding: 0 24rpx;
	}

	.zhuige-classify-cms-text view:nth-child(2) {
		color: #555;
		font-size: 26rpx;
		font-weight: 400;
		height: 1.6rem;
		line-height: 1.6rem;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>