<template>
	<view class="content">
		<view class="zhuige-detail-block" v-if="post">
			<view class="zhuige-block">
				<!-- 作者信息 -->
				<view class="zhuige-social-poster-blcok" v-if="post.author">
					<view class="zhuige-social-poster" @click="clickUser(post.author.user_id)">
						<view class="zhuige-social-poster-avatar">
							<image mode="aspectFill" :src="post.author.avatar"></image>
						</view>
						<view class="zhuige-social-poster-info">
							<view>
								<text>{{post.author.nickname}}</text>
								<image v-if="post.author.certify && post.author.certify.status == 1" mode="aspectFill"
									:src="post.author.certify.icon"></image>
								<image v-if="post.author.vip && post.author.vip.status == 1" class="zhuige-social-vip"
									mode="aspectFill" :src="post.author.vip.icon"></image>
							</view>
						</view>
					</view>
					<view class="zhuige-social-opt" @click="clickFollowUser(post.author.user_id)">
						<view v-if="post.author.is_follow">已关注</view>
						<view v-else>+ 关注</view>
					</view>
				</view>

				<!-- 上方广告 -->
				<view class="zhuige-wide-image-ad" v-if="ad_up" @click="clickLink(ad_up.link)">
					<view>
						<image mode="aspectFill" :src="ad_up.image"></image>
					</view>
				</view>

				<!-- 文章标题 -->
				<view class="zhuige-detail-title">{{post.title}}</view>

				<!-- 文章数据 -->
				<view class="zhuige-detail-data">
					<text>{{post.time}}</text>
					<text>{{'浏览 ' + post.views}}</text>
				</view>

				<!-- 文章内容 -->
				<view class="zhuige-detail-cont">
					<mp-html v-if="post.read_limit == 'jiliv' && jili_content" :content="jili_content"></mp-html>
					<mp-html v-else :content="post.content"></mp-html>
				</view>

				<!-- 付费内容按钮 -->
				<view class="zhuige-detail-button" v-if="post.read_limit == 'cost'" @click="clickCost('post')">
					<view v-if="is_ios && post.cost_ios_switch != 1">规范原因，iOS功能暂不可用</view>
					<view v-else>{{'支付￥' + post.cost_price + '查看剩余内容'}}</view>
				</view>

				<!-- 积分兑换按钮 -->
				<view class="zhuige-detail-button" v-if="post.read_limit == 'score'" @click="clickCostScore('post')">
					<view>{{'' + post.cost_score + '积分兑换查看剩余内容'}}</view>
				</view>

				<!-- 其他限制 -->
				<block v-else>
					<!-- 认证限制 -->
					<block v-if="post.read_limit == 'certify'">
						<view class="zhuige-detail-button" v-if="post.read_limit_switch == 1"
							@click="clickLink('/pages/user/certify/certify')">
							<view>认证用户可阅读全文</view>
							<text>{{post.read_limit_tip}}</text>
						</view>
					</block>

					<!-- 其他限制 -->
					<block v-else>
						<!-- 激励视频限制 -->
						<block v-if="post.read_limit == 'jiliv'">
							<view class="zhuige-detail-button" v-if="jili_content.length == 0"
								@click="clickRewardVideo">
								<view>观看完整视频，阅读更多</view>
							</view>
						</block>

						<!-- 点赞限制 -->
						<block v-else>
							<block v-if="post.read_limit == 'like'">
								<view class="zhuige-detail-button" v-if="!post.user.is_like">
									<view>底部点赞，阅读全文</view>
								</view>
							</block>
						</block>
					</block>
				</block>

				<!-- 标签 -->
				<view class="zhuige-detail-tags">
					<text v-for="(tag, index) in post.tags" :key="index"
						@click="clickTag(tag.id, tag.name)">{{tag.name}}</text>
				</view>

				<!-- 版权信息 -->
				<view class="zhuige-detail-copy" v-if="copyright">{{'' + copyright + ''}}</view>
			</view>
		</view>

		<!-- 点赞分享区域 -->
		<view class="zhuige-detail-share" v-if="post">
			<view class="zhuige-block">
				<view class="zhuige-detail-share-user">
					<view class="zhuige-detail-share-title">{{'-' + post.like_list.length + '人点赞 -'}}</view>
					<view class="zhuige-detail-share-user-list" v-if="post.like_list.length > 0">
						<view v-for="(user, index) in post.like_list" :key="index" @click="clickUser(user.user_id)">
							<image mode="aspectFill" :src="user.avatar"></image>
						</view>
					</view>
				</view>
				<view class="zhuige-detail-share-opt">
					<view @click="clickPoster">
						<text>分享海报</text>
						<image mode="aspectFill" src="@/static/poster.png"></image>
					</view>
					<view v-if="post && post.author && post.author.reward" @click="clickReword">
						<text>鼓励作者</text>
						<image mode="aspectFill" src="@/static/packet.png"></image>
					</view>
				</view>
			</view>
		</view>

		<!-- 关注公众号 -->
		<official-account v-if="official_switch == '1'"></official-account>

		<!-- 流量广告 -->
		<view class="zhuige-traffic-ad" v-if="wx_ad && wx_ad.banner">
			<view class="zhuige-block">
				<ad-custom :unit-id="wx_ad.banner"></ad-custom>
			</view>
		</view>

		<!-- 相关推荐 -->
		<view class="zhuige-detail-cms-recom" v-if="post && post.recs">
			<view class="zhuige-block">
				<view class="zhuige-block-head">
					<view>相关推荐</view>
				</view>
				<view v-for="item in post.recs" :key="item.id" class="zhugie-info-block left-side"
					@click="clickDetail(item)">
					<view class="zhugie-info-image">
						<image mode="aspectFill" :src="item.thumbnail"></image>
					</view>
					<view class="zhugie-info-text">
						<view class="zhugie-info-title">{{item.title}}</view>
						<view class="zhuige-info-post">
							<view class="zhuige-info-data">
								<text>{{'浏览 ' + item.views}}</text>
								<text>{{'点赞 ' + item.likes}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 下方广告 -->
		<view class="zhuige-wide-image-ad" v-if="ad_down" @click="clickLink(ad_down.link)">
			<view>
				<image mode="aspectFill" :src="ad_down.image"></image>
			</view>
		</view>

		<!-- 评论列表 -->
		<view class="zhuige-reply-list">
			<view class="zhuige-block">
				<view class="zhuige-block-head">
					<view>近期回复</view>
					<view @click="clickAllComments">查看全部</view>
				</view>
				<block v-if="comments.length > 0">
					<zhuige-reply v-for="(comment, index) in comments" :key="index" :item="comment"
						@clickReply="clickReply"></zhuige-reply>
				</block>
				<block v-else>
					<view class="zhuige-none-tips" v-if="loaded">
						<image mode="aspectFill" src="@/static/404.png"></image>
						<view>暂无评论，抢个沙发</view>
					</view>
				</block>
			</view>
		</view>

		<!-- 底部操作栏 -->
		<view class="zhuige-detail-comment" v-if="post">
			<view class="zhuige-detail-input" v-if="!bottom_buttons" @click.stop="openComment(0)">
				<text>我想说两句...</text>
			</view>
			<view class="zhuige-detail-opt">
				<view @click.stop="openComment(0)">
					<uni-icons :type="post.user.is_comment == 1 ? 'chat-filled' : 'chat'"
						:color="post.user.is_comment == 1 ? '#ff6146' : '#444444'" size="24"></uni-icons>
					<uni-badge :text="post.comment_count.toString()" type="error" absolute="rightTop"
						:inverted="false"></uni-badge>
				</view>
				<view @click.stop="clickLike">
					<uni-icons :type="post.user.is_like ? 'hand-up-filled' : 'hand-up'"
						:color="post.user.is_like ? '#FF6147' : ''" size="24"></uni-icons>
					<uni-badge :text="post.like_list.length.toString()" type="error" absolute="rightTop"
						:inverted="false"></uni-badge>
				</view>
				<view @click.stop="clickFavorite">
					<uni-icons :type="post.user.is_favorite ? 'star-filled' : 'star'"
						:color="post.user.is_favorite ? '#FF6147' : ''" size="24"></uni-icons>
					<uni-badge :text="post.favorites.toString()" type="error" absolute="rightTop"
						:inverted="false"></uni-badge>
				</view>
			</view>
			<view class="zhuige-detail-btn" v-if="bottom_buttons">
				<view v-if="post.button1" @click="clickLink(post.button1.link)">{{'' + post.button1.title + ''}}</view>
				<view v-if="post.button2" @click="clickLink(post.button2.link)">{{post.button2.title}}</view>
			</view>
		</view>



		<!-- 海报组件 -->
		<!-- #ifdef MP-BAIDU -->
		<view v-if="isShowPainter" style="position: fixed; top: 0;" @longpress="longTapPainter" @click="clickPainter()">
			<l-painter isCanvasToTempFilePath :board="base" @success="onPainterSuccess" />
		</view>
		<!-- #endif -->

		<!-- #ifdef MP-WEIXIN || H5 -->
		<l-painter v-if="isShowPainter" isCanvasToTempFilePath custom-style="position: fixed; left: 200%;" :board="base"
			@success="onPainterSuccess" />
		<!-- #endif -->

		<!-- 评论弹窗 -->
		<uni-popup ref="popupcomment" class="vue-ref" type="bottom">
			<view class="zhuige-pop-textarea" :style="'margin-bottom:' + comment_bottom + 'px;'">
				<view>
					<textarea v-model="comment_content" placeholder="友善是交流的起点......" maxlength="140" fixed="true"
						@focus="focusCommentTextarea" @blur="blurCommentTextarea"></textarea>
				</view>
				<view class="zhuige-pop-text-btn">
					<text>{{comment_content.length + '/140'}}</text>
					<view @click="clickSubmitComment">确定</view>
				</view>
			</view>
		</uni-popup>

		<!-- 打赏弹窗 -->
		<uni-popup ref="popupreward" type="center">
			<view class="zhuige-popup-center">
				<view class="zhuige-popup-tips" v-if="is_ios">
					<text>由于相关规范，您暂时无法使用相关功能</text>
				</view>
				<block v-else>
					<view class="zhuige-popup-qr" v-if="post && post.author && post.author.reward">
						<image mode="aspectFill" :src="post.author.reward"></image>
					</view>
				</block>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeReply from "@/components/zhuige-reply";

	export default {
		components: {
			ZhuigeReply
		},
		data() {
			return {
				// 基础数据
				title: 'uni-fab',
				directionStr: '垂直',
				horizontal: 'right',
				vertical: 'bottom',
				direction: 'horizontal',
				pattern: {
					color: '#7A7E83',
					backgroundColor: '#fff',
					selectedColor: '#363B51',
					buttonColor: '#363B51',
					iconColor: '#fff'
				},
				is_color_type: false,
				content: [{
					iconPath: '/static/images/default/home_f.png',
					selectedIconPath: '/static/images/default/home_f.png',
					text: '首页',
					active: false
				}, {
					iconPath: '/static/images/default/post_f.png',
					selectedIconPath: '/static/images/default/post_f.png',
					text: '举报',
					active: false
				}],

				// 文章相关数据
				post_id: undefined,
				post: null,
				official_switch: undefined,
				copyright: undefined,
				ad_up: undefined,
				ad_down: undefined,
				wx_ad: undefined,
				interstitialAd: undefined,
				comments: [],
				loadMore: 'more',
				loaded: false,
				isShowPainter: false,
				painterImage: '',
				acode: '',
				poster: undefined,
				base: undefined,
				loginReload: false,
				comment_focus: false,
				comment_bottom: 0,
				comment_content: '',
				rewardedVideoAd: undefined,
				jili_content: '',
				is_ios: false,
				parent_comment_id: 0,
				reply_user_id: 0
			};
		},
		computed: {
			bottom_buttons() {
				return this.post && (this.post.button1 || this.post.button2);
			}
		},
		onLoad(options) {
			// 处理文章ID参数
			if (options.id) {
				options.post_id = options.id;
			}

			if (options.post_id) {
				this.post_id = options.post_id;
			} else if (options.scene) {
				this.post_id = options.scene;
			} else {
				this.post_id = 177; // 默认文章ID
			}

			if (this.post_id) {
				Util.addShareScore(options.source);
				this.is_ios = uni.getSystemInfoSync().platform === 'ios';

				// 注册事件监听
				uni.$on('linktap', this.onMPHtmlLink);
				uni.$on('zhuige_event_user_login', this.onSetReload);
				uni.$on('zhuige_event_follow_user', this.onFlollowUser);

				this.loadSetting();
			} else {
				uni.reLaunch({
					url: '/pages/tabs/index/index'
				});
			}
		},
		onUnload() {
			uni.$off('zhuige_event_follow_user', this.onFlollowUser);
			uni.$off('zhuige_event_user_login', this.onSetReload);
			uni.$off('linktap', this.onMPHtmlLink);
		},
		onShow() {
			if (!this.post) {
				this.loadDetail();
			}
			if (this.loginReload) {
				this.loginReload = false;
				this.refresh();
			}
		},
		onPullDownRefresh() {
			this.refresh();
		},
		onReachBottom() {
			if (this.loadMore === 'more') {
				this.loadComments();
			}
		},
		onShareAppMessage() {
			let shareData = {
				path: Util.addShareSource('pages/cms/detail/detail?post_id=' + this.post_id)
			};
			if (this.post) {
				if (this.post.title) {
					shareData.title = this.post.title + '-' + getApp().globalData.appName;
				}
				if (this.post.thumbnail) {
					shareData.imageUrl = this.post.thumbnail;
				}
			}
			return shareData;
		},
		onShareTimeline() {
			let shareData = {};
			if (this.post) {
				if (this.post.title) {
					shareData.title = this.post.title + '-' + getApp().globalData.appName;
				}
				if (this.post.thumbnail) {
					shareData.imageUrl = this.post.thumbnail;
				}
			}
			return shareData;
		},
		methods: {
			onFlollowUser(data) {
				if (this.post && this.post.author && this.post.author.user_id == data.user_id) {
					this.post.author.is_follow = data.is_follow;
				}
			},
			onSetReload() {
				this.loginReload = true;
			},
			onMPHtmlLink(data) {
				if (data['data-link']) {
					Util.openLink(data['data-link']);
				}
			},
			refresh() {
				this.loadSetting();
				this.loadDetail();
			},
			clickUser(user_id) {
				Util.openLink('/pages/user/home/<USER>' + user_id);
			},
			clickFollowUser(user_id) {
				Rest.post(Api.URL('user', 'follow_user'), {
					user_id: this.post.author.user_id
				}).then(res => {
					uni.$emit('zhuige_event_follow_user', {
						user_id: user_id,
						is_follow: res.data.is_follow
					});
				}, err => {
					console.log(err);
				});
			},
			clickCost(type) {
				if (this.is_ios && this.post.cost_ios_switch != '1') {
					this.$refs.popupreward.open();
				} else {
					Rest.post(Api.URL('cms', 'wx_post_pay'), {
						post_id: this.post_id,
						post_type: type
					}).then(res => {
						if (res.code == 0) {
							if (res.data.success) {
								uni.requestPayment({
									timeStamp: res.data.timeStamp,
									nonceStr: res.data.nonceStr,
									package: res.data.package,
									signType: 'MD5',
									paySign: res.data.paySign,
									success: (result) => {
										Alert.toast('支付成功');
										if (type == 'post') {
											setTimeout(() => {
												this.refresh();
											}, 1500);
										}
									},
									fail: (error) => {
										Alert.toast('未支付');
									}
								});
							} else if (res.data.return_msg) {
								Alert.toast(res.data.return_msg);
							} else {
								Alert.toast('不明异常~');
							}
						} else {
							Alert.toast(res.message);
						}
					}, err => {
						console.log(err);
					});
				}
			},
			clickCostScore(type) {
				let content = '';
				if (type == 'post') {
					content = '确认使用' + this.post.cost_score + '积分阅读';
				}
				uni.showModal({
					title: '提示',
					content: content,
					success: (res) => {
						if (res.confirm) {
							Rest.post(Api.URL('cms', 'score_post_pay'), {
								post_id: this.post_id,
								post_type: type
							}).then(result => {
								Alert.toast(result.message);
								if (result.code == 0 && type == 'post') {
									setTimeout(() => {
										this.refresh();
									}, 1500);
								}
							}, err => {
								console.log(err);
							});
						}
					}
				});
			},
			clickRewardVideo() {
				if (this.rewardedVideoAd) {
					this.rewardedVideoAd.show().catch(() => {
						this.rewardedVideoAd.load().then(() => {
							return this.rewardedVideoAd.show();
						}).catch(err => {
							console.log('激励视频 广告显示失败');
						});
					});
				}
			},
			clickTag(tag_id, tag_name) {
				Util.openLink('/pages/cms/list/list?tag_id=' + tag_id + '&title=' + tag_name);
			},
			clickDetail(item) {
				if (item.driect_link_switch === '1') {
					Util.openLink(item.driect_link);
				} else {
					Util.openLink('/pages/cms/detail/detail?post_id=' + item.id);
				}
			},
			clickLink(url) {
				Util.openLink(url);
			},
			clickPoster() {
				if (!this.poster) {
					Alert.error('请在后台配置海报背景');
					return;
				}

				// #ifndef MP-BAIDU
				if (this.painterImage) {
					uni.previewImage({
						urls: [this.painterImage]
					});
					return;
				}
				// #endif

				uni.showLoading({
					title: '海报生成中……'
				});

				this.isShowPainter = true;

				this.base = {
					css: {
						width: '750rpx',
						height: '1334rpx',
					},
					views: [{
							type: 'image',
							src: this.poster.bg,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '0rpx',
								top: '0rpx',
								width: '750rpx',
								height: '1334rpx'
							}
						}, {
							type: 'view',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '240rpx',
								width: '690rpx',
								height: '860rpx',
								background: '#FFFFFF',
								radius: '20rpx'
							}
						}, {
							type: 'image',
							src: this.post.thumbnail ? this.post.thumbnail : this.poster.thumb,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '240rpx',
								width: '690rpx',
								height: '500rpx',
								radius: '20rpx'
							}
						}, {
							type: 'text',
							text: this.post.title,
							css: {
								position: 'absolute',
								left: '70rpx',
								top: '755rpx',
								width: '610rpx',
								color: '#000000',
								fontSize: '36rpx',
								textAlign: 'left',
								lineClamp: 1,
								fontWeight: 'bold'
							}
						}, {
							type: 'text',
							text: this.post.excerpt,
							css: {
								position: 'absolute',
								left: '70rpx',
								top: '820rpx',
								width: '610rpx',
								color: '#000000',
								fontSize: '28rpx',
								lineHeight: '50rpx',
								lineClamp: 2
							}
						}, {
							type: 'view',
							css: {
								position: 'absolute',
								left: '255rpx',
								top: '940rpx',
								width: '240rpx',
								height: '240rpx',
								background: '#FFFFFF',
								radius: '120rpx'
							}
						},

						// #ifdef MP-WEIXIN || H5
						{
							type: 'image',
							src: this.acode,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '275rpx',
								top: '960rpx',
								width: '200rpx',
								height: '200rpx',
								borderRadius: '30%',
								border: '1rpx solid rgb(255,255,255)'
							}
						},
						// #endif

						// #ifdef MP-BAIDU
						{
							type: 'view',
							css: {
								position: 'absolute',
								left: '255rpx',
								top: '940rpx',
								width: '240rpx',
								height: '240rpx',
								background: '#FFFFFF',
								radius: '20rpx'
							}
						},
						{
							type: 'image',
							src: this.acode,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '275rpx',
								top: '960rpx',
								width: '200rpx',
								height: '200rpx'
							}
						},
						// #endif

						{
							type: 'text',
							text: getApp().globalData.appName,
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '1200rpx',
								width: '690rpx',
								color: '#FFFFFF',
								fontSize: '28rpx',
								textAlign: 'center',
							}
						}
					]
				};

				// 添加作者信息
				if (this.post.author) {
					this.base.views.splice(1, 0, {
						type: 'image',
						src: this.post.author.avatar,
						mode: 'aspectFill',
						css: {
							position: 'absolute',
							left: '30rpx',
							top: '70rpx',
							width: '120rpx',
							height: '120rpx',
							radius: '60rpx'
						}
					}, {
						type: 'text',
						text: this.post.author.nickname,
						css: {
							position: 'absolute',
							left: '165rpx',
							top: '90rpx',
							width: '525rpx',
							color: '#FFFFFF',
							fontSize: '38rpx',
							textAlign: 'left',
							lineClamp: 1
						}
					}, {
						type: 'text',
						text: this.post.author.sign ? this.post.author.sign : this.poster.title,
						css: {
							position: 'absolute',
							left: '165rpx',
							top: '140rpx',
							width: '525rpx',
							color: '#FFFFFF',
							fontSize: '24rpx',
							textAlign: 'left',
							lineClamp: 1
						}
					});
				}
			},
			onPainterSuccess: function(e) {
				this.painterImage = e;

				// #ifndef MP-BAIDU
				uni.previewImage({
					urls: [e]
				});
				// #endif

				uni.hideLoading();
			},

			// #ifdef MP-BAIDU
			/**
			 * 百度小程序 - 长按海报保存
			 */
			longTapPainter() {
				if (this.painterImage) {
					uni.saveImageToPhotosAlbum({
						filePath: this.painterImage,
						success: () => {
							Alert.toast('保存成功');
						},
						fail: () => {
							Alert.toast('保存失败');
						}
					});
				}
			},

			/**
			 * 百度小程序 - 点击海报关闭
			 */
			clickPainter() {
				this.isShowPainter = false;
			},
			// #endif
			clickReword() {
				this.$refs.popupreward.open();
			},
			clickLike() {
				Rest.post(Api.URL('user', 'like'), {
					post_id: this.post_id
				}).then(res => {
					if (this.post.read_limit === 'like') {
						this.refresh();
					} else {
						this.post.user.is_like = res.data.is_like;
						if (res.data.is_like) {
							this.post.like_list.unshift(res.data.user);
						} else {
							let newList = [];
							this.post.like_list.forEach(user => {
								if (user.user_id != res.data.user.user_id) {
									newList.push(user);
								}
							});
							this.post.like_list = newList;
						}
					}
				}, err => {
					console.log(err);
				});
			},
			clickFavorite() {
				Rest.post(Api.URL('user', 'favorite'), {
					type: 'post',
					post_id: this.post_id
				}).then(res => {
					this.post.user.is_favorite = res.data.is_favorite;
					this.post.favorites = res.data.favorites;
				}, err => {
					console.log(err);
				});
			},
			checkComment() {
				if (this.post.comment_switch != '1') {
					Alert.error('评论已关闭');
					return false;
				}
				if (this.post.comment_require_mobile2) {
					Util.openLink('/pages/user/login/login?type=mobile&tip=评论');
					return false;
				}
				if (this.post.comment_require_avatar2) {
					Util.openLink('/pages/user/verify/verify?tip=评论');
					return false;
				}
				return true;
			},
			openComment(parent_id) {
				if (this.checkComment()) {
					this.parent_comment_id = parent_id;
					this.reply_user_id = 0;
					this.$refs.popupcomment.open('bottom');
				}
			},
			clickReply(comment) {
				if (this.checkComment()) {
					this.parent_comment_id = comment.comment_id;
					this.reply_user_id = comment.user_id;
					this.$refs.popupcomment.open('bottom');
				}
			},
			focusCommentTextarea(e) {
				this.comment_bottom = e.detail.height;
			},
			blurCommentTextarea() {
				this.comment_bottom = 0;
			},
			clickSubmitComment() {
				Rest.post(Api.URL('comment', 'add'), {
					post_id: this.post_id,
					parent_id: this.parent_comment_id,
					reply_id: this.reply_user_id,
					content: this.comment_content
				}).then(res => {
					if (res.code == 0) {
						Alert.toast('审核后，他人可见');
						this.comment_id = 0;
						this.parent_comment_id = 0;
						this.reply_user_id = 0;
						this.comment_content = '';
						this.post.user.is_comment = 1;
						this.$refs.popupcomment.close();
					} else if (res.code == 'require_mobile') {
						Util.openLink('/pages/user/login/login?type=mobile&tip=评论');
					} else if (res.code == 'require_avatar') {
						Util.openLink('/pages/user/verify/verify?tip=评论');
					} else {
						Alert.toast(res.message);
					}
				}, err => {
					console.log(err);
				});
			},
			clickAllComments() {
				Util.openLink('/pages/base/comments/comments?post_id=' + this.post_id);
			},
			loadSetting() {
				Rest.post(Api.URL('cms', 'setting_detail')).then(res => {
					this.official_switch = res.data.official_switch;
					this.copyright = res.data.copyright;
					this.ad_up = res.data.ad_up;
					this.ad_down = res.data.ad_down;
					this.wx_ad = res.data.wx_ad;

					if (this.wx_ad.chaping && uni.createInterstitialAd) {
						this.interstitialAd = uni.createInterstitialAd({
							adUnitId: this.wx_ad.chaping
						});
						this.interstitialAd.onLoad(() => {
							this.interstitialAd.show();
						});
					}

					this.loadWxJili();

					if (res.data.poster) {
						this.poster = res.data.poster;
					}
				}, err => {
					console.log(err);
				});
			},
			loadDetail() {
				Rest.post(Api.URL('cms', 'detail'), {
					post_id: this.post_id
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code != 0) {
						Alert.toast(res.message);
						return;
					}

					this.post = res.data;
					this.loadWxJili();
					this.loadACode();
					this.loadComments();
				}, err => {
					console.log(err);
				});
			},
			loadWxJili() {
				if (this.post && this.post.read_limit === 'jiliv' && this.wx_ad && this.wx_ad.jili && !this
					.rewardedVideoAd) {
					// #ifdef MP-WEIXIN
					this.rewardedVideoAd = uni.createRewardedVideoAd({
						adUnitId: this.wx_ad.jili
					});
					this.rewardedVideoAd.onLoad(() => {});
					this.rewardedVideoAd.onError(err => {});
					this.rewardedVideoAd.onClose(res => {
						if (res && res.isEnded && this.post.read_limit) {
							this.loadContent();
						}
					});
					// #endif
				}
			},
			loadComments() {
				if (this.loadMore === 'loading') {
					return;
				}

				this.loadMore = 'loading';
				Rest.post(Api.URL('comment', 'index'), {
					post_id: this.post_id,
					offset: this.comments.length
				}).then(res => {
					this.comments = this.comments.concat(res.data.comments);
					this.loadMore = res.data.more;
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			},
			loadContent() {
				Rest.post(Api.URL('cms', 'content'), {
					post_id: this.post_id
				}).then(res => {
					this.jili_content = res.data.content;
					console.log(this.jili_content);
				}, err => {
					console.log(err);
				});
			},
			loadACode() {
				// #ifdef MP-WEIXIN
				Rest.post(Api.URL('posts', 'wxacode'), {
					post_id: this.post_id,
					type: 'post'
				}).then(res => {
					this.acode = res.data.acode;
				}, err => {
					console.log(err);
				});
				// #endif

				// #ifdef MP-BAIDU
				Rest.post(Api.URL('posts', 'baiducode'), {
					post_id: this.post_id,
					type: 'post'
				}).then(res => {
					this.acode = res.data.acode;
				}, err => {
					console.log(err);
				});
				// #endif
			}

		}
	};
</script>

<style lang="scss">
	.content {
		padding-bottom: 160rpx;
	}

	.zhuige-pop-mask {
		align-items: center;
		background: rgba(0, 0, 0, .7);
		display: flex;
		height: 100%;
		justify-content: center;
		left: 0;
		position: fixed;
		top: 0;
		width: 100%;
		z-index: 999;
	}

	.zhuige-pop-info {
		background: #fff;
		border-radius: 12rpx;
		padding: 30rpx;
		position: relative;
		text-align: center;
		width: 480rpx;
	}

	.zhuige-pop-info .pop-text {
		color: #666;
		font-size: 28rpx;
		font-weight: 300;
		height: 3em;
		line-height: 3em;
	}

	.zhuige-pop-info image {
		height: 400rpx;
		width: 400rpx;
	}

	.zhuige-pop-info .pop-icon {
		bottom: -150rpx;
		left: 230rpx;
		position: absolute;
	}

	.zhuige-detail-ad,
	.zhuige-detail-block,
	.zhuige-detail-recom,
	.zhuige-detail-share,
	.zhuige-reply-list,
	.zhuige-traffic-ad {
		padding: 0 20rpx;
	}

	.zhuige-traffic-ad {
		position: relative;
		z-index: 9;
	}

	.zhuige-detail-ad {
		margin-bottom: 20rpx;
	}

	.zhuige-detail-block>.zhuige-block {
		padding: 30rpx;
	}

	.zhuige-detail-block .zhuige-wide-image-ad {
		padding: 20rpx 0;
	}

	.zhuige-detail-cont {
		font-size: 30rpx;
		padding: 20rpx 0;
	}

	.zhuige-detail-classify {
		align-items: center;
		background: #f1f3f5;
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		padding: 20rpx;
	}

	.zhuige-detail-classify .zhuige-classify-block {
		border: none;
		padding: 0;
		width: 80%;
	}

	.zhuige-detail-classify .zhuige-classify-block>view:nth-child(1) {
		height: 96rpx;
		width: 96rpx;
	}

	.zhuige-detail-classify .zhuige-classify-text view {
		height: 48rpx;
		line-height: 48rpx;
	}

	.zhuige-detail-comment {
		align-items: center;
		background: #fff;
		bottom: 0;
		box-shadow: 0rpx 0rpx 6rpx rgba(0, 0, 0, .1);
		display: flex;
		height: 128rpx;
		justify-content: space-between;
		left: 0;
		padding-bottom: 20rpx;
		padding-top: 4rpx;
		position: fixed;
		width: 100%;
		z-index: 99;
	}

	.zhuige-detail-input {
		background-color: #f6f6f6;
		border-radius: 80rpx;
		height: 80rpx;
		line-height: 80rpx;
		margin-left: 30rpx;
		padding-left: 30rpx;
		width: 320rpx;
	}

	.zhuige-detail-input text {
		color: #999;
		font-size: 24rpx;
		text-indent: 1.4rem;
	}

	.zhuige-detail-opt {
		align-items: center;
		display: flex;
		padding: 0 20rpx;
	}

	.zhuige-detail-opt>view {
		margin: 0 28rpx;
	}

	.zhuige-detail-opt view .uni-badge {
		margin-top: -44rpx;
	}

	.zhuige-detail-btn {
		align-items: center;
		display: flex;
		padding-right: 20rpx;
	}

	.zhuige-detail-btn view {
		background: #ff684f;
		border-radius: 40rpx;
		color: #fff;
		font-size: 24rpx;
		font-weight: 400;
		height: 64rpx;
		line-height: 64rpx;
		margin: 0 0 0 12rpx;
		padding: 0 32rpx;
	}

	.zhuige-detail-btn view:nth-child(2) {
		background: #010101;
	}

	.zhuige-social-poster-blcok {
		border-bottom: 1rpx solid #eee;
		padding-bottom: 20rpx;
	}

	.zhuige-detail-title {
		font-size: 36rpx;
		font-weight: 600;
		line-height: 1.8em;
	}

	.zhuige-detail-data {
		align-items: center;
		display: flex;
		padding: 10rpx 0;
	}

	.zhuige-detail-data text {
		background: #f5f5f5;
		border-radius: 8rpx;
		color: #333;
		font-size: 24rpx;
		height: 44rpx;
		line-height: 44rpx;
		margin: 0 8rpx 0 0;
		padding: 0 20rpx;
	}

	.zhuige-detail-tags {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
		padding: 10rpx 0;
	}

	.zhuige-detail-tags text {
		background: #f5f5f5;
		border-radius: 56rpx;
		color: #333;
		font-size: 24rpx;
		height: 56rpx;
		line-height: 56rpx;
		margin: 0 8rpx 8rpx;
		padding: 0 24rpx;
	}

	.zhuige-detail-button {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		padding: 20rpx;
	}

	.zhuige-detail-button view {
		background: #ff684f;
		border-radius: 40rpx;
		color: #fff;
		font-size: 30rpx;
		font-weight: 300;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		width: 66%;
	}

	.zhuige-detail-button text {
		color: #666;
		font-size: 26rpx;
		font-weight: 400;
		padding: 20rpx;
		text-align: center;
		width: 66%;
	}

	.zhuige-detail-copy {
		background: #f6f6f6;
		border: 1rpx solid #f3f3f3;
		color: #555;
		font-size: 26rpx;
		font-weight: 400;
		padding: 30rpx;
	}

	.zhuige-detail-cms-recom {
		margin: 0 20rpx;
	}

	.zhuige-detail-cms-recom .zhugie-info-block {
		border-bottom: 1rpx solid #ddd;
		padding: 20rpx 0;
	}

	.zhuige-block-head+.left-side,
	.zhuige-block-head+.right-side {
		padding-top: 0;
	}

	.zhuige-detail-cms-recom .zhugie-info-block:last-of-type {
		border: none;
	}

	.zhuige-wide-image-ad view {
		height: 140rpx;
	}

	.zhuige-info-data text {
		margin: 0 12rpx 0 0;
	}

	.zhugie-info-title {
		font-size: 28rpx !important;
		margin-bottom: 10rpx;
	}

	.jiangqie-comment-no-btn .jiangqie-comment-btn {
		display: none;
	}

	.jiangqie-comment-no-btn .jiangqie-comment-input {
		display: inline;
	}
</style>