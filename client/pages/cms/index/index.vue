<template>
	<view class="content">
		<!-- 轮播图 -->
		<zhuige-swiper v-if="slides && slides.length > 0" :items="slides" type="zhuige-space-swiper" nextMargin="10rpx"
			previousMargin="10rpx"></zhuige-swiper>

		<view class="zhuige-cms-box">
			<!-- 导航图标 -->
			<view class="zhuige-block" v-if="navs && navs.length > 0">
				<zhuige-icons :items="navs" type="scroll"></zhuige-icons>
			</view>

			<!-- 三图广告 -->
			<zhuige-treble-ad v-if="events" :title="events.title" :description="events.description"
				:items="events.items"></zhuige-treble-ad>

			<!-- 热门标签 -->
			<view class="zhuige-block" v-if="tags && tags.length > 0">
				<view class="zhuige-block-head">
					<view>热门标签</view>
					<view @click="clickLink('/pages/cms/tags/tags')">查看更多</view>
				</view>
				<view class="zhuige-tags-list-box">
					<view class="zhuige-tags-list" :style="'width:' + tags_width + 'rpx'">
						<view v-for="(tag, index) in tags" :key="index"
							@click="clickLink('/pages/cms/list/list?tag_id=' + tag.id + '&title=' + tag.name)">
							<text v-if="tag.badge">{{tag.badge}}</text>
							{{'#' + tag.name + ''}}
						</view>
					</view>
				</view>
			</view>

			<!-- 分类标签 -->
			<view class="zhuige-scroll-box" v-if="nav_cats && nav_cats.length > 0">
				<zhuige-tab :tabs="nav_cats" :curTab="cat_cur_id" type="scroll" :opt="true" @clickTab="clickTab"
					@clickTabOpt="clickTabOpt"></zhuige-tab>
			</view>

			<!-- 文章列表 -->
			<view class="zhuige-infor-list" v-if="posts && posts.length > 0">
				<block v-for="(post, index) in posts" :key="index">
					<!-- 广告插入 -->
					<view class="zhuige-block"
						v-if="wx_ad && wx_ad.frequency > 0 && (index + 1) % wx_ad.frequency == 0">
						<view class="zhugie-info-block">
							<view class="zhugie-info-text">
								<view class="zhugie-info-title">{{'' + wx_ad.title + ''}}</view>
								<view class="zhugie-info-ad">
									<ad-custom :unit-id="wx_ad.banner"></ad-custom>
								</view>
								<view class="zhuige-info-post">
									<view class="zhuige-info-data">
										<text>广告</text>
										<text>{{wx_ad.desc}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 文章项 -->
					<view class="zhuige-block" @click="clickDetail(post)">
						<!-- 无图文章 -->
						<view class="zhugie-info-block" v-if="post.thumbnails.length == 0">
							<view class="zhugie-info-text">
								<view class="zhugie-info-title">{{'' + post.title + ''}}</view>
								<view class="zhuige-info-post">
									<view class="zhuige-info-data">
										<text v-if="post.stick == 1" class="data-top">置顶</text>
										<text
											v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
											class="pay">{{'￥' + post.cost_price}}</text>
										<text v-if="post.read_limit == 'score'"
											class="pay">{{post.cost_score + '积分'}}</text>
										<text>{{'浏览 ' + post.views}}</text>
										<text>{{'点赞 ' + post.likes}}</text>
									</view>
								</view>
							</view>
						</view>

						<!-- 有图文章 -->
						<block v-else>
							<!-- 单图或双图 -->
							<block v-if="post.thumbnails.length < 3">
								<!-- 右侧图片 -->
								<view class="zhugie-info-block right-side" v-if="index % 5 != 4">
									<view class="zhugie-info-image">
										<text v-if="post.badge">{{post.badge}}</text>
										<image mode="aspectFill" :src="post.thumbnails[0]"></image>
									</view>
									<view class="zhugie-info-text">
										<view class="zhugie-info-title">{{'' + post.title + ''}}</view>
										<view class="zhuige-info-post">
											<view class="zhuige-info-data">
												<text v-if="post.stick == 1" class="data-top">置顶</text>
												<text
													v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
													class="pay">{{'￥' + post.cost_price}}</text>
												<text v-if="post.read_limit == 'score'"
													class="pay">{{post.cost_score + '积分'}}</text>
												<text>{{'浏览 ' + post.views}}</text>
												<text>{{'点赞 ' + post.likes}}</text>
											</view>
										</view>
									</view>
								</view>

								<!-- 大图模式 -->
								<view class="zhugie-info-block" v-else>
									<view class="zhugie-info-text">
										<view class="zhugie-info-title">{{'' + post.title + ''}}</view>
									</view>
									<view class="zhugie-info-image">
										<text v-if="post.badge">{{post.badge}}</text>
										<image mode="aspectFill" :src="post.thumbnails[0]"></image>
									</view>
									<view class="zhugie-info-text">
										<view class="zhuige-info-post">
											<view class="zhuige-info-data">
												<text v-if="post.stick == 1">置顶</text>
												<text
													v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
													class="pay">{{'￥' + post.cost_price}}</text>
												<text v-if="post.read_limit == 'score'"
													class="pay">{{post.cost_score + '积分'}}</text>
												<text>{{'浏览 ' + post.views}}</text>
												<text>{{'点赞 ' + post.likes}}</text>
											</view>
										</view>
									</view>
								</view>
							</block>

							<!-- 三图模式 -->
							<block v-else>
								<view class="zhugie-info-block" v-if="post.thumbnails.length >= 3">
									<view class="zhugie-info-text">
										<view class="zhugie-info-title">{{'' + post.title + ''}}</view>
									</view>
									<view class="zhugie-info-image image-treble">
										<text v-if="post.badge">{{post.badge}}</text>
										<image v-for="(thumb, thumbIndex) in post.thumbnails" :key="thumbIndex"
											mode="aspectFill" :src="thumb"></image>
									</view>
									<view class="zhugie-info-text">
										<view class="zhuige-info-post">
											<view class="zhuige-info-data">
												<text v-if="post.stick == 1" class="data-top">置顶</text>
												<text
													v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
													class="pay">{{'￥' + post.cost_price}}</text>
												<text v-if="post.read_limit == 'score'"
													class="pay">{{post.cost_score + '积分'}}</text>
												<text>{{'浏览 ' + post.views}}</text>
												<text>{{'点赞 ' + post.likes}}</text>
											</view>
										</view>
									</view>
								</view>
							</block>
						</block>
					</view>
				</block>

				<!-- 加载更多 -->
				<uni-load-more :status="loadMore"></uni-load-more>
			</view>

			<!-- 无数据 -->
			<block v-else>
				<zhuige-nodata v-if="loaded"></zhuige-nodata>
			</block>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeSwiper from "@/components/zhuige-swiper";
	import ZhuigeIcons from "@/components/zhuige-icons";
	import ZhuigeTrebleAd from "@/components/zhuige-treble-ad";
	import ZhuigeTab from "@/components/zhuige-tab";
	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeSwiper,
			ZhuigeIcons,
			ZhuigeTrebleAd,
			ZhuigeTab,
			ZhuigeNodata
		},
		data() {
			return {
				nav_cats: [],
				cat_cur_id: 0,
				scrollLeft: 0,
				slides: [],
				navs: [],
				events: undefined,
				tags_width: 750,
				tags: [],
				posts: [],
				loadMore: 'more',
				loaded: false,
				wx_ad: undefined,
				bd_title: '',
				bd_description: '',
				bd_keywords: '',
				is_ios: false
			};
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			this.is_ios = uni.getSystemInfoSync().platform === 'ios';
			this.loadSetting();
			this.loadPosts();
		},
		onShow() {},
		onShareAppMessage() {
			let shareData = {
				title: getApp().globalData.appName + '-' + getApp().globalData.appDesc,
				path: Util.addShareSource('pages/cms/index/index?n=n')
			};
			if (this.thumb) {
				shareData.imageUrl = this.thumb;
			}
			return shareData;
		},
		onShareTimeline() {
			return {
				title: getApp().globalData.appName + '-' + getApp().globalData.appDesc
			};
		},
		onReachBottom() {
			if (this.loadMore === 'more') {
				this.loadPosts();
			}
		},
		onPullDownRefresh() {
			this.refresh();
		},
		methods: {
			refresh() {
				this.loadSetting();
				this.loadMore = 'more';
				this.loaded = false;
				this.posts = [];
				this.loadPosts();
			},
			clickTab(tab) {
				if (this.cat_cur_id !== tab.id) {
					this.cat_cur_id = tab.id;
					this.loadMore = 'more';
					this.loaded = false;
					this.posts = [];
					this.loadPosts();
				}
			},
			clickTabOpt() {
				Util.openLink('/pages/cms/classify/classify');
			},
			clickDetail(post) {
				if ('1' == post.driect_link_switch) {
					Util.openLink(post.driect_link);
				} else {
					Util.openLink('/pages/cms/detail/detail?post_id=' + post.id);
				}
			},
			clickLink(url) {
				Util.openLink(url);
			},
			loadSetting() {
				Rest.post(Api.URL('cms', 'setting_home')).then(res => {
					this.nav_cats = res.data.nav_cats;
					this.slides = res.data.slides;
					this.navs = res.data.navs;
					if (res.data.events) {
						this.events = res.data.events;
					}
					this.tags = res.data.tags;
					this.tags_width = res.data.tags_width;
					if (res.data.wx_ad) {
						this.wx_ad = res.data.wx_ad;
					}
					if (res.data.thumb) {
						this.thumb = res.data.thumb;
					}
					uni.stopPullDownRefresh();
				}, err => {
					console.log(err);
				});
			},
			loadPosts() {
				if (this.loadMore === 'loading') {
					return;
				}

				this.loadMore = 'loading';

				if (this.cat_cur_id === 0) {
					// 加载最新文章
					Rest.post(Api.URL('cms', 'last'), {
						offset: this.posts.length
					}).then(res => {
						this.posts = this.posts.concat(res.data.posts);
						this.loadMore = res.data.more;
						this.loaded = true;
					}, err => {
						console.log(err);
					});
				} else {
					// 加载分类文章
					Rest.post(Api.URL('cms', 'category'), {
						offset: this.posts.length,
						cat_id: this.cat_cur_id
					}).then(res => {
						this.posts = this.posts.concat(res.data.posts);
						this.loadMore = res.data.more;
						this.loaded = true;
					}, err => {
						console.log(err);
					});
				}
			}
		}
	};
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
	}

	.zhuige-cms-box {
		margin: 20rpx;
	}

	.zhuige-treble-ad-box {
		margin-bottom: 20rpx;
	}

	.zhuige-tags-list-box {
		overflow-x: scroll;
		padding-bottom: 12rpx;
	}

	.zhuige-tags-list-box::-webkit-scrollbar {
		display: none;
	}

	.zhuige-tags-list {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
		overflow-y: hidden;
	}

	.zhuige-tags-list view {
		background: #f3f3f3;
		border-radius: 60rpx;
		color: #555;
		font-size: 24rpx;
		font-weight: 500;
		margin-right: 20rpx;
		margin-top: 18rpx;
		padding: 8rpx 30rpx;
		position: relative;
	}

	.zhuige-tags-list view text {
		background: #363b51;
		border-radius: 18rpx 18rpx 18rpx 0;
		color: #fff;
		font-size: 18rpx;
		font-weight: 300;
		height: 36rpx;
		line-height: 36rpx;
		padding: 0 12rpx;
		position: absolute;
		right: -20rpx;
		top: -18rpx;
		z-index: 6;
	}

	.zhuige-tab-nav .view {
		height: 72rpx !important;
		line-height: 72rpx !important;
		padding: 0 20rpx !important;
	}

	.zhuige-tab,
	.zhuige-tab-nav .view:first-of-type {
		padding-left: 0 !important;
	}

	.zhuige-tab-nav .view.active::after {
		top: 66rpx !important;
	}

	.zhugie-info-title {
		margin-top: -6rpx;
	}

	.zhuige-info-data text {
		margin: 0 12rpx 0 0;
	}

	.zhuige-info-post {
		padding-top: 12rpx;
	}

	.zhuige-dots-left .wx-swiper-dots.wx-swiper-dots-horizontal {
		bottom: 40rpx;
		padding-left: 10rpx;
		text-align: left;
		width: 90%;
	}

	.zhuige-swiper view view {
		bottom: 80rpx !important;
	}

	.zhuige-scroll-box {
		margin-bottom: 20rpx;
	}
</style>