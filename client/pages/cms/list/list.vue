<template>
	<view class="content">
		<view class="zhuige-information-list" v-if="posts && posts.length > 0">
			<block v-for="(post, index) in posts" :key="index">
				<!-- 广告插入 -->
				<view class="zhuige-block" v-if="wx_ad && wx_ad.frequency > 0 && (index + 1) % wx_ad.frequency == 0">
					<view class="zhugie-info-block">
						<view class="zhugie-info-text">
							<view class="zhugie-info-title">{{'' + wx_ad.title + ''}}</view>
							<view class="zhugie-info-ad">
								<ad-custom :unit-id="wx_ad.banner"></ad-custom>
							</view>
							<view class="zhuige-info-post">
								<view class="zhuige-info-data">
									<text>广告</text>
									<text>{{wx_ad.desc}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 文章项 -->
				<view class="zhuige-block" @click="clickDetail(post)">
					<!-- 无图文章 -->
					<view class="zhugie-info-block" v-if="post.thumbnails.length == 0">
						<view class="zhugie-info-text">
							<view class="zhugie-info-title">{{post.title}}</view>
							<view class="zhuige-info-post">
								<view class="zhuige-info-data">
									<text
										v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
										class="pay">{{'￥' + post.cost_price}}</text>
									<text v-if="post.read_limit == 'score'"
										class="pay">{{post.cost_score + '积分'}}</text>
									<text>{{'浏览 ' + post.views}}</text>
									<text>{{'点赞 ' + post.likes}}</text>
								</view>
							</view>
						</view>
					</view>

					<!-- 有图文章 -->
					<block v-else>
						<!-- 单图或双图 -->
						<block v-if="post.thumbnails.length < 3">
							<!-- 右侧图片 -->
							<view class="zhugie-info-block right-side" v-if="index % 5 != 4">
								<view class="zhugie-info-image">
									<text v-if="post.badge">{{post.badge}}</text>
									<image mode="aspectFill" :src="post.thumbnails[0]"></image>
								</view>
								<view class="zhugie-info-text">
									<view class="zhugie-info-title">{{post.title}}</view>
									<view class="zhuige-info-post">
										<view class="zhuige-info-data">
											<text
												v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
												class="pay">{{'￥' + post.cost_price}}</text>
											<text v-if="post.read_limit == 'score'"
												class="pay">{{post.cost_score + '积分'}}</text>
											<text>{{'浏览 ' + post.views}}</text>
											<text>{{'点赞 ' + post.likes}}</text>
										</view>
									</view>
								</view>
							</view>

							<!-- 大图模式 -->
							<view class="zhugie-info-block" v-else>
								<view class="zhugie-info-text">
									<view class="zhugie-info-title">{{post.title}}</view>
								</view>
								<view class="zhugie-info-image">
									<text v-if="post.badge">{{post.badge}}</text>
									<image mode="aspectFill" :src="post.thumbnails[0]"></image>
								</view>
								<view class="zhugie-info-text">
									<view class="zhuige-info-post">
										<view class="zhuige-info-data">
											<text
												v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
												class="pay">{{'￥' + post.cost_price}}</text>
											<text v-if="post.read_limit == 'score'"
												class="pay">{{post.cost_score + '积分'}}</text>
											<text>{{'浏览 ' + post.views}}</text>
											<text>{{'点赞 ' + post.likes}}</text>
										</view>
									</view>
								</view>
							</view>
						</block>

						<!-- 三图模式 -->
						<block v-else>
							<view class="zhugie-info-block" v-if="post.thumbnails.length >= 3">
								<view class="zhugie-info-text">
									<view class="zhugie-info-title">{{post.title}}</view>
								</view>
								<view class="zhugie-info-image image-treble">
									<text v-if="post.badge">{{post.badge}}</text>
									<image v-for="(thumb, thumbIndex) in post.thumbnails" :key="thumbIndex"
										mode="aspectFill" :src="thumb"></image>
								</view>
								<view class="zhugie-info-text">
									<view class="zhuige-info-post">
										<view class="zhuige-info-data">
											<text
												v-if="post.read_limit == 'cost' && (!is_ios || (is_ios && post.cost_ios_switch == '1'))"
												class="pay">{{'￥' + post.cost_price}}</text>
											<text v-if="post.read_limit == 'score'"
												class="pay">{{post.cost_score + '积分'}}</text>
											<text>{{'浏览 ' + post.views}}</text>
											<text>{{'点赞 ' + post.likes}}</text>
										</view>
									</view>
								</view>
							</view>
						</block>
					</block>
				</view>
			</block>

			<!-- 加载更多 -->
			<uni-load-more :status="loadMore"></uni-load-more>
		</view>

		<!-- 无数据 -->
		<block v-else>
			<zhuige-nodata v-if="loaded"></zhuige-nodata>
		</block>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeNodata
		},
		data() {
			return {
				title: '',
				posts: [],
				loadMore: 'more',
				loaded: false,
				wx_ad: undefined,
				bd_title: '',
				bd_description: '',
				bd_keywords: '',
				is_ios: false
			};
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			this.is_ios = uni.getSystemInfoSync().platform === 'ios';

			if (options.title) {
				this.title = options.title;
				uni.setNavigationBarTitle({
					title: decodeURIComponent(options.title)
				});
			}

			if (options.cat_id) {
				this.cat_id = options.cat_id;
			} else if (options.tag_id) {
				this.tag_id = options.tag_id;
			}

			Rest.post(Api.URL('cms', 'setting_list')).then(res => {
				if (res.data.wx_ad) {
					this.wx_ad = res.data.wx_ad;
				}
			}, err => {
				console.log(err);
			});

			this.loadPost();
		},
		onShow() {},
		onShareAppMessage() {
			let path = 'pages/cms/list/list?title=' + this.title;
			if (this.cat_id) {
				path += '&cat_id=' + this.cat_id;
			} else if (this.tag_id) {
				path += '&tag_id=' + this.tag_id;
			}

			return {
				title: this.title + '-' + getApp().globalData.appName,
				path: Util.addShareSource(path)
			};
		},
		onShareTimeline() {
			return {
				title: this.title + '-' + getApp().globalData.appName
			};
		},
		onReachBottom() {
			if (this.loadMore === 'more') {
				this.loadPost();
			}
		},
		methods: {
			clickLink(url) {
				Util.openLink(url);
			},
			clickDetail(post) {
				if ('1' == post.driect_link_switch) {
					Util.openLink(post.driect_link);
				} else {
					Util.openLink('/pages/cms/detail/detail?post_id=' + post.id);
				}
			},
			loadPost() {
				if (this.loadMore === 'loading') {
					return;
				}

				this.loadMore = 'loading';

				let apiUrl = '';
				let apiParams = {
					offset: this.posts.length
				};

				if (this.cat_id !== undefined) {
					apiUrl = Api.URL('cms', 'category');
					apiParams.cat_id = this.cat_id;
				} else if (this.tag_id !== undefined) {
					apiUrl = Api.URL('cms', 'tag_list');
					apiParams.tag_id = this.tag_id;
				} else {
					apiUrl = Api.URL('cms', 'last');
				}

				Rest.post(apiUrl, apiParams).then(res => {
					if (res.code !== 0) {
						Alert.error(res.message);
						setTimeout(() => {
							Util.navigateBack();
						}, 1500);
						return;
					}

					this.posts = this.posts.concat(res.data.posts);
					this.loadMore = res.data.more;
					this.loaded = true;
				});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
	}

	.zhuige-info-data text {
		margin: 0 12rpx 0 0;
	}

	.zhugie-info-block .zhugie-info-text:last-of-type {
		padding-top: 12rpx;
	}
</style>