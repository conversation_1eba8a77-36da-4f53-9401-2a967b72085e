<template>
	<view class="content">
		<view class="zhuige-tags-list">
			<block v-if="tags && tags.length > 0">
				<view class="zhuige-block" v-for="(tag, index) in tags" :key="index" @click="clickTag(tag)">
					<view class="zhuige-tags-block">
						<view class="zhuige-tags-info">
							<view>
								<image mode="aspectFill" :src="tag.logo"></image>
							</view>
							<view>
								<view>{{tag.name}}</view>
								<view>
									<text>{{'总计' + tag.count + '篇'}}</text>
								</view>
							</view>
						</view>
						<view class="zhuige-tags-act">
							<uni-icons type="right" size="20" color="#BBBBBB"></uni-icons>
						</view>
					</view>
				</view>
			</block>
			<block v-else>
				<zhuige-nodata v-if="loaded"></zhuige-nodata>
			</block>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeNodata
		},
		data() {
			return {
				tags: [],
				loaded: false
			};
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			this.loadTags();
		},
		onShareAppMessage() {
			return {
				title: '资讯标签_' + getApp().globalData.appName,
				path: Util.addShareSource('pages/cms/tags/tags?n=n')
			};
		},
		methods: {
			clickTag(tag) {
				Util.openLink('/pages/cms/list/list?tag_id=' + tag.id + '&title=' + tag.name);
			},
			loadTags() {
				Rest.post(Api.URL('cms', 'tags')).then(res => {
					this.tags = res.data.tags;
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
	}

	.zhuige-tags-list {
		padding: 0 20rpx;
	}

	.zhuige-tags-block {
		justify-content: space-between;
		padding: 10rpx 0;
	}

	.zhuige-tags-block,
	.zhuige-tags-info {
		align-items: center;
		display: flex;
	}

	.zhuige-tags-info {
		width: 86%;
	}

	.zhuige-tags-info>view:nth-child(1) {
		height: 128rpx;
		width: 128rpx;
	}

	.zhuige-tags-info>view:nth-child(1) image {
		border-radius: 50%;
		height: 100%;
		width: 100%;
	}

	.zhuige-tags-info>view:nth-child(2) {
		padding-left: 20rpx;
	}

	.zhuige-tags-info>view:nth-child(2) view:nth-child(1) {
		font-size: 32rpx;
		font-weight: 600;
	}

	.zhuige-tags-info>view:nth-child(2) view:nth-child(2) {
		align-items: center;
		display: flex;
	}

	.zhuige-tags-info>view:nth-child(2) view:nth-child(2) text {
		background: #fff1ec;
		border-radius: 24rpx;
		color: #ff6146;
		font-size: 24rpx;
		font-weight: 300;
		height: 40rpx;
		line-height: 40rpx;
		padding: 0 32rpx;
	}

	.zhuige-classify-text {
		padding-left: 20rpx;
	}
</style>