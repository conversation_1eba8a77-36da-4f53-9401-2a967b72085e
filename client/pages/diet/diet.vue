<template>
	<view class="container">
		<!-- 上传区域 -->
		<view class="upload-section">
			<view class="upload-box" :class="{ 'has-image': imagePath }" @tap="previewImage(imagePath)">
				<image v-if="imagePath" :src="imagePath" class="preview-image" mode="aspectFill"></image>
				<view v-else class="upload-placeholder">
					<text class="upload-icon">🍽️</text>
					<text class="upload-text">点击上传或拍摄美食图片</text>
				</view>
			</view>
			<view class="action-buttons">
				<button @tap="chooseImage" class="action-btn photo-btn">
					<text class="btn-icon">🖼️</text>
					<text class="btn-text">相册选择</text>
				</button>
				<button @tap="takePhoto" class="action-btn camera-btn">
					<text class="btn-icon">📸</text>
					<text class="btn-text">拍照识别</text>
				</button>
			</view>
		</view>

		<!-- 识别结果 -->
		<scroll-view v-if="results.length > 0" class="result-scroll" scroll-y>
			<view class="result-card" v-for="(item, index) in results" :key="index">
				<view class="card-header">
					<text class="dish-name" @tap="copyText(item.name)">{{ item.name }}</text>
					<view class="calories-info">
						<text v-if="item.calories" class="calories-text">{{ item.calories_per_100g }}大卡/100g</text>
						<text v-if="item.calories" class="calories-tip">(标准份量约300g)</text>
					</view>
				</view>

				<view class="card-body">
					<view class="progress-container">
						<text class="progress-label">识别置信度</text>
						<view class="progress-bar">
							<view class="progress-fill" :style="{ width: (item.score * 100) + '%' }"></view>
							<text class="progress-text">{{ (item.score * 100).toFixed(1) }}%</text>
						</view>
					</view>

					<view class="exercise-section">
						<text class="section-title">🏃‍♂️ 运动消耗建议</text>
						<view class="exercise-list">
							<view class="exercise-item"
								v-for="(exercise, type) in getExerciseSuggestions(item.calories)" :key="type">
								<text class="exercise-name">{{ exercise.name }}</text>
								<text class="exercise-time">{{ exercise.time }}</text>
							</view>
						</view>
					</view>

					<view v-if="item.description" class="description-box">
						<text class="description-title">📝 菜品介绍</text>
						<view class="description-content">
							<image v-if="item.image_url" :src="item.image_url" class="image-url" mode="widthFix"
								:lazy-load="true" @error="handleImageError(index)" @load="handleImageLoad(index)"
								@tap.stop="previewImage(item.image_url)">
								<view v-if="item.image_url && !imageLoaded[index]" class="image-loading">🍽️ 努力加载中...
								</view>
								<view v-if="item.image_url && imageError[index]" class="image-error">
									<text>图片加载失败</text>
								</view>
							</image>
							<view class="description-container" @tap.stop="toggleDescription(index)">
								<text class="description-text" :class="{'expanded': item.isExpanded}">
									{{ item.description }}
								</text>
							</view>
							<view v-if="item.baike_url" class="source-link" @tap="copyBaikeUrl(item.baike_url)">
								<text class="link-icon">🌐</text>
								<text class="link-text">百科来源</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 广告容器 -->
		<view class="ad-container" v-if="adUnitId">
			<view class="ad-header">
				<text class="ad-icon">💪</text>
				<text class="ad-title">科学饮食，健康生活</text>
			</view>
			<view class="ad-content">
				<ad :unit-id="adUnitId" @error="onAdError" class="ad-component"></ad>
			</view>
			<view class="ad-footer">
				<text class="ad-tip">健康生活，从饮食开始</text>
			</view>
		</view>

		<!-- 广告列表容器 -->
		<view class="ad-list-container" v-if="adList && adList.length > 0">
			<view class="ad-list-header">
				<text class="ad-list-icon">🎯</text>
				<text class="ad-list-title">健康生活推荐</text>
			</view>
			<view class="ad-list-content">
				<swiper class="ad-swiper" :indicator-dots="true" :autoplay="true" :interval="5000" :duration="500"
					circular>
					<swiper-item v-for="(item, index) in adList" :key="index" class="ad-swiper-item"
						@tap="jumpToMiniProgram(item)">
						<image :src="item.imgUrl" mode="aspectFill" class="ad-swiper-image"></image>
					</swiper-item>
				</swiper>
			</view>
			<view class="ad-list-footer">
				<text class="ad-list-tip">更多健康生活小贴士</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		BAIDU_AI_CONFIG,
		EXERCISE_CONFIG
	} from '@/utils/config.js';

	export default {
		data() {
			return {
				imagePath: '',
				results: [],
				adUnitId: BAIDU_AI_CONFIG.AD_UNIT_ID,
				adList: [],
				imageLoaded: {},
				imageError: {}
			}
		},
		mounted() {
			this.fetchAdUnitId();
			this.fetchAdList();
		},
		methods: {
			async chooseImage() {
				try {
					const res = await uni.chooseImage({
						count: 1,
						sizeType: ['compressed']
					})
					this.imagePath = res.tempFilePaths[0]
					this.recognizeDish()
				} catch (err) {
					uni.showToast({
						title: '选择图片失败',
						icon: 'none'
					})
				}
			},

			async takePhoto() {
				try {
					const res = await uni.chooseImage({
						sourceType: ['camera'],
						sizeType: ['compressed']
					})
					this.imagePath = res.tempFilePaths[0]
					this.recognizeDish()
				} catch (err) {
					uni.showToast({
						title: '拍照失败',
						icon: 'none'
					})
				}
			},

			async getAccessToken() {
				try {
					const backendRes = await uni.request({
						url: BAIDU_AI_CONFIG.TOKEN_URL
					});
					return backendRes.data.access_token;
				} catch (backendError) {
					console.log('后端接口不可用，降级使用前端配置');
					const url =
						`https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${BAIDU_AI_CONFIG.API_KEY}&client_secret=${BAIDU_AI_CONFIG.SECRET_KEY}`;
					const res = await uni.request({
						url
					});
					return res.data.access_token;
				}
			},

			async recognizeDish() {
				uni.showLoading({
					title: '识别中...'
				})

				try {
					const access_token = await this.getAccessToken()
					const fileContent = await this.getFileBase64(this.imagePath)

					const res = await uni.request({
						url: BAIDU_AI_CONFIG.DISH_URL,
						method: 'POST',
						header: {
							'Content-Type': 'application/x-www-form-urlencoded'
						},
						data: {
							access_token,
							image: fileContent,
							baike_num: 5
						}
					})

					if (res.data.result) {
						this.results = res.data.result.map(item => ({
							name: item.name,
							score: parseFloat(item.probability) || 0,
							calories: parseFloat(item.calorie) || 0,
							calories_per_100g: parseFloat(item.calorie) || 0,
							description: item.baike_info?.description || '暂无描述信息',
							image_url: item.baike_info?.image_url || '',
							baike_url: item.baike_info?.baike_url || '',
							isExpanded: false
						}))

						if (this.results.length > 0) {
							const keyword = this.results[0].name;
							this.fetchAdList(keyword);
						}
					}
				} catch (err) {
					uni.showToast({
						title: '识别失败',
						icon: 'none'
					})
				}

				uni.hideLoading()
			},

			// 计算运动建议
			getExerciseSuggestions(calories) {
				const suggestions = {};
				// 假设一份菜品的标准重量为300g
				const standardWeight = 300;
				const totalCalories = (calories * standardWeight) / 100;
				const steps = Math.ceil(totalCalories / EXERCISE_CONFIG.CALORIES_PER_STEP);

				suggestions.WALKING = {
					name: '步行',
					time: `${Math.ceil(steps / 100)}分钟`
				};

				suggestions.RUNNING = {
					name: '跑步',
					time: `${Math.ceil(steps / 200)}分钟`
				};

				suggestions.CYCLING = {
					name: '骑行',
					time: `${Math.ceil(steps / 150)}分钟`
				};

				suggestions.SWIMMING = {
					name: '游泳',
					time: `${Math.ceil(steps / 250)}分钟`
				};

				return suggestions;
			},

			async fetchAdUnitId() {
				try {
					const res = await uni.request({
						url: BAIDU_AI_CONFIG.AD_UNIT_URL
					});
					if (res.data.ad_unit_id) {
						this.adUnitId = res.data.ad_unit_id;
					} else {
						this.adUnitId = BAIDU_AI_CONFIG.AD_UNIT_ID;
					}
				} catch (err) {
					console.log('广告接口异常:', err);
				}
			},

			onAdError(e) {
				console.error('广告加载失败:', e);
				this.adUnitId = '';
			},

			getFileBase64(filePath) {
				return new Promise((resolve) => {
					const fileManager = uni.getFileSystemManager()
					fileManager.readFile({
						filePath,
						encoding: 'base64',
						success: res => resolve(res.data)
					})
				})
			},

			previewImage(url) {
				if (!url) return;
				uni.previewImage({
					urls: [url],
					current: url,
					indicator: 'number'
				});
			},

			copyText(text) {
				uni.setClipboardData({
					data: text,
					success: () => {
						uni.showToast({
							title: '名称已复制',
							icon: 'success',
							duration: 1500
						});
					}
				});
			},

			async fetchAdList(keyword = '') {
				try {
					let url = `${BAIDU_AI_CONFIG.CUSTOM_AD_URL}?sourceType=diet`;
					if (keyword) {
						url += `&keyword=${encodeURIComponent(keyword)}`;
					}
					const res = await uni.request({
						url
					});
					if (res.data && Array.isArray(res.data)) {
						this.adList = res.data;
					}
				} catch (err) {
					console.log('广告列表获取失败:', err);
				}
			},

			jumpToMiniProgram(ad) {
				if (ad.jumpAppid && ad.jumpPagePath) {
					uni.navigateToMiniProgram({
						appId: ad.jumpAppid,
						path: ad.jumpPagePath,
						success(res) {
							console.log('跳转成功');
						},
						fail(err) {
							console.log('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				}
			},

			handleImageLoad(index) {
				this.$set(this.imageLoaded, index, true);
				this.$set(this.imageError, index, false);
			},

			handleImageError(index) {
				this.$set(this.imageError, index, true);
				this.$set(this.imageLoaded, index, false);
			},

			toggleDescription(index) {
				this.$set(this.results[index], 'isExpanded', !this.results[index].isExpanded);
			},

			copyBaikeUrl(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: '链接已复制',
							icon: 'success',
							duration: 2000
						});
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.container {
		padding: 20rpx;
		background: linear-gradient(180deg, #FFF5F5 0%, #FFFFFF 100%);
		min-height: 100vh;
	}

	.upload-section {
		margin: 30rpx 0;
		padding: 20rpx;
	}

	.upload-box {
		width: 100%;
		height: 400rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.08);
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		margin-bottom: 30rpx;

		&.has-image {
			height: 500rpx;

			.preview-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				transition: transform 0.3s ease;

				&:active {
					transform: scale(1.02);
				}
			}
		}
	}

	.upload-placeholder {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: rgba(255, 107, 107, 0.05);

		.upload-icon {
			font-size: 80rpx;
			margin-bottom: 20rpx;
		}

		.upload-text {
			font-size: 28rpx;
			color: #FF6B6B;
			opacity: 0.8;
		}
	}

	.action-buttons {
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		margin-top: 20rpx;
	}

	.action-btn {
		flex: 1;
		margin: 0 15rpx;
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 44rpx;
		border: none;
		transition: all 0.3s ease;

		&.photo-btn {
			background: linear-gradient(135deg, #FF9A9E 0%, #FAD0C4 100%);
		}

		&.camera-btn {
			background: linear-gradient(135deg, #FF6B6B 0%, #FF8E8E 100%);
		}

		&:active {
			transform: translateY(2rpx);
			opacity: 0.9;
		}

		.btn-icon {
			font-size: 36rpx;
			margin-right: 12rpx;
		}

		.btn-text {
			color: #FFFFFF;
			font-size: 28rpx;
			font-weight: 500;
		}
	}

	.result-card {
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 32rpx;
		margin: 20rpx 0;
		box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.08);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.12);
		}

		.card-header {
			position: relative;
			padding: 0 0 24rpx 88rpx;
			border-bottom: 2rpx solid #FFF5F5;
			margin-bottom: 24rpx;

			&::before {
				content: '🍽️';
				position: absolute;
				left: 0;
				top: -8rpx;
				font-size: 48rpx;
				width: 72rpx;
				height: 72rpx;
				background: rgba(255, 107, 107, 0.1);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.dish-name {
				font-size: 36rpx;
				color: #333;
				font-weight: 600;
				margin-right: 16rpx;
			}

			.calories-info {
				margin-top: 12rpx;

				.calories-text {
					font-size: 28rpx;
					color: #FF6B6B;
					font-weight: 500;
				}

				.calories-tip {
					font-size: 24rpx;
					color: #999;
					margin-left: 8rpx;
				}
			}
		}

		.card-body {
			.progress-container {
				margin-bottom: 24rpx;

				.progress-label {
					font-size: 28rpx;
					color: #666;
					margin-bottom: 12rpx;
					display: block;
				}

				.progress-bar {
					height: 24rpx;
					background: #FFF5F5;
					border-radius: 12rpx;
					overflow: hidden;
					position: relative;

					.progress-fill {
						height: 100%;
						background: linear-gradient(90deg, #FF6B6B 0%, #FF8E8E 100%);
						border-radius: 12rpx;
						transition: width 0.6s ease-out;
					}

					.progress-text {
						position: absolute;
						right: 24rpx;
						top: 50%;
						transform: translateY(-50%);
						font-size: 24rpx;
						color: #FFFFFF;
						text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.12);
					}
				}
			}

			.exercise-section {
				background: #FFF5F5;
				border-radius: 16rpx;
				padding: 24rpx;
				margin-bottom: 24rpx;

				.section-title {
					font-size: 30rpx;
					color: #FF6B6B;
					font-weight: 500;
					margin-bottom: 16rpx;
					display: block;
				}

				.exercise-list {
					display: grid;
					grid-template-columns: repeat(2, 1fr);
					gap: 16rpx;

					.exercise-item {
						background: #FFFFFF;
						border-radius: 12rpx;
						padding: 16rpx;
						text-align: center;

						.exercise-name {
							font-size: 28rpx;
							color: #333;
							font-weight: 500;
							display: block;
							margin-bottom: 8rpx;
						}

						.exercise-time {
							font-size: 24rpx;
							color: #FF6B6B;
						}
					}
				}
			}

			.description-box {
				background: #FFF5F5;
				border-radius: 16rpx;
				padding: 24rpx;

				.description-title {
					font-size: 30rpx;
					color: #FF6B6B;
					font-weight: 500;
					margin-bottom: 16rpx;
					display: block;
				}

				.description-content {
					position: relative;

					.image-url {
						max-width: 100%;
						width: auto;
						height: auto;
						border-radius: 16rpx;
						margin: 20rpx 0;
						display: block;
						animation: fadeIn 0.6s ease-in-out;
						opacity: 0;
						animation-fill-mode: forwards;
						overflow: hidden;
					}

					@keyframes fadeIn {
						from {
							opacity: 0;
							transform: translateY(20rpx);
						}

						to {
							opacity: 1;
							transform: translateY(0);
						}
					}

					.image-loading {
						color: #FF6B6B;
						font-size: 28rpx;
						padding: 20rpx;
					}

					.description-container {
						display: flex;
						align-items: flex-end;
						flex-wrap: nowrap;
					}

					.description-text {
						font-size: 28rpx;
						color: #666;
						line-height: 1.6;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 3;
						overflow: hidden;
						transition: all 0.3s;
						user-select: text;

						&.expanded {
							-webkit-line-clamp: unset;
						}
					}

					.source-link {
						margin-top: 20rpx;
						padding: 12rpx 20rpx;
						background: rgba(255, 107, 107, 0.1);
						border-radius: 8rpx;
						display: inline-flex;
						align-items: center;

						.link-icon {
							font-size: 24rpx;
							margin-right: 8rpx;
						}

						.link-text {
							font-size: 24rpx;
							color: #FF6B6B;
						}
					}
				}
			}
		}
	}

	/* 广告容器样式 */
	.ad-container {
		background: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.08);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.12);
		}

		.ad-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			padding: 0 8rpx;

			.ad-icon {
				font-size: 32rpx;
				margin-right: 8rpx;
			}

			.ad-title {
				font-size: 28rpx;
				color: #FF6B6B;
				font-weight: 500;
			}
		}

		.ad-content {
			background: #FFF5F5;
			border-radius: 12rpx;
			overflow: hidden;
			margin: 0;
			padding: 12rpx;
			min-height: 200rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.ad-component {
				width: 100%;
				height: 100%;
			}
		}

		.ad-footer {
			margin-top: 12rpx;
			text-align: center;

			.ad-tip {
				font-size: 22rpx;
				color: #999;
				opacity: 0.8;
			}
		}
	}

	/* 广告列表样式 */
	.ad-list-container {
		background: #fff;
		border-radius: 16rpx;
		padding: 20rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.08);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.12);
		}

		.ad-list-header {
			display: flex;
			align-items: center;
			margin-bottom: 16rpx;
			padding: 0 8rpx;

			.ad-list-icon {
				font-size: 32rpx;
				margin-right: 8rpx;
			}

			.ad-list-title {
				font-size: 28rpx;
				color: #FF6B6B;
				font-weight: 500;
			}
		}

		.ad-list-content {
			background: #FFF5F5;
			border-radius: 12rpx;
			overflow: hidden;
			margin: 0;
			padding: 12rpx;

			.ad-swiper {
				width: 100%;
				height: 600rpx;
				border-radius: 12rpx;
				overflow: hidden;

				.ad-swiper-item {
					width: 100%;
					height: 100%;

					.ad-swiper-image {
						width: 100%;
						height: 100%;
						object-fit: cover;
						border-radius: 12rpx;
					}
				}
			}
		}

		.ad-list-footer {
			margin-top: 12rpx;
			text-align: center;

			.ad-list-tip {
				font-size: 22rpx;
				color: #999;
				opacity: 0.8;
			}
		}
	}
</style>