<template>
	<view class="content">
		<view class="zhuige-classify" v-for="(cat, index) in cats" :key="index">
			<view class="zhuige-idle-classify-header">
				<view>
					<view>{{cat.name}}</view>
					<text>总计{{cat.count}}产品</text>
				</view>
				<view @click="clickCat(cat)">查看更多</view>
			</view>
			<view class="zhuige-idle-classify-cover">
				<view v-for="(goods, iGoods) in cat.list" :key="iGoods" @click="clickGoods(goods)">
					<image mode="aspectFill" :src="goods.thumb"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	export default {
		components: {},
		data() {
			return {
				cats: []
			}
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			this.loadSetting();
		},
		onShareAppMessage() {
			return {
				title: '闲置物品分类-' + getApp().globalData.appName,
				path: Util.addShareSource('pages/idle-shop/index/index?n=n')
			};
		},
		onShareTimeline() {
			return {
				title: '闲置物品分类-' + getApp().globalData.appName
			};
		},
		methods: {
			clickCat(cat) {
				Util.openLink('/pages/idle-shop/list/list?title=分类【' + cat.name + '】&cat_id=' + cat.id);
			},
			clickGoods(goods) {
				Util.openLink('/pages/idle-shop/detail/detail?id=' + goods.id);
			},
			loadSetting() {
				Rest.post(Api.URL('idle', 'setting_cat'), {}).then(res => {
					this.cats = res.data.cats;
				}, err => {
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
	}

	.content {
		padding: 0 30rpx 120rpx;
	}

	.zhuige-classify {
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
		padding: 22rpx 30rpx 30rpx;
	}

	.zhuige-idle-classify-header {
		justify-content: space-between;
	}

	.zhuige-idle-classify-header,
	.zhuige-idle-classify-header view:nth-child(1) {
		align-items: center;
		display: flex;
	}

	.zhuige-idle-classify-header view:nth-child(1) view {
		font-size: 36rpx;
		font-weight: 500;
		margin-right: 8rpx;
	}

	.zhuige-idle-classify-header view:nth-child(1) text {
		background: #fff1ed;
		border-radius: 1.8em;
		color: #ff6146;
		font-size: 24rpx;
		font-weight: 400;
		height: 1.8em;
		line-height: 1.8em;
		margin-bottom: 4rpx;
		padding: 0 24rpx;
	}

	.zhuige-idle-classify-header view:nth-child(2) {
		color: #777;
		font-size: 24rpx;
		font-weight: 300;
	}

	.zhuige-idle-classify-cover {
		align-items: center;
		display: flex;
	}

	.zhuige-idle-classify-cover view {
		border-radius: 12rpx;
		height: 200rpx;
		margin-right: 1.5%;
		width: 32%;
	}

	.zhuige-idle-classify-cover view:nth-child(3) {
		margin: 0;
	}

	.zhuige-idle-classify-cover view image {
		border-radius: 12rpx;
		height: 100%;
		width: 100%;
	}
</style>