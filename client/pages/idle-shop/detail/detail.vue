<template>
	<view class="content" v-if="goods">
		<view class="zhuige-detail-block">
			<view class="zhuige-block">
				<view class="zhuige-social-poster-blcok" v-if="goods.author">
					<view class="zhuige-social-poster" @click="clickUser(goods.author.user_id)">
						<view class="zhuige-social-poster-avatar">
							<image mode="aspectFill" :src="goods.author.avatar"></image>
						</view>
						<view class="zhuige-social-poster-info">
							<view>
								<text>{{goods.author.nickname}}</text>
								<image mode="aspectFill" :src="goods.author.certify.icon"
									v-if="goods.author.certify && goods.author.certify.status==1"></image>
								<image class="zhuige-social-vip" mode="aspectFill" :src="goods.author.vip.icon"
									v-if="goods.author.vip && goods.author.vip.status==1"></image>
							</view>
						</view>
					</view>
					<view class="zhuige-social-opt" @click="clickFollowUser(goods.author.user_id)">
						<view v-if="goods.author.is_follow">已关注</view>
						<view v-else>+ 关注</view>
					</view>
				</view>
				<view class="zhuige-idle-pr">
					<text>￥</text>
					<text>{{goods.price}}</text>
				</view>
				<view class="zhuige-idle-tit">{{goods.title}}</view>
				<view class="zhuige-detail-cont">
					<mp-html :content="goods.content"></mp-html>
				</view>
				<view class="zhuige-idle-goods-img" v-for="(image, index) in goods.images" :key="index">
					<image mode="widthFix" :src="image"></image>
				</view>
			</view>
		</view>

		<view class="zhuige-detail-share">
			<view class="zhuige-block">
				<view class="zhuige-detail-share-user">
					<view class="zhuige-detail-share-title">- {{goods.like_list.length}}人点赞 -</view>
					<view class="zhuige-detail-share-user-list" v-if="goods.like_list.length > 0">
						<view v-for="(user, index) in goods.like_list" :key="index" @click="clickUser(user.user_id)">
							<image mode="aspectFill" :src="user.avatar"></image>
						</view>
					</view>
				</view>
				<view class="zhuige-detail-share-opt">
					<view @click="clickPoster">
						<text>分享海报</text>
						<image mode="aspectFill" src="/static/poster.png"></image>
					</view>
				</view>
			</view>
		</view>

		<block v-if="goods.recs && goods.recs.length > 0">
			<view class="zhuige-idle-mark-head" v-if="goods.rec_cat">
				<view>闲置好物</view>
				<text @click="clickRecCat">查看更多</text>
			</view>
			<view class="zhuige-block">
				<view class="zhugie-info-block left-side" v-for="(item, index) in goods.recs" :key="index"
					@click="clickLink('/pages/idle-shop/detail/detail?id='+item.id)">
					<view class="zhugie-info-image">
						<image mode="aspectFill" :src="item.thumb"></image>
					</view>
					<view class="zhugie-info-text">
						<view class="zhugie-info-title">{{item.title}}</view>
						<view class="zhuige-info-price">
							<text>￥</text>
							<text>{{item.price}}</text>
						</view>
					</view>
				</view>
			</view>
		</block>

		<view class="zhuige-reply-list">
			<view class="zhuige-block-head">
				<view>近期回复</view>
				<view @click="clickAllComments">查看全部</view>
			</view>
			<view class="zhuige-block">
				<block v-if="comments.length > 0">
					<zhuige-reply v-for="(comment, index) in comments" :key="index" :item="comment"
						@clickReply="clickReply"></zhuige-reply>
				</block>
				<view class="zhuige-none-tips" v-else>
					<image mode="aspectFill" src="/static/404.png"></image>
					<view>暂无评论，抢个沙发</view>
				</view>
			</view>
		</view>

		<view class="zhuige-detail-comment">
			<view class="zhuige-detail-opt">
				<view @click="openComment(0)">
					<uni-badge :text="goods.comment_count.toString()" type="error" absolute="rightTop" :offset="[0,12]"
						:inverted="false">
						<uni-icons :type="goods.is_comment==1?'chat-filled':'chat'" size="24"
							:color="goods.is_comment==1?'#ff6146':'#444444'"></uni-icons>
					</uni-badge>
				</view>
				<view @click="clickLike">
					<uni-badge :text="goods.like_list.length.toString()" type="error" absolute="rightTop"
						:offset="[0,12]" :inverted="false">
						<uni-icons :type="goods.is_like==1?'hand-up-filled':'hand-up'" size="24"
							:color="goods.is_like==1?'#ff6146':'#444444'"></uni-icons>
					</uni-badge>
				</view>
				<view @click="clickFavorite">
					<uni-badge :text="goods.favorites.toString()" type="error" absolute="rightTop" :offset="[0,12]"
						:inverted="false">
						<uni-icons :type="goods.is_favorite==1?'star-filled':'star'" size="24"
							:color="goods.is_favorite==1?'#ff6146':'#444444'"></uni-icons>
					</uni-badge>
				</view>
			</view>
			<view class="zhuige-detail-opt-btn">
				<view @click="clickSell">卖同款</view>
				<view @click="clickBuy">我想要</view>
			</view>
		</view>

		<view class="zhuige-right-side-btn" style="top:800rpx;" v-if="goods.is_show_promotion==1"
			@click="clickPromotion">
			<text>付费推广</text>
		</view>
		<view class="zhuige-right-side-btn" style="top:960rpx;" v-if="goods.is_show_edit" @click="clickEdit">
			<text>信息修改</text>
		</view>

		<!-- #ifdef MP-WEIXIN || H5 -->
		<l-painter v-if="isShowPainter" isCanvasToTempFilePath custom-style="position: fixed; left: 200%;" :board="base"
			@success="onPainterSuccess" />
		<!-- #endif -->

		<uni-popup ref="popupComment" type="bottom">
			<view class="zhuige-sp-appraise-box" :style="'margin-bottom:'+comment_bottom+'px;'">
				<view class="zhuige-sp-appraise">
					<view class="zhuige-sp-appraise-title" v-if="parent_comment_id==0">
						<view>用户评价</view>
					</view>
					<view class="zhuige-sp-appraise-text">
						<textarea v-model="comment_content" placeholder="说点什么吧..." maxlength="140" :fixed="true"
							@keyboardheightchange="keyboardheightchange"></textarea>
					</view>
					<view class="zhuige-sp-appraise-btn" @click="clickCommentOK">
						<view>提交</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="popupQrcode" type="center">
			<view class="zhuige-pop-qr">
				<view>
					<image mode="aspectFill" :src="goods.author.weixin" :show-menu-by-longpress="true"></image>
				</view>
				<view>扫一扫/长按加好友</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeReply from "@/components/zhuige-reply";

	export default {
		components: {
			ZhuigeReply
		},
		data() {
			this.goods_id = undefined;
			this.acode = undefined;
			this.reply_user_id = 0;
			return {
				goods: undefined,
				comment_content: '',
				comment_bottom: 0,
				comments: [],
				loadMore: 'more',
				loaded: false,
				parent_comment_id: 0,
				comment_focus: false,
				isShowPainter: false,
				painterImage: '',
				poster: undefined,
				base: undefined,
				traffic_ad: undefined
			}
		},
		onLoad(options) {
			if (options.id) {
				this.goods_id = options.id;
			} else if (options.scene) {
				this.goods_id = options.scene;
			}

			if (this.goods_id) {
				Util.addShareScore(options.source);
				this.loadGoods();
				uni.$on('linktap', this.onMPHtmlLink);
				uni.$on('zhuige_event_user_login', this.onSetReload);
				uni.$on('zhuige_event_follow_user', this.onFlollowUser);
			} else {
				uni.reLaunch({
					url: '/pages/tabs/index/index'
				});
			}
		},
		onShow() {
			if (this.loginReload) {
				this.loginReload = false;
				this.loadGoods();
			}
		},
		onUnload() {
			uni.$off('zhuige_event_follow_user', this.onFlollowUser);
			uni.$off('zhuige_event_user_login', this.onSetReload);
			uni.$off('linktap', this.onMPHtmlLink);
		},
		onPullDownRefresh() {
			this.loadGoods();
		},
		onShareAppMessage() {
			let share = {
				path: Util.addShareSource('pages/idle-shop/detail/detail?id=' + this.goods_id)
			};
			if (this.goods) {
				if (this.goods.title) {
					share.title = this.goods.title + '-' + getApp().globalData.appName;
				}
				if (this.goods.images && this.goods.images.length > 0) {
					share.imageUrl = this.goods.images[0];
				}
			}
			return share;
		},
		onShareTimeline() {
			let share = {};
			if (this.goods) {
				if (this.goods.title) {
					share.title = this.goods.title + '-' + getApp().globalData.appName;
				}
				if (this.goods.images && this.goods.images.length > 0) {
					share.imageUrl = this.goods.images[0];
				}
			}
			return share;
		},
		methods: {
			onFlollowUser(data) {
				if (this.goods && this.goods.author && this.goods.author.user_id == data.user_id) {
					this.goods.author.is_follow = data.is_follow;
				}
			},
			onSetReload(data) {
				this.loginReload = true;
			},
			onMPHtmlLink(data) {
				if (data['data-link']) {
					Util.openLink(data['data-link']);
				}
			},
			clickLink(url) {
				Util.openLink(url);
			},
			clickPromotion() {
				Util.openLink('/pages/promotion/pay/pay?id=' + this.goods_id);
			},
			clickEdit() {
				Util.openLink('/pages/idle-shop/post/post?goods_id=' + this.goods_id);
			},
			clickRecCat() {
				Util.openLink('/pages/idle-shop/list/list?title=分类【' + this.goods.rec_cat.name + '】&cat_id=' + this.goods
					.rec_cat.id);
			},
			clickSell() {
				let url = '/pages/idle-shop/post/post';
				if (this.goods.rec_cat) {
					url += '?cat_id=' + this.goods.rec_cat.id;
				}
				Util.openLink(url);
			},
			clickBuy() {
				this.$refs.popupQrcode.open('center');
			},
			checkComment() {
				if (!this.goods.comment_switch) {
					Alert.error('评论已关闭');
					return false;
				}
				if (this.goods.comment_require_mobile2) {
					Util.openLink('/pages/user/login/login?type=mobile&tip=评论');
					return false;
				}
				if (this.goods.comment_require_avatar2) {
					Util.openLink('/pages/user/verify/verify?tip=评论');
					return false;
				}
				return true;
			},
			openComment(parent_id) {
				if (this.checkComment()) {
					this.parent_comment_id = parent_id;
					this.reply_user_id = 0;
					this.$refs.popupComment.open('bottom');
				}
			},
			clickReply(comment) {
				if (this.checkComment()) {
					this.parent_comment_id = comment.comment_id;
					this.reply_user_id = comment.user_id;
					this.$refs.popupComment.open('bottom');
				}
			},
			clickAllComments() {
				Util.openLink('/pages/base/comments/comments?post_id=' + this.goods_id);
			},
			clickUser(user_id) {
				Util.openLink('/pages/user/home/<USER>' + user_id);
			},
			clickFollowUser(user_id) {
				Rest.post(Api.URL('user', 'follow_user'), {
					user_id: this.goods.author.user_id
				}).then(res => {
					uni.$emit('zhuige_event_follow_user', {
						user_id: user_id,
						is_follow: res.data.is_follow
					});
				}, err => {
					console.log(err);
				});
			},
			loadGoods() {
				Rest.post(Api.URL('idle', 'detail'), {
					goods_id: this.goods_id
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.goods = res.data;
						if (res.data.poster) {
							this.poster = res.data.poster;
						}
						this.loadACode();
						this.loadComments();
					} else {
						Alert.toast(res.message);
					}
				}, err => {
					console.log(err);
				});
			},
			loadComments() {
				if (this.loadMore == 'loading') {
					return;
				}
				this.loadMore = 'loading';
				Rest.post(Api.URL('comment', 'index'), {
					post_id: this.goods_id,
					offset: this.comments.length
				}).then(res => {
					this.comments = this.comments.concat(res.data.comments);
					this.loadMore = res.data.comments.length > 0 ? 'more' : 'nomore';
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			},
			clickLike() {
				Rest.post(Api.URL('user', 'like'), {
					post_id: this.goods_id
				}).then(res => {
					this.goods.is_like = res.data.is_like;
					if (res.data.is_like) {
						this.goods.like_list.unshift(res.data.user);
					} else {
						let newList = [];
						this.goods.like_list.forEach(user => {
							if (user.user_id != res.data.user.user_id) {
								newList.push(user);
							}
						});
						this.goods.like_list = newList;
					}
				}, err => {
					console.log(err);
				});
			},
			clickFavorite() {
				Rest.post(Api.URL('user', 'favorite'), {
					type: 'post',
					post_id: this.goods_id
				}).then(res => {
					this.goods.is_favorite = res.data.is_favorite;
					this.goods.favorites = res.data.favorites;
				}, err => {
					console.log(err);
				});
			},
			clickCommentOK() {
				Rest.post(Api.URL('comment', 'add'), {
					post_id: this.goods_id,
					parent_id: this.parent_comment_id,
					reply_id: this.reply_user_id,
					content: this.comment_content
				}).then(res => {
					if (res.code == 0) {
						Alert.toast('审核后，他人可见');
						this.comment_id = 0;
						this.parent_comment_id = 0;
						this.reply_user_id = 0;
						this.comment_content = '';
						this.goods.is_comment = 1;
						this.$refs.popupComment.close();
					} else if (res.code == 'require_mobile') {
						Util.openLink('/pages/user/login/login?type=mobile&tip=评论');
					} else if (res.code == 'require_avatar') {
						Util.openLink('/pages/user/verify/verify?tip=评论');
					} else {
						Alert.toast(res.message);
					}
				}, err => {
					console.log(err);
				});
			},
			keyboardheightchange(e) {
				this.comment_bottom = e.detail.height;
			},
			clickPoster() {
				if (!this.poster) {
					Alert.error('请在后台配置海报背景');
					return;
				}

				// #ifndef MP-BAIDU
				if (this.painterImage) {
					uni.previewImage({
						urls: [this.painterImage]
					});
					return;
				}
				// #endif

				uni.showLoading({
					title: '海报生成中……'
				});

				this.isShowPainter = true;

				this.base = {
					css: {
						width: '750rpx',
						height: '1334rpx',
					},
					views: [{
							type: 'image',
							src: this.poster.background,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '0rpx',
								top: '0rpx',
								width: '750rpx',
								height: '1334rpx',
							}
						},
						{
							type: 'image',
							src: this.goods.author.avatar,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '50rpx',
								top: '85rpx',
								width: '100rpx',
								height: '100rpx',
								borderRadius: '50rpx',
							}
						},
						{
							type: 'text',
							text: this.goods.author.nickname,
							css: {
								position: 'absolute',
								left: '165rpx',
								top: '95rpx',
								width: '525rpx',
								color: '#010101',
								fontSize: '28rpx',
								fontWeight: 'bold',
								textAlign: 'left',
								lineClamp: 1,
							}
						},
						{
							type: 'text',
							text: this.goods.author.sign ? this.goods.author.sign : this.poster.title,
							css: {
								position: 'absolute',
								left: '165rpx',
								top: '140rpx',
								width: '525rpx',
								color: '#939393',
								fontSize: '22rpx',
								textAlign: 'left',
								lineClamp: 1,
							}
						},
						{
							type: 'view',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '240rpx',
								width: '690rpx',
								height: '860rpx',
								backgroundColor: '#FFFFFF',
								radius: '20rpx',
							}
						},
						{
							type: 'image',
							src: this.poster.thumb,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '30rpx',
								top: '240rpx',
								width: '690rpx',
								height: '520rpx',
								borderRadius: '20rpx 20rpx 0 0',
							}
						},
						{
							type: 'text',
							text: '￥' + this.goods.price,
							css: {
								position: 'absolute',
								left: '60rpx',
								top: '800rpx',
								width: '630rpx',
								color: '#ff6146',
								fontSize: '36rpx',
								textAlign: 'left',
								lineClamp: 1,
								fontWeight: 'bold'
							}
						},
						{
							type: 'text',
							text: this.goods.title,
							css: {
								position: 'absolute',
								left: '60rpx',
								top: '860rpx',
								width: '630rpx',
								color: '#000000',
								fontSize: '32rpx',
								textAlign: 'left',
								lineClamp: 2,
								fontWeight: 'bold'
							}
						},
						{
							type: 'image',
							src: this.acode,
							mode: 'aspectFill',
							css: {
								position: 'absolute',
								left: '570rpx',
								top: '950rpx',
								width: '120rpx',
								height: '120rpx',
								borderRadius: '10rpx',
							}
						},
						{
							type: 'text',
							text: '长按/扫一扫查看',
							css: {
								position: 'absolute',
								left: '60rpx',
								top: '1000rpx',
								width: '480rpx',
								color: '#939393',
								fontSize: '24rpx',
								textAlign: 'left',
								lineClamp: 1,
							}
						}
					]
				};
			},
			onPainterSuccess(image) {
				this.painterImage = image;
				uni.previewImage({
					urls: [image]
				});
				uni.hideLoading();
			},
			loadACode() {
				Rest.post(Api.URL('posts', 'wxacode'), {
					post_id: this.goods_id
				}).then(res => {
					this.acode = res.data.acode;
				}, err => {
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	.content {
		padding: 0 30rpx 180rpx;
	}

	.zhuige-detail-block .zhuige-block,
	.zhuige-detail-share .zhuige-block {
		padding: 30rpx;
	}

	.zhuige-social-poster-blcok {
		border-bottom: 1px solid #ddd;
		padding-bottom: 30rpx;
	}

	.zhuige-idle-pr {
		align-items: baseline;
		color: #ff6146;
		display: flex;
		font-size: 40rpx;
		font-weight: 600;
	}

	.zhuige-idle-pr text:nth-child(1) {
		font-size: 28rpx;
		font-weight: 300;
		margin-right: 8rpx;
	}

	.zhuige-idle-tit {
		font-size: 32rpx;
		font-weight: 500;
		line-height: 1.8em;
		margin-bottom: 20rpx;
	}

	.zhuige-idle-mark-head {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding-bottom: 12rpx;
	}

	.zhuige-idle-mark-head view {
		font-size: 30rpx;
		font-weight: 500;
	}

	.zhuige-idle-mark-head text {
		color: #999;
		font-size: 26rpx;
		font-weight: 400;
	}

	.zhugie-info-block {
		border-top: 1px solid #eee;
		padding: 20rpx 0;
	}

	.zhugie-info-block:nth-child(1) {
		border-top: 0;
	}

	.zhuige-info-price {
		align-items: baseline;
		color: #ff6146;
		display: flex;
		font-size: 40rpx;
		font-weight: 600;
	}

	.zhuige-info-price text:nth-child(1) {
		font-size: 28rpx;
		font-weight: 300;
		margin-right: 8rpx;
	}

	.zhuige-detail-comment {
		background: #fff;
		bottom: 0;
		box-shadow: 0rpx 0rpx 6rpx rgba(99, 99, 99, .1);
		height: 140rpx;
		justify-content: space-between;
		left: 0;
		padding-bottom: 20rpx;
		position: fixed;
		width: 100%;
		z-index: 20;
	}

	.zhuige-detail-comment,
	.zhuige-detail-opt {
		align-items: center;
		display: flex;
	}

	.zhuige-detail-opt {
		padding: 0 30rpx;
	}

	.zhuige-detail-opt>view {
		margin: 0 24rpx;
	}

	.zhuige-detail-opt-btn {
		align-items: center;
		display: flex;
		padding-right: 40rpx;
	}

	.zhuige-detail-opt-btn view {
		background: #ff6146;
		border-radius: 0 40rpx 40rpx 0;
		color: #fff;
		font-size: 26rpx;
		font-weight: 500;
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 50rpx;
	}

	.zhuige-detail-opt-btn view:nth-child(1) {
		background: #ffc24c;
		border-radius: 40rpx 0 0 40rpx;
	}

	.zhuige-sp-appraise-box {
		background: #f7f7f7;
		border-radius: 12rpx 12rpx 0 0;
		bottom: 0;
		position: fixed;
		width: 100%;
	}

	.zhuige-sp-appraise {
		padding: 30rpx;
	}

	.zhuige-sp-appraise-title {
		align-items: center;
		display: flex;
		justify-content: space-between;
		padding-bottom: 20rpx;
	}

	.zhuige-sp-appraise-text {
		background: #fff;
		border-radius: 12rpx;
		height: 240rpx;
		margin-bottom: 40rpx;
		padding: 30rpx;
	}

	.zhuige-sp-appraise-text textarea {
		height: 240rpx;
		line-height: normal;
		width: 100%;
	}

	.zhuige-sp-appraise-btn {
		align-items: center;
		display: flex;
		justify-content: center;
		padding: 0 30rpx 30rpx;
	}

	.zhuige-sp-appraise-btn view {
		background: #ff6146;
		border-radius: 12rpx;
		color: #fff;
		font-size: 26rpx;
		font-weight: 300;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		width: 60%;
	}

	.zhuige-pop-qr {
		background: #fff;
		border-radius: 20rpx;
	}

	.zhuige-pop-qr view:nth-child(1) {
		padding: 20rpx;
	}

	.zhuige-pop-qr image {
		height: 200px;
		width: 200px;
	}

	.zhuige-pop-qr view:nth-child(2) {
		background: #ff6146;
		border-radius: 0 0 20rpx 20rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: 400;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
	}

	.zhuige-right-side-btn {
		background: hsla(0, 0%, 100%, .9);
		border-radius: 64rpx 0 0 64rpx;
		box-shadow: 0 10rpx 16rpx -8rpx rgba(79, 125, 183, .3);
		color: #777;
		font-size: 24rpx;
		font-weight: 400;
		height: 64rpx;
		line-height: 64rpx;
		padding: 0 32rpx 0 42rpx;
		position: fixed;
		right: 0;
		z-index: 99;
	}

	.zhuige-idle-goods-img {
		padding: 20rpx 0;
		width: 100%;
	}

	.zhuige-idle-goods-img image {
		display: flex;
		width: 100%;
	}

	.zhuige-detail-cont {
		font-size: 1.1em;
	}
</style>