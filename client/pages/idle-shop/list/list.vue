<template>
	<view class="content">
		<block v-if="list.length > 0">
			<view class="zhuige-idel-block" v-for="(goods, index) in list" :key="index" @click="clickGoods(goods)">
				<view class="zhuige-idle-cover">
					<text v-if="goods.stick">推广</text>
					<image mode="aspectFill" :src="goods.thumb"></image>
				</view>
				<view class="zhuige-idle-info">
					<view class="zhuige-idle-info-title">{{goods.title}}</view>
					<view class="zhuige-idle-info-price">
						<text>￥</text>
						<text>{{goods.price}}</text>
					</view>
					<view class="zhuige-idle-info-act">
						<view>
							<image mode="aspectFill" :src="goods.user.avatar"></image>
							<text>{{goods.user.nickname}}</text>
						</view>
						<view>
							<text @click.stop="clickSell(goods.cat.id)">卖同款</text>
							<text @click.stop="clickBuy(goods.user.weixin)">我想要</text>
						</view>
					</view>
				</view>
			</view>
			<uni-load-more :status="loadMore"></uni-load-more>
		</block>
		<block v-else>
			<zhuige-nodata v-if="loaded"></zhuige-nodata>
		</block>

		<uni-popup ref="popupQrcode" type="center">
			<view class="zhuige-pop-qr">
				<view>
					<image mode="aspectFill" :src="qrcode" :show-menu-by-longpress="true"></image>
				</view>
				<view>扫一扫/长按加好友</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	import ZhuigeNodata from "@/components/zhuige-nodata";

	export default {
		components: {
			ZhuigeNodata
		},
		data() {
			this.title = '';
			this.cat_id = undefined;
			this.tag_id = undefined;
			this.search = undefined;
			return {
				list: [],
				loadMore: 'more',
				loaded: false,
				qrcode: undefined
			}
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			if (options.title) {
				this.title = options.title;
				uni.setNavigationBarTitle({
					title: decodeURIComponent(options.title)
				});
			}
			if (options.cat_id) {
				this.cat_id = options.cat_id;
			} else if (options.tag_id) {
				this.tag_id = options.tag_id;
			} else if (options.search) {
				this.search = options.search;
			}
			this.loadGoods(true);
		},
		onShareAppMessage() {
			let path = 'pages/idle-shop/list/list?title=' + this.title;
			if (this.cat_id) {
				path += '&cat_id=' + this.cat_id;
			} else if (this.tag_id) {
				path += '&tag_id=' + this.tag_id;
			} else if (this.search !== undefined) {
				path += '&search=' + this.search;
			}
			return {
				title: this.title + '-' + getApp().globalData.appName,
				path: Util.addShareSource(path)
			};
		},
		onShareTimeline() {
			return {
				title: this.title + '-' + getApp().globalData.appName
			};
		},
		onReachBottom() {
			if (this.loadMore == 'more') {
				this.loadGoods(false);
			}
		},
		methods: {
			clickGoods(goods) {
				Util.openLink('/pages/idle-shop/detail/detail?id=' + goods.id);
			},
			clickSell(cat_id) {
				let url = '/pages/idle-shop/post/post';
				if (cat_id) {
					url += '?cat_id=' + cat_id;
				}
				Util.openLink(url);
			},
			clickBuy(qrcode) {
				this.qrcode = qrcode;
				this.$refs.popupQrcode.open('center');
			},
			loadGoods(refresh) {
				if (this.loadMore == 'loading') {
					return;
				}
				this.loadMore = 'loading';

				let url = '';
				let params = {
					offset: this.list.length
				};

				if (this.cat_id !== undefined) {
					url = Api.URL('idle', 'cat');
					params.cat_id = this.cat_id;
				} else if (this.tag_id !== undefined) {
					url = Api.URL('idle', 'tag');
					params.tag_id = this.tag_id;
				} else if (this.search !== undefined) {
					url = Api.URL('idle', 'search');
					params.search = this.search;
				} else {
					url = Api.URL('idle', 'last');
				}

				Rest.post(url, params).then(res => {
					this.list = refresh ? res.data.list : this.list.concat(res.data.list);
					this.loadMore = res.data.more;
					this.loaded = true;
				}, err => {
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #f5f5f5;
	}

	.content {
		padding: 0 30rpx 120rpx;
	}

	.zhuige-idel-block {
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 30rpx;
	}

	.zhuige-idle-cover {
		border-radius: 12rpx 12rpx 0 0;
		height: 400rpx;
		overflow: hidden;
		position: relative;
		width: 100%;
	}

	.zhuige-idle-cover image {
		border-radius: 12rpx 12rpx 0 0;
		height: 100%;
		width: 100%;
	}

	.zhuige-idle-cover text {
		background: #ff6146;
		border-radius: 12rpx 0 12rpx 0;
		color: #fff;
		font-size: 22rpx;
		font-weight: 300;
		left: 0;
		padding: 4rpx 12rpx;
		position: absolute;
		top: 0;
		z-index: 3;
	}

	.zhuige-idle-info {
		padding: 30rpx;
	}

	.zhuige-idle-info-title {
		font-size: 30rpx;
		font-weight: 500;
		height: 1.8em;
		line-height: 1.8em;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-idle-info-price {
		align-items: baseline;
		border-bottom: 1px solid #eee;
		display: flex;
	}

	.zhuige-idle-info-price text:nth-child(1) {
		color: #363b51;
		font-size: 24rpx;
		font-weight: 400;
	}

	.zhuige-idle-info-price text:nth-child(2) {
		color: #363b51;
		font-size: 36rpx;
		font-weight: 600;
		margin-left: 8rpx;
	}

	.zhuige-idle-info-act {
		justify-content: space-between;
		padding: 28rpx 0 0;
	}

	.zhuige-idle-info-act,
	.zhuige-idle-info-act>view {
		align-items: center;
		display: flex;
	}

	.zhuige-idle-info-act view:nth-child(1) image {
		border-radius: 50%;
		height: 56rpx;
		margin-right: 12rpx;
		width: 56rpx;
	}

	.zhuige-idle-info-act view:nth-child(1) text {
		font-size: 28rpx;
		font-weight: 400;
	}

	.zhuige-idle-info-act view:nth-child(2) text {
		color: #fff;
		font-size: 28rpx;
		font-weight: 400;
		height: 72rpx;
		line-height: 72rpx;
		padding: 0 30rpx;
	}

	.zhuige-idle-info-act view:nth-child(2) text:nth-child(1) {
		background: #ffc24c;
		border-radius: 72rpx 0 0 72rpx;
		padding-left: 42rpx;
	}

	.zhuige-idle-info-act view:nth-child(2) text:nth-child(2) {
		background: #ff6146;
		border-radius: 0 72rpx 72rpx 0;
		padding-right: 42rpx;
	}

	.zhuige-pop-qr {
		background: #fff;
		border-radius: 20rpx;
	}

	.zhuige-pop-qr view:nth-child(1) {
		padding: 20rpx;
	}

	.zhuige-pop-qr image {
		height: 200px;
		width: 200px;
	}

	.zhuige-pop-qr view:nth-child(2) {
		background: #ff6146;
		border-radius: 0 0 20rpx 20rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: 400;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
	}
</style>