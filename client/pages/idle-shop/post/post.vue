<template>
	<view class="content">
		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-title-line">
					<input type="text" v-model="title" placeholder="请输入名称" />
				</view>
			</view>
		</view>

		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-input">
					<textarea v-model="content" placeholder="详细介绍有利于被推荐哦…" maxlength="140"></textarea>
				</view>
			</view>
		</view>

		<view class="zhuige-post-box">
			<view class="zhuige-upload-set">
				<view v-if="images.length < 9" @click="clickImage">
					<uni-icons type="plusempty" size="30" color="#777777"></uni-icons>
					<view>图片</view>
				</view>
				<view class="loaded" v-for="(image, index) in images" :key="index">
					<uni-icons type="clear" size="24" color="#FD6531" @click="clickDelImage(index)"></uni-icons>
					<image mode="aspectFill" :src="image.image.url"></image>
				</view>
			</view>
		</view>

		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-check">
					<view>选择分类：</view>
					<view>
						<picker :range="cat_names" :value="cur_cat" @change="onCatChange">
							<view>
								<view class="picker">{{cats && cats.length > 0 ? cats[cur_cat].name : ''}}</view>
								<uni-icons type="right" size="16" color="#BBBBBB"></uni-icons>
							</view>
						</picker>
					</view>
				</view>
			</view>
		</view>

		<view class="zhuige-post-box">
			<view class="zhuige-block">
				<view class="zhuige-post-line line-price">
					<view>销售价格：</view>
					<view>
						<input type="number" v-model="price" placeholder="如:9.99" />
					</view>
				</view>
			</view>
		</view>

		<view class="zhuige-creat-opt" v-if="fbxy">
			<label class="zhuige-cread-radio" @click="clickCheckFbxy">
				<radio :checked="checkFbxy" color="#111111" />
				我已阅读并同意<text @click.stop="clickFbxy">《发布协议》</text>
			</label>
		</view>

		<view class="zhuige-base-button" @click="clickOK">
			<view>确定</view>
		</view>
	</view>
</template>

<script>
	/*
	 * 追格小程序
	 * 作者: 追格
	 * 文档: https://www.zhuige.com/docs/zg.html
	 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
	 * github: https://github.com/zhuige-com/zhuige_xcx
	 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
	 */

	import Util from '@/utils/util';
	import Alert from '@/utils/alert';
	import Api from '@/utils/api';
	import Rest from '@/utils/rest';

	export default {
		components: {},
		data() {
			this.opt_cat_id = undefined;
			return {
				goods_id: undefined,
				cats: [],
				cat_names: [],
				cur_cat: 0,
				title: '',
				content: '',
				images: [],
				price: '',
				checkFbxy: false,
				fbxy: undefined
			}
		},
		onLoad(options) {
			Util.addShareScore(options.source);
			if (options.cat_id) {
				this.opt_cat_id = options.cat_id;
			}
			if (options.goods_id) {
				this.goods_id = options.goods_id;
				uni.setNavigationBarTitle({
					title: '编辑商品'
				});
			}
			this.preCreate();
		},
		onShareAppMessage() {
			return {
				title: this.shareTitle(),
				path: Util.addShareSource('pages/business-card/post/post?n=n')
			};
		},
		onShareTimeline() {
			return {
				title: this.shareTitle()
			};
		},
		methods: {
			shareTitle() {
				return (this.card_id ? '编辑商品' : '发布商品') + getApp().globalData.appName;
			},
			onCatChange(e) {
				this.cur_cat = parseInt(e.detail.value);
			},
			preCreate() {
				Rest.post(Api.URL('idle', 'create_pre'), {
					goods_id: this.goods_id
				}).then(res => {
					if (res.code == 0) {
						this.cats = res.data.cats;
						if (res.data.fbxy) {
							this.fbxy = res.data.fbxy;
						}

						// 构建分类名称数组
						for (let i = 0; i < this.cats.length; i++) {
							this.cat_names.push(this.cats[i].name);
							if (this.opt_cat_id && this.opt_cat_id == this.cats[i].id) {
								this.cur_cat = i;
							}
						}

						// 如果是编辑模式，填充数据
						if (res.data.goods) {
							let goods = res.data.goods;
							if (goods.cat_id) {
								for (let i = 0; i < this.cats.length; i++) {
									if (goods.cat_id == this.cats[i].id) {
										this.cur_cat = i;
										break;
									}
								}
							}
							if (goods.title) {
								this.title = goods.title;
							}
							if (goods.content) {
								this.content = goods.content;
							}
							if (goods.images) {
								this.images = goods.images;
							}
							if (goods.price) {
								this.price = goods.price;
							}
						}
					} else if (res.code == 'require_mobile') {
						uni.redirectTo({
							url: '/pages/user/info/info?tip=手机号'
						});
					} else if (res.code == 'require_avatar') {
						uni.redirectTo({
							url: '/pages/user/info/info?tip=头像昵称'
						});
					} else if (res.code == 'require_weixin') {
						uni.redirectTo({
							url: '/pages/user/info/info?tip=微信二维码'
						});
					} else {
						Alert.error(res.message);
						setTimeout(() => {
							Util.navigateBack();
						}, 1500);
					}
				}, err => {
					console.log(err);
				});
			},
			clickImage() {
				uni.chooseImage({
					count: 9 - this.images.length,
					sizeType: ['compressed'],
					success: (res) => {
						let tempFilePath = res.tempFilePaths[0];
						Rest.upload(Api.URL('other', 'upload2'), tempFilePath).then(res => {
							if (res.code == 0) {
								this.images.push({
									image: res.data
								});
							} else {
								Alert.error(res.message);
							}
						}, err => {
							Alert.error(err);
						});
					},
					fail: (err) => {
						if (err.errMsg && err.errMsg.indexOf('cancel') < 0) {
							Alert.error(err.errMsg);
						}
					}
				});
			},
			clickDelImage(index) {
				if (index >= 0 && index < this.images.length) {
					this.images.splice(index, 1);
				}
			},
			clickCheckFbxy() {
				this.checkFbxy = !this.checkFbxy;
			},
			clickFbxy() {
				Util.openLink(this.fbxy);
			},
			clickOK() {
				if (this.fbxy && !this.checkFbxy) {
					Alert.toast('请阅读并同意发布协议');
					return;
				}

				let content = this.content.replace(/\r\n/g, '<br/>').replace(/\n/g, '<br/>').replace(/\s/g, ' ');

				if (parseFloat(this.price) < 0.01) {
					Alert.toast('请设置价格');
					return;
				}

				let params = {
					title: this.title,
					content: Util.htmlEncode(content),
					images: JSON.stringify(this.images),
					price: this.price,
					cat_id: this.cats[this.cur_cat].id
				};

				if (this.goods_id) {
					params.goods_id = this.goods_id;
				}

				if (this.requesting) {
					return;
				}
				this.requesting = true;

				Rest.post(Api.URL('idle', 'create'), params).then(res => {
					this.requesting = false;
					if (res.code == 0) {
						Alert.toast('审核后，他人可见');
						setTimeout(() => {
							Util.navigateBack();
						}, 1500);
					} else if (res.code == 'require_mobile') {
						Util.openLink('/pages/user/info/info?tip=手机号');
					} else if (res.code == 'require_avatar') {
						Util.openLink('/pages/user/info/info?tip=头像昵称');
					} else if (res.code == 'require_weixin') {
						Util.openLink('/pages/user/info/info?tip=微信二维码');
					} else {
						Alert.error(res.message);
					}
				}, err => {
					this.requesting = false;
					console.log(err);
				});
			}
		}
	}
</script>

<style lang="scss">
	.content {
		height: 100%;
		overflow-y: scroll;
		position: fixed;
		width: 100%;
	}

	.zhuige-post-box {
		padding: 0 20rpx;
	}

	.zhuige-post-video-box {
		display: flex;
		flex-wrap: wrap;
		padding: 0 20rpx;
	}

	.zhuige-post-video-box video {
		margin-bottom: 20rpx;
		width: 100%;
	}

	.content .zhuige-post-box:nth-last-child(2) {
		margin-bottom: 180rpx;
	}

	.zhuige-post-line {
		align-items: center;
		display: flex;
		height: 2.2em;
		line-height: 2.2em;
	}

	.zhuige-post-line>view:nth-child(1) {
		font-size: 30rpx;
		font-weight: 400;
		width: 156rpx;
	}

	.zhuige-post-line>view:nth-child(2) {
		align-items: center;
		display: flex;
		max-width: 510rpx;
	}

	.zhuige-post-line view:nth-child(2) view {
		font-size: 28rpx;
		font-weight: 400;
		height: 1.6rem;
		line-height: 1.6rem;
		margin-right: 12rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.zhuige-post-line view:nth-child(2) input {
		text-align: left;
		width: 450rpx;
	}

	.zhuige-upload-set {
		align-items: center;
		display: flex;
		flex-wrap: wrap;
	}

	.zhuige-upload-set>view {
		background: #fff;
		border-radius: 12rpx;
		height: 154rpx;
		margin: 0 20rpx 20rpx 0;
		position: relative;
		text-align: center;
		width: 154rpx;
	}

	.zhuige-upload-set>view view {
		color: #777;
		font-size: 28rpx;
		font-weight: 400;
		height: 1em;
		line-height: 1em;
		margin-top: -28rpx;
	}

	.zhuige-upload-set view.loaded image {
		border-radius: 12rpx;
		height: 100%;
		width: 100%;
	}

	.zhuige-upload-set>view.loaded uni-icons {
		position: absolute;
		right: -20rpx;
		top: -46rpx;
		z-index: 3;
	}

	.zhuige-post-input textarea {
		font-size: 30rpx;
		height: 240rpx;
		line-height: normal;
		padding: 20rpx 0;
	}

	.zhuige-post-plug {
		align-items: center;
		justify-content: space-between;
	}

	.zhuige-post-opt,
	.zhuige-post-opt view,
	.zhuige-post-plug {
		display: flex;
		flex-wrap: nowrap;
	}

	.zhuige-post-opt view {
		align-items: center;
		line-height: 1.8em;
		margin-right: 8rpx;
		opacity: .5;
	}

	.zhuige-post-plug view.active {
		opacity: 1;
	}

	.zhuige-post-plug view image {
		height: 28rpx;
		margin-right: 8rpx;
		width: 28rpx;
	}

	.zhuige-post-plug view text {
		font-size: 28rpx;
		font-weight: 300;
		margin-left: 8rpx;
		white-space: nowrap;
	}

	.zhuige-post-count {
		color: #999;
	}

	.zhuige-post-title-line {
		width: 100%;
	}

	.zhuige-post-title-line input {
		font-size: 30rpx;
		width: 100%;
	}

	.zhuige-creat-opt {
		align-items: center;
		display: flex;
		justify-content: center;
		padding: 30rpx 30rpx 160rpx;
	}

	.zhuige-creat-tips {
		font-size: 26rpx;
		padding: 25rpx;
		text-align: center;
	}

	.zhuige-creat-tips text {
		color: #1d9ffc;
		padding: 10rpx;
	}

	.zhuige-cread-radio radio {
		transform: scale(.7);
	}
</style>