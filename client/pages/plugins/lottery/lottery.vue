<template>
	<view class="content">
		<!-- 导航栏 -->
		<uni-nav-bar 
			:fixed="true" 
			:opacity="nav_opacity" 
			:placeholder="false" 
			:statusBar="true" 
			:title="title" 
			leftIcon="back" 
			@clickLeft="clickBack">
		</uni-nav-bar>

		<view class="zhuige-lottery-bg" :style="background ? 'background: url(' + background + ') no-repeat center; background-size: cover;' : ''">
			<view class="zhuige-lottery-bg-box">
				<!-- 横幅 -->
				<view class="zhuige-lottery-banner" v-if="banner">
					<view @click="clickLink('/pages/points/task/task')">获取积分</view>
					<image mode="aspectFill" :src="banner.image" @click="clickLink(banner.link)"></image>
				</view>

				<!-- 转盘 -->
				<view class="zhuige-lottery-mod">
					<lucky-wheel 
						ref="myLucky"
						width="600rpx" 
						height="600rpx" 
						:blocks="blocks" 
						:prizes="prizes" 
						:buttons="buttons"
						@start="startCallBack" 
						@end="endCallBack">
					</lucky-wheel>
				</view>

				<!-- 信息栏 -->
				<view class="zhuige-lottery-info">
					<view>{{ '已有' + count + '人参与' }}</view>
					<view v-if="hdgz" @click="clickLink(hdgz)">活动规则</view>
					<view v-if="lxdj" @click="clickLink(lxdj)">联系兑奖</view>
				</view>

				<!-- 奖项说明 -->
				<view class="zhuige-lottery-tips zhuige-lottery-box">
					<view class="zhuige-lottery-title">
						<view>奖项说明</view>
						<text v-if="lottery_time">{{ '活动截止: ' + lottery_time.to }}</text>
					</view>
					<mp-html :content="explain"></mp-html>
				</view>

				<!-- 中奖记录 -->
				<view class="zhuige-lottery-group">// 中奖记录 //</view>
				<view class="zhuige-lottery-box">
					<!-- 中奖名单 -->
					<view class="zhuige-lottery-prize" v-if="lucy_logs && lucy_logs.length > 0">
						<view class="zhuige-lottery-title">中奖名单</view>
						<swiper 
							class="zhuige-lottery-list" 
							:autoplay="true" 
							:circular="true" 
							:duration="300" 
							:interval="4000" 
							:vertical="true">
							<swiper-item v-for="(item, index) in lucy_logs" :key="index">
								<view>
									<image mode="aspectFill" :src="item.avatar"></image>
									<text>{{ item.name + ' 参与了抽奖获得：' + item.prize }}</text>
								</view>
							</swiper-item>
						</swiper>
					</view>

					<!-- 抽奖记录 -->
					<view class="zhuige-lottery-draw" v-if="my_logs && my_logs.length > 0">
						<view class="zhuige-lottery-title">抽奖记录</view>
						<view class="zhuige-lottery-line">
							<view v-for="(item, index) in my_logs" :key="index">
								{{ item.createtime + ' 抽奖获得 ' + item.prize }}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import LuckyWheel from '@/components/@lucky-canvas/uni/lucky-wheel'
import Util from '@/utils/util';
import Api from '@/utils/api';
import Rest from '@/utils/rest';

export default {
	components: {
		LuckyWheel
	},
	data() {
		return {
			nav_opacity: 0,
			title: '',
			background: '',
			share_img: '',
			banner: null,
			explain: '',
			hdgz: '',
			lxdj: '',
			lottery_time: null,
			lottery_score: 0,
			lucy_logs: [],
			my_logs: [],
			count: 0,
			my_prize: null,
			blocks: [],
			prizes: [],
			buttons: [{
				radius: '35%',
				imgs: []
			}]
		}
	},
	onLoad(options) {
		// 添加分享积分
		if (options.source) {
			Util.addShareScore(options.source);
		}
		this.loadSetting();
	},
	onPageScroll(e) {
		this.nav_opacity = (e.scrollTop > 255 ? 255 : e.scrollTop) / 255;
		if (e.scrollTop > 20) {
			uni.setNavigationBarColor({
				frontColor: '#000000',
				backgroundColor: '#ffffff'
			});
		} else {
			uni.setNavigationBarColor({
				frontColor: '#ffffff',
				backgroundColor: '#ffffff'
			});
		}
	},
	onShareAppMessage() {
		let shareData = {
			title: this.title + '-' + getApp().globalData.appName,
			path: Util.addShareSource('pages/plugins/lottery/lottery?n=n')
		};
		if (this.share_img) {
			shareData.imageUrl = this.share_img;
		}
		return shareData;
	},
	onShareTimeline() {
		return {
			title: this.title + '-' + getApp().globalData.appName
		};
	},
	methods: {
		clickLink(url) {
			Util.openLink(url);
		},
		clickBack() {
			Util.navigateBack();
		},
		// 加载设置
		loadSetting() {
			Rest.post(Api.URL('lottery', 'setting'), {}).then(res => {
				this.title = res.data.title;
				this.background = res.data.background;
				this.share_img = res.data.share_img;
				this.banner = res.data.banner;
				this.explain = res.data.explain;
				this.hdgz = res.data.hdgz;
				this.lxdj = res.data.lxdj;
				this.lottery_time = res.data.lottery_time;
				this.lottery_score = res.data.lottery_score;
				this.count = res.data.count;
				this.lucy_logs = res.data.lucy_logs;
				this.my_logs = res.data.my_logs;

				// 配置奖品
				const prizes = res.data.prizes;
				this.prizes = []; // 清空现有奖品
				for (let i = 0; i < prizes.length; i++) {
					const prize = prizes[i];
					this.prizes.push({
						fonts: [{
							text: prize.name,
							top: '14%',
							fontSize: '14px',
							fontColor: '#FFBF00',
							fontWeight: '500'
						}],
						background: prize.bg_color || '#FFE4B5',
						imgs: prize.image ? [{
							src: prize.image,
							width: '22%',
							top: '44%'
						}] : []
					});
				}

				// 配置转盘背景
				this.blocks = []; // 清空现有背景
				if (res.data.lottery_bg) {
					this.blocks.push({
						padding: '32rpx',
						imgs: [{
							src: res.data.lottery_bg,
							width: '100%',
							height: '100%'
						}]
					});
				}

				// 配置抽奖按钮
				this.buttons = [{
					radius: '35%',
					imgs: res.data.lottery_chou ? [{
						src: res.data.lottery_chou,
						width: '80%',
						top: '-90%'
					}] : []
				}];
			}).catch(err => {
				console.log(err);
			});
		},
		// 加载记录
		loadLogs() {
			Rest.post(Api.URL('lottery', 'logs'), {}).then(res => {
				this.count = res.data.count;
				this.lucy_logs = res.data.lucy_logs;
				this.my_logs = res.data.my_logs;
			}).catch(err => {
				console.log(err);
			});
		},
		// 开始抽奖回调
		startCallBack() {
			if (this.lottery_score > 0) {
				uni.showModal({
					title: '提示',
					content: '抽奖需要支付' + this.lottery_score + '积分',
					success: (res) => {
						if (!res.cancel) {
							this.postDraw();
						}
					}
				});
			} else {
				this.postDraw();
			}
		},
		// 执行抽奖
		postDraw() {
			Rest.post(Api.URL('lottery', 'draw'), {}).then(res => {
				if (res.code == 0) {
					this.$refs.myLucky.play();
					setTimeout(() => {
						this.$refs.myLucky.stop(res.data.prize.index);
						this.my_prize = res.data.prize;
					}, 2000);
				} else {
					uni.showToast({
						title: res.message,
						icon: 'none'
					});
				}
			}).catch(err => {
				console.log(err);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			});
		},
		// 结束抽奖回调
		endCallBack(prize) {
			if (this.my_prize) {
				uni.showToast({
					title: '您抽中了' + this.my_prize.name,
					icon: 'success'
				});
				setTimeout(() => {
					this.loadLogs();
					this.my_prize = null;
				}, 2000);
			}
		}
	}
}
</script>

<style scoped>
.content {
	background: #f5f5f5;
}

.zhuige-lottery-bg {
	padding: 0 15px;
}

.zhuige-lottery-bg-box {
	padding: 90px 0 40px;
}

.zhuige-lottery-banner {
	height: 160px;
	margin-bottom: 0;
	position: relative;
	width: 100%;
}

.zhuige-lottery-banner view {
	background: #363b51;
	border-radius: 14px 0 0 14px;
	color: #fff;
	font-size: 24rpx;
	font-weight: 300;
	height: 28px;
	line-height: 28px;
	padding: 0 10px 0 14px;
	position: absolute;
	right: -15px;
	text-align: center;
	top: 30px;
}

.zhuige-lottery-banner image {
	border-radius: 3px;
	height: 100%;
	width: 100%;
}

.zhuige-lottery-mod {
	padding: 10px;
	text-align: center;
}

.zhuige-lottery-info {
	align-items: center;
	display: flex;
	justify-content: center;
	margin: 20px 0 30px;
}

.zhuige-lottery-info view {
	border-right: 1rpx solid #fff;
	color: #fff;
	font-size: 28rpx;
	font-weight: 400;
	height: 1.2em;
	line-height: 1.2em;
	padding: 0 40rpx;
	white-space: nowrap;
}

.zhuige-lottery-info view:last-of-type {
	border: none;
}

.zhuige-lottery-box {
	background: #fff;
	border-radius: 6px;
	margin-bottom: 15px;
	padding: 15px;
}

.zhuige-lottery-title {
	font-size: 32rpx;
	font-weight: 600;
}

.zhuige-lottery-tips .zhuige-lottery-title {
	align-items: center;
	display: flex;
	justify-content: space-between;
	margin-bottom: 10px;
}

.zhuige-lottery-tips .zhuige-lottery-title text {
	color: #666;
	font-size: 26rpx;
	font-weight: 400;
}

.zhuige-lottery-line view {
	border-bottom: 1rpx solid #ddd;
	color: #999;
	font-size: 28rpx;
	font-weight: 300;
	padding: 12px 0;
}

.zhuige-lottery-line view:last-of-type {
	border: none;
	padding-bottom: 4px;
}

.zhuige-lottery-group {
	color: #fff;
	font-size: 36rpx;
	font-weight: 600;
	padding: 10px 0 20px;
	text-align: center;
}

.zhuige-lottery-group + .zhuige-lottery-box {
	padding: 0;
}

.zhuige-lottery-prize {
	padding: 15px;
}

.zhuige-lottery-draw {
	border-top: 12px solid #eee;
	padding: 15px;
}

.zhuige-lottery-list {
	height: 50px;
	overflow: hidden;
}

.zhuige-lottery-list view {
	align-items: center;
	border-bottom: 1rpx solid #ddd;
	display: flex;
	padding: 15px 0;
}

.zhuige-lottery-list view:last-of-type {
	border: none;
	padding-bottom: 0;
}

.zhuige-lottery-list view image {
	border-radius: 50%;
	height: 48rpx;
	margin-right: 10px;
	width: 48rpx;
}

.zhuige-lottery-list view text {
	color: #999;
	font-size: 28rpx;
	font-weight: 400;
}
</style>
