<template>
	<view class="container">
		<!-- 顶部搜索栏 -->
		<view class="search-bar">
			<view class="search-input-wrapper">
				<text class="search-icon">🔍</text>
				<input class="search-input" v-model="searchText" placeholder="搜索识别类型"
					@input="filterCategories(searchText)" />
				<text v-if="searchText" class="clear-icon" @tap="clearSearch">✕</text>
			</view>
			<button class="search-btn" @tap="filterCategories(searchText)">搜索</button>
		</view>

		<!-- 分类入口 -->
		<view class="category-container">
			<view class="category-item" v-for="(item, index) in filterCategories(searchText)" :key="index"
				@tap="navigateTo(item)" :style="{ background: item.bgGradient }">
				<view class="category-icon-wrapper">
					<text class="category-icon">{{item.icon}}</text>
				</view>
				<text class="category-title">{{item.title}}</text>
				<text class="category-desc">{{item.description}}</text>
			</view>
		</view>

		<!-- 热门案例展示 -->
		<view class="hot-cases" v-if="cases && cases.length > 0">
			<view class="section-title">
				<text class="title-icon">🔥</text>
				<text class="title-text">热门识别案例</text>
				<text class="more-link" @tap="viewMore">查看更多</text>
			</view>
			<scroll-view class="case-scroll" scroll-x>
				<view class="case-item" v-for="(item, index) in cases" :key="index" @tap="viewCase(item)">
					<view class="case-image-wrapper">
						<image :src="item.image" class="case-image" mode="aspectFill" />
						<view class="case-tag">{{item.accuracy}}%</view>
					</view>
					<text class="case-name">{{item.name}}</text>
					<text class="case-count">{{item.count}}次识别</text>
				</view>
			</scroll-view>
		</view>

		<!-- 使用指南 -->
		<view class="guide-container">
			<view class="section-title">
				<text class="title-icon">📚</text>
				<text class="title-text">使用指南</text>
			</view>
			<view class="guide-content">
				<view class="guide-step" v-for="(step, index) in guideSteps" :key="index">
					<view class="step-number">{{index + 1}}</view>
					<view class="step-content">
						<text class="step-title">{{step.title}}</text>
						<text class="step-desc">{{step.desc}}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 广告位 -->
		<view class="ad-container" v-if="adUnitId">
			<view class="ad-header">
				<text class="ad-icon">🌱</text>
				<text class="ad-title">发现更多识别知识</text>
			</view>
			<view class="ad-content">
				<ad :unit-id="adUnitId" @error="onAdError" class="ad-component"></ad>
			</view>
			<view class="ad-footer">
				<text class="ad-tip">我是一个有温度的广告</text>
			</view>
		</view>

		<!-- 广告列表容器 -->
		<view class="ad-list-container" v-if="adList && adList.length > 0">
			<view class="ad-list-header">
				<text class="ad-list-icon">🎯</text>
				<text class="ad-list-title">精选推荐</text>
			</view>
			<view class="ad-list-content">
				<swiper class="ad-swiper" :indicator-dots="true" :autoplay="true" :interval="5000" :duration="500"
					circular>
					<swiper-item v-for="(item, index) in adList" :key="index" class="ad-swiper-item"
						@tap="jumpToMiniProgram(item)">
						<image :src="item.imgUrl" mode="aspectFill" class="ad-swiper-image"></image>
					</swiper-item>
				</swiper>
			</view>
			<view class="ad-list-footer">
				<text class="ad-list-tip">更多精彩内容等你发现</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		BAIDU_AI_CONFIG
	} from '@/utils/config.js';

	export default {
		data() {
			return {
				categories: [{
						icon: '🌿',
						title: '植物识别',
						description: '识别各类植物、花卉、树木',
						bgGradient: 'linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%)',
						path: '/pages/plant/plant'
					},
					{
						icon: '🐾',
						title: '动物识别',
						description: '识别各类动物、宠物、野生动物',
						bgGradient: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
						path: '/pages/animal/animal'
					},
					{
						icon: '🍽️',
						title: 'AI减肥相机',
						description: '识别卡路里，科学饮食',
						bgGradient: 'linear-gradient(135deg, #f1f8e9 0%, #dcedc8 100%)',
						path: '/pages/diet/diet'
					},
					{
						icon: '🌸',
						title: '花卉识别',
						description: '专业的花卉识别与养护知识',
						bgGradient: 'linear-gradient(135deg, #fce4ec 0%, #f8bbd0 100%)',
						path: '/pages/plant/plant'
					}
				],
				cases: [{
						name: '樱花',
						image: 'https://ai-public-console.cdn.bcebos.com/portal-pc-static/1742892956837/images/technology/imagerecognition/plant/1.jpg',
						accuracy: 98,
						count: 1234
					},
					{
						name: '向日葵',
						image: 'https://ai-public-console.cdn.bcebos.com/portal-pc-static/1742892956837/images/technology/imagerecognition/plant/2.jpg',
						accuracy: 95,
						count: 986
					},
					{
						name: '猕猴',
						image: 'https://ai-public-console.cdn.bcebos.com/portal-pc-static/1742892956837/images/technology/imagerecognition/animal/4.jpg',
						accuracy: 99,
						count: 2345
					},
					{
						name: '风景',
						image: 'https://ai-public-console.cdn.bcebos.com/portal-pc-static/1742892956837/images/technology/imagerecognition/general/2.jpg',
						accuracy: 97,
						count: 1876
					},
					{
						name: '胡萝卜',
						image: 'https://ai-public-console.cdn.bcebos.com/portal-pc-static/1742892956837/images/technology/imagerecognition/ingredient/2.jpg',
						accuracy: 99,
						count: 2345
					},
					{
						name: '红鹳',
						image: 'https://ai-public-console.cdn.bcebos.com/portal-pc-static/1742892956837/images/technology/imagerecognition/animal/3.jpg',
						accuracy: 97,
						count: 1876
					}
				],
				guideSteps: [{
						title: '选择识别类型',
						desc: '根据需求选择植物、动物等识别类型'
					},
					{
						title: '上传图片',
						desc: '支持拍照或从相册选择图片进行识别'
					},
					{
						title: '查看结果',
						desc: '获取详细的识别结果和相关知识'
					}
				],
				adUnitId: BAIDU_AI_CONFIG.AD_UNIT_ID,
				searchText: '',
				adList: [] // 广告列表数据
			}
		},
		methods: {
			navigateTo(item) {
				if (item && item.path) {
					uni.navigateTo({
						url: item.path
					})
				}
			},
			async fetchAdUnitId() {
				try {
					const res = await uni.request({
						url: BAIDU_AI_CONFIG.AD_UNIT_URL
					});
					if (res.data.ad_unit_id) {
						this.adUnitId = res.data.ad_unit_id;
					} else {
						this.adUnitId = BAIDU_AI_CONFIG.AD_UNIT_ID;
					}
				} catch (err) {
					console.log('广告接口异常:', err);
				}
			},
			onAdError(e) {
				console.error('广告加载失败:', e);
				this.adUnitId = '';
			},
			filterCategories(searchText) {
				if (!searchText) {
					return this.categories;
				}
				return this.categories.filter(item =>
					item.title.includes(searchText)
				);
			},
			clearSearch() {
				this.searchText = '';
				this.filterCategories('');
			},
			viewMore() {
				uni.navigateTo({
					url: '/pages/hot/hot'
				});
			},
			viewCase(item) {
				uni.navigateTo({
					url: `/pages/detail/detail?id=${item.id}`
				});
			},
			// 获取广告列表
			async fetchAdList(keyword = '') {
				try {
					let url = `${BAIDU_AI_CONFIG.CUSTOM_AD_URL}?sourceType=index`;
					if (keyword) {
						url += `&keyword=${encodeURIComponent(keyword)}`;
					}
					const res = await uni.request({
						url
					});
					if (res.data && Array.isArray(res.data)) {
						this.adList = res.data;
					}
				} catch (err) {
					console.log('广告列表获取失败:', err);
				}
			},
			// 跳转到指定小程序
			jumpToMiniProgram(ad) {
				if (ad.jumpAppid && ad.jumpPagePath) {
					uni.navigateToMiniProgram({
						appId: ad.jumpAppid,
						path: ad.jumpPagePath,
						success(res) {
							console.log('跳转成功');
						},
						fail(err) {
							console.log('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				}
			}
		},
		onLoad() {
			this.fetchAdUnitId();
			this.fetchAdList(); // 获取广告列表
		}
	}
</script>

<style lang="scss">
	.container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.search-bar {
		display: flex;
		margin-bottom: 40rpx;
		gap: 24rpx;

		.search-input-wrapper {
			flex: 1;
			height: 88rpx;
			padding: 0 24rpx;
			background: #fff;
			border-radius: 44rpx;
			border: 1rpx solid #07c160;
			box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.1);
			display: flex;
			align-items: center;

			.search-icon {
				font-size: 40rpx;
				color: #999;
				margin-right: 12rpx;
			}

			.search-input {
				flex: 1;
				height: 100%;
				font-size: 32rpx;
			}

			.clear-icon {
				font-size: 40rpx;
				color: #999;
				padding: 12rpx;
			}
		}

		.search-btn {
			width: 160rpx;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			background: linear-gradient(135deg, #07c160 0%, #0ab956 100%);
			color: #fff;
			border-radius: 44rpx;
			box-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.2);
			transition: all 0.2s ease;
			font-size: 32rpx;

			&:active {
				transform: scale(0.98);
				opacity: 0.9;
			}
		}
	}

	.category-container {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 24rpx;
		margin-bottom: 40rpx;

		.category-item {
			height: 240rpx;
			border-radius: 20rpx;
			padding: 36rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
			transition: all 0.3s ease;
			display: flex;
			flex-direction: column;
			justify-content: center;

			&:active {
				transform: scale(0.98);
			}

			.category-icon-wrapper {
				width: 88rpx;
				height: 88rpx;
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 24rpx;

				.category-icon {
					font-size: 44rpx;
					color: #07c160;
				}
			}

			.category-title {
				font-size: 36rpx;
				font-weight: 500;
				color: #333;
				margin-bottom: 12rpx;
			}

			.category-desc {
				font-size: 28rpx;
				color: #666;
			}
		}
	}

	.hot-cases {
		background: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;

			.title-icon {
				font-size: 40rpx;
				margin-right: 12rpx;
			}

			.title-text {
				flex: 1;
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}

			.more-link {
				font-size: 28rpx;
				color: #07c160;
			}
		}

		.case-scroll {
			white-space: nowrap;
		}

		.case-item {
			display: inline-block;
			width: 200rpx;
			margin-right: 24rpx;

			.case-image-wrapper {
				position: relative;
				width: 200rpx;
				height: 200rpx;
				border-radius: 12rpx;
				overflow: hidden;

				.case-image {
					width: 100%;
					height: 100%;
					transition: transform 0.3s ease;
				}

				.case-tag {
					position: absolute;
					top: 12rpx;
					right: 12rpx;
					background: rgba(7, 193, 96, 0.9);
					color: #fff;
					font-size: 24rpx;
					padding: 6rpx 16rpx;
					border-radius: 24rpx;
				}
			}

			.case-name {
				display: block;
				text-align: center;
				margin-top: 12rpx;
				font-size: 28rpx;
				color: #333;
			}

			.case-count {
				display: block;
				text-align: center;
				font-size: 24rpx;
				color: #999;
			}
		}
	}

	.guide-container {
		background: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-top: 40rpx;
		margin-bottom: 40rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;

			.title-icon {
				font-size: 40rpx;
				margin-right: 12rpx;
			}

			.title-text {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}
		}

		.guide-content {
			padding: 24rpx 0;

			.guide-step {
				display: flex;
				align-items: flex-start;
				margin-bottom: 24rpx;
				padding: 24rpx;
				background: #f8f8f8;
				border-radius: 16rpx;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.98);
				}

				&:last-child {
					margin-bottom: 0;
				}

				.step-number {
					width: 48rpx;
					height: 48rpx;
					line-height: 48rpx;
					text-align: center;
					background: linear-gradient(135deg, #07c160 0%, #0ab956 100%);
					color: #fff;
					border-radius: 50%;
					margin-right: 24rpx;
					font-size: 28rpx;
					flex-shrink: 0;
				}

				.step-content {
					flex: 1;

					.step-title {
						font-size: 32rpx;
						color: #333;
						font-weight: 500;
						margin-bottom: 12rpx;
						display: block;
					}

					.step-desc {
						font-size: 28rpx;
						color: #666;
						display: block;
					}
				}
			}
		}
	}

	.ad-container {
		background: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
		}

		.ad-header {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			padding: 0 12rpx;

			.ad-icon {
				font-size: 40rpx;
				margin-right: 12rpx;
			}

			.ad-title {
				font-size: 32rpx;
				color: #07c160;
				font-weight: 500;
			}
		}

		.ad-content {
			background: #F8FCF9;
			border-radius: 16rpx;
			overflow: hidden;
			margin: 0;
			padding: 16rpx;
			min-height: 240rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.ad-component {
				width: 100%;
				height: 100%;
			}
		}

		.ad-footer {
			margin-top: 16rpx;
			text-align: center;

			.ad-tip {
				font-size: 26rpx;
				color: #999;
				opacity: 0.8;
			}
		}
	}

	/* 广告列表样式 */
	.ad-list-container {
		background: #fff;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
		}

		.ad-list-header {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;
			padding: 0 12rpx;

			.ad-list-icon {
				font-size: 40rpx;
				margin-right: 12rpx;
			}

			.ad-list-title {
				font-size: 32rpx;
				color: #07c160;
				font-weight: 500;
			}
		}

		.ad-list-content {
			background: #F8FCF9;
			border-radius: 16rpx;
			overflow: hidden;
			margin: 0;
			padding: 16rpx;

			.ad-swiper {
				width: 100%;
				height: 680rpx;
				border-radius: 16rpx;
				overflow: hidden;

				.ad-swiper-item {
					width: 100%;
					height: 100%;

					.ad-swiper-image {
						width: 100%;
						height: 100%;
						object-fit: cover;
						border-radius: 16rpx;
					}
				}
			}
		}

		.ad-list-footer {
			margin-top: 16rpx;
			text-align: center;

			.ad-list-tip {
				font-size: 26rpx;
				color: #999;
				opacity: 0.8;
			}
		}
	}
</style>