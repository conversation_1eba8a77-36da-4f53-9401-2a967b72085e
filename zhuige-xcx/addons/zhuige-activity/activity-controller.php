<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

class ZhuiGe_Xcx_Activity_Controller extends ZhuiGe_Xcx_Base_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->module = 'activity';
        $this->routes = [
            'setting' => 'get_setting',
            'last' => 'get_last',
            'cat' => 'get_by_category',
            'detail' => 'get_detail',
            'form' => ['callback' => 'get_form', 'auth' => 'login'],
            'enroll' => ['callback' => 'post_enroll', 'auth' => 'login'],
            'wx_enroll_pay' => ['callback' => 'wx_enroll_pay', 'auth' => 'login'],
            'my_act' => ['callback' => 'get_my_act', 'auth' => 'login'],
        ];
    }

    // 获取活动设置
    public function get_setting($request)
    {
        $data = array();

        // 获取轮播图设置
        $slides_org = ZhuiGe_Xcx::option_value('activity_slides');
        $slides = array();
        if (is_array($slides_org)) {
            foreach ($slides_org as $item) {
                // 检查开关状态
                $switch_on = isset($item['switch']) && ($item['switch'] === '1' || $item['switch'] === 1 || $item['switch'] === true);
                if ($switch_on && !empty($item['image']) && !empty($item['image']['url'])) {
                    $slides[] = array(
                        'image' => $item['image']['url'],
                        'link' => isset($item['link']) ? $item['link'] : ''
                    );
                }
            }
        }
        $data['slides'] = $slides;

        // 获取导航分类
        $data['nav_cats'] = zhuige_activity_get_nav_categories();

        // 获取分享图片
        $activity_share_img = ZhuiGe_Xcx::option_value('activity_share_img');
        if ($activity_share_img) {
            $data['share_img'] = ZhuiGe_Xcx::option_image_url($activity_share_img);
        } else {
            $data['share_img'] = '';
        }

        return $this->success($data);
    }

    // 获取最新活动列表
    public function get_last($request)
    {
        $offset = $this->param_int($request, 'offset', 0);
        $limit = 10;

        $args = array(
            'post_type' => 'zhuige_activity',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $posts = get_posts($args);
        $activities = array();

        foreach ($posts as $post) {
            $activity = zhuige_activity_format($post);
            $activities[] = $activity;
        }

        // 检查是否还有更多数据
        $has_more = count($posts) >= $limit;

        return $this->success(array(
            'list' => $activities,
            'more' => $has_more ? 'more' : 'noMore'
        ));
    }

    // 按分类获取活动列表
    public function get_by_category($request)
    {
        $cat_id = $this->param_int($request, 'cat_id', 0);
        $offset = $this->param_int($request, 'offset', 0);
        $limit = 10;

        $args = array(
            'post_type' => 'zhuige_activity',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        // 如果指定了分类且不是"全部"分类
        if ($cat_id > 0) {
            $args['tax_query'] = array(
                array(
                    'taxonomy' => 'zhuige_activity_cat',
                    'field'    => 'term_id',
                    'terms'    => $cat_id,
                )
            );
        }

        $posts = get_posts($args);
        $activities = array();

        foreach ($posts as $post) {
            $activity = zhuige_activity_format($post);
            $activities[] = $activity;
        }

        // 检查是否还有更多数据
        $has_more = count($posts) >= $limit;

        return $this->success(array(
            'list' => $activities,
            'more' => $has_more ? 'more' : 'noMore'
        ));
    }

    // 获取活动详情
    public function get_detail($request)
    {
        $act_id = $this->param_int($request, 'act_id', 0);

        if (!$act_id) {
            return $this->error('活动ID不能为空');
        }

        $post = get_post($act_id);
        if (!$post || $post->post_type !== 'zhuige_activity' || $post->post_status !== 'publish') {
            return $this->error('活动不存在或已下线');
        }

        try {
            $activity = zhuige_activity_format($post);

            // 验证返回数据的完整性
            if (!$activity || !isset($activity['id'])) {
                return $this->error('活动数据获取失败');
            }

            // 为详情页添加相关推荐（根据标签和分类获取相关活动）
            $recs = array();

            // 获取当前活动的标签和分类
            $tags = get_the_terms($post->ID, 'zhuige_activity_tag');
            $cats = get_the_terms($post->ID, 'zhuige_activity_cat');

            $tag_ids = array();
            $cat_ids = array();

            if (!is_wp_error($tags) && !empty($tags)) {
                $tag_ids = wp_list_pluck($tags, 'term_id');
            }
            if (!is_wp_error($cats) && !empty($cats)) {
                $cat_ids = wp_list_pluck($cats, 'term_id');
            }

            // 构建查询参数
            $rec_args = array(
                'post_type' => 'zhuige_activity',
                'post_status' => 'publish',
                'posts_per_page' => 3,
                'post__not_in' => array($post->ID), // 排除当前活动
                'orderby' => 'date',
                'order' => 'DESC'
            );

            // 如果有标签或分类，优先按标签和分类查询
            if (!empty($tag_ids) || !empty($cat_ids)) {
                $tax_query = array('relation' => 'OR');

                if (!empty($tag_ids)) {
                    $tax_query[] = array(
                        'taxonomy' => 'zhuige_activity_tag',
                        'field' => 'term_id',
                        'terms' => $tag_ids
                    );
                }

                if (!empty($cat_ids)) {
                    $tax_query[] = array(
                        'taxonomy' => 'zhuige_activity_cat',
                        'field' => 'term_id',
                        'terms' => $cat_ids
                    );
                }

                $rec_args['tax_query'] = $tax_query;
            }

            $rec_posts = get_posts($rec_args);

            // 如果相关活动不足3个，补充最新的活动
            if (count($rec_posts) < 3) {
                $additional_args = array(
                    'post_type' => 'zhuige_activity',
                    'post_status' => 'publish',
                    'posts_per_page' => 3 - count($rec_posts),
                    'post__not_in' => array_merge(array($post->ID), wp_list_pluck($rec_posts, 'ID')),
                    'orderby' => 'date',
                    'order' => 'DESC'
                );

                $additional_posts = get_posts($additional_args);
                $rec_posts = array_merge($rec_posts, $additional_posts);
            }

            // 格式化推荐活动数据，符合前端 zhuige-scroll-ad 组件的数据结构
            foreach ($rec_posts as $rec_post) {
                $rec_activity = zhuige_activity_format($rec_post);
                $recs[] = array(
                    'image' => $rec_activity['thumbnail'], // 前端组件需要的字段
                    'title' => $rec_activity['title'],
                    'link' => '/pages/activity/detail/detail?act_id=' . $rec_activity['id'], // 跳转链接
                    'subtitle' => $rec_activity['time']['from'] . ' - ' . $rec_activity['time']['to'], // 活动时间作为副标题
                    'price' => $rec_activity['cost'] > 0 ? $rec_activity['cost'] : null, // 费用，免费时不显示
                    'badge' => $rec_activity['is_end'] ? '已结束' : null // 状态标识
                );
            }

            $activity['recs'] = $recs;

            // 为详情页添加报名用户列表
            global $wpdb;
            $enroll_table = $wpdb->prefix . 'zhuige_activity_enrolls';
            $users = array();

            // 检查表是否存在
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enroll_table'") == $enroll_table;
            if ($table_exists) {
                $recent_users = $wpdb->get_results($wpdb->prepare(
                    "SELECT DISTINCT u.ID, u.display_name
                     FROM {$wpdb->users} u
                     INNER JOIN $enroll_table e ON u.ID = e.user_id
                     WHERE e.activity_id = %d AND e.status = 1
                     ORDER BY e.time DESC
                     LIMIT 10",
                    $post->ID
                ));

                foreach ($recent_users as $user) {
                    $users[] = array(
                        'id' => intval($user->ID),
                        'nickname' => get_user_meta($user->ID, 'nickname', true) ?: $user->display_name ?: '用户' . $user->ID,
                        'avatar' => ZhuiGe_Xcx::user_avatar($user->ID)
                    );
                }
            }
            $activity['users'] = $users;

            // 确保必要字段存在
            $required_fields = ['id', 'title', 'content', 'thumbnail', 'property'];
            foreach ($required_fields as $field) {
                if (!isset($activity[$field])) {
                    $activity[$field] = $this->get_default_value($field);
                }
            }

            // 确保property字段的完整性
            if (!isset($activity['property']['address'])) {
                $activity['property']['address'] = array(
                    'address' => '',
                    'latitude' => '0',
                    'longitude' => '0'
                );
            }

            return $this->success($activity);

        } catch (Exception $e) {
            error_log('Activity detail error: ' . $e->getMessage());
            return $this->error('获取活动详情时发生错误');
        }
    }

    // 获取字段默认值
    private function get_default_value($field)
    {
        $defaults = array(
            'id' => 0,
            'title' => '',
            'content' => '',
            'thumbnail' => '',
            'property' => array(
                'badges' => array(),
                'time' => array('from' => '', 'to' => ''),
                'address' => array('address' => '', 'latitude' => '0', 'longitude' => '0'),
                'cost' => 0,
                'form_labels' => array()
            ),
            'users' => array(),
            'recs' => array()
        );

        return isset($defaults[$field]) ? $defaults[$field] : null;
    }

    // 获取报名表单
    public function get_form($request)
    {
        $act_id = $this->param_int($request, 'act_id', 0);

        if (!$act_id) {
            return $this->error('活动ID不能为空');
        }

        $post = get_post($act_id);
        if (!$post || $post->post_type !== 'zhuige_activity' || $post->post_status !== 'publish') {
            return $this->error('活动不存在');
        }

        $meta = get_post_meta($post->ID, 'zhuige-activity-option', true);
        if (!$meta) {
            $meta = array();
        }

        // 处理表单字段
        $labels = array();
        if (isset($meta['form_labels']) && is_array($meta['form_labels'])) {
            foreach ($meta['form_labels'] as $label_item) {
                if (isset($label_item['label']) && !empty($label_item['label'])) {
                    $labels[] = array(
                        'label' => $label_item['label'],
                        'required' => isset($label_item['required']) ? $label_item['required'] : true,
                        'value' => ''
                    );
                }
            }
        }

        return $this->success(array(
            'title' => $post->post_title,
            'thumbnail' => get_the_post_thumbnail_url($post->ID, 'full') ?: '',
            'labels' => $labels
        ));
    }

    // 提交报名
    public function post_enroll($request)
    {
        $act_id = $this->param_int($request, 'act_id', 0);
        $form = $this->param($request, 'form', '');

        if (!$act_id) {
            return $this->error('活动ID不能为空');
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录');
        }

        $post = get_post($act_id);
        if (!$post || $post->post_type !== 'zhuige_activity' || $post->post_status !== 'publish') {
            return $this->error('活动不存在');
        }

        // 检查是否已报名
        if (zhuige_activity_check_user_enrolled($act_id, $user_id)) {
            return $this->error('您已经报名过此活动');
        }

        // 检查活动是否结束
        $meta = get_post_meta($post->ID, 'zhuige-activity-option', true);
        if (isset($meta['time_to']) && !empty($meta['time_to'])) {
            if (strtotime($meta['time_to']) < time()) {
                return $this->error('活动已结束，无法报名');
            }
        }

        // 检查报名人数限制
        if (isset($meta['max_enrolls']) && $meta['max_enrolls'] > 0) {
            global $wpdb;
            $enroll_table = $wpdb->prefix . 'zhuige_activity_enrolls';
            $current_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $enroll_table WHERE activity_id = %d AND status = 1",
                $act_id
            ));
            
            if ($current_count >= $meta['max_enrolls']) {
                return $this->error('报名人数已满');
            }
        }

        // 验证表单数据
        if (!empty($form)) {
            $form_data = json_decode($form, true);
            if (!$form_data) {
                return $this->error('表单数据格式错误');
            }

            // 验证必填字段
            if (isset($meta['form_labels']) && is_array($meta['form_labels'])) {
                foreach ($meta['form_labels'] as $label_item) {
                    if (isset($label_item['required']) && $label_item['required']) {
                        $found = false;
                        foreach ($form_data as $form_item) {
                            if (isset($form_item['label']) && $form_item['label'] == $label_item['label']) {
                                if (empty($form_item['value'])) {
                                    return $this->error('请填写' . $label_item['label']);
                                }
                                $found = true;
                                break;
                            }
                        }
                        if (!$found) {
                            return $this->error('请填写' . $label_item['label']);
                        }
                    }
                }
            }
        }

        // 保存报名信息
        $cost = isset($meta['cost']) ? floatval($meta['cost']) : 0;
        $result = zhuige_activity_enroll_user($act_id, $user_id, $form, $cost);

        if ($result) {
            return $this->success('报名成功');
        } else {
            return $this->error('报名失败');
        }
    }

    // 微信支付报名
    public function wx_enroll_pay($request)
    {
        $act_id = $this->param_int($request, 'act_id', 0);

        if (!$act_id) {
            return $this->error('活动ID不能为空');
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录');
        }

        $post = get_post($act_id);
        if (!$post || $post->post_type !== 'zhuige_activity' || $post->post_status !== 'publish') {
            return $this->error('活动不存在');
        }

        // 检查是否已报名
        if (zhuige_activity_check_user_enrolled($act_id, $user_id)) {
            return $this->error('您已经报名过此活动');
        }

        $meta = get_post_meta($post->ID, 'zhuige-activity-option', true);
        $cost = isset($meta['cost']) ? floatval($meta['cost']) : 0;

        // 如果是免费活动
        if ($cost <= 0) {
            return $this->success(array(
                'free' => 1,
                'pay_finish' => 1
            ));
        }

        // 这里应该集成微信支付，暂时返回模拟数据
        return $this->success(array(
            'free' => 0,
            'pay_finish' => 0,
            'success' => false,
            'return_msg' => '支付功能待完善'
        ));
    }

    // 获取我的活动列表
    public function get_my_act($request)
    {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录');
        }

        $offset = $this->param_int($request, 'offset', 0);
        $limit = 10;

        global $wpdb;
        $enroll_table = $wpdb->prefix . 'zhuige_activity_enrolls';

        // 检查表是否存在
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enroll_table'") == $enroll_table;
        if (!$table_exists) {
            return $this->success(array(
                'list' => array(),
                'more' => 'noMore'
            ));
        }

        // 获取报名记录
        $enrolls = $wpdb->get_results($wpdb->prepare(
            "SELECT e.activity_id, e.time, e.pay_status, e.pay_amount
             FROM $enroll_table e
             INNER JOIN {$wpdb->posts} p ON e.activity_id = p.ID
             WHERE e.user_id = %d AND e.status = 1 AND p.post_status = 'publish'
             ORDER BY e.time DESC
             LIMIT %d, %d",
            $user_id,
            $offset,
            $limit
        ));

        $activities = array();
        foreach ($enrolls as $enroll) {
            $post = get_post($enroll->activity_id);
            if ($post) {
                $activity = zhuige_activity_format($post);
                // 添加报名相关信息
                $activity['my_enroll_time'] = zhuige_xcx_time_stamp_beautify($enroll->time);
                $activity['my_pay_status'] = intval($enroll->pay_status);
                $activity['my_pay_amount'] = floatval($enroll->pay_amount);
                $activities[] = $activity;
            }
        }

        // 获取总数
        $total_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*)
             FROM $enroll_table e
             INNER JOIN {$wpdb->posts} p ON e.activity_id = p.ID
             WHERE e.user_id = %d AND e.status = 1 AND p.post_status = 'publish'",
            $user_id
        ));
        $has_more = $total_count > ($offset + $limit);

        return $this->success(array(
            'list' => $activities,
            'more' => $has_more ? 'more' : 'noMore'
        ));
    }
}

// 注册控制器
ZhuiGe_Xcx::$rest_controllers[] = new ZhuiGe_Xcx_Activity_Controller();


