<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 追格活动
//
CSF::createSection($prefix, array(
    'id'    => 'activity',
    'title' => '追格活动',
    'icon'  => 'fas fa-calendar-alt',
));

//
// 活动设置
//
CSF::createSection($prefix, array(
    'parent' => 'activity',
    'title' => '活动设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(



        array(
            'id'     => 'activity_slides',
            'type'   => 'group',
            'title'  => '幻灯片',
            'subtitle' => '在活动首页顶部显示的轮播图片',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                    'after' => '<a href="https://www.zhuige.com/docs/zg/216.html" target="_blank">图片规格建议</a>',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                    'default' => 'https://www.zhuige.com',
                    'after' => '<a href="https://www.zhuige.com/docs/zg/209.html" target="_blank">如何获取链接</a>',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '停用/启用',
                    'default' => '1'
                ),
            ),
        ),



        array(
            'id'      => 'activity_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '用户分享活动时显示的图片',
            'library' => 'image',
            'after' => '<a href="https://www.zhuige.com/docs/zg/216.html" target="_blank">图片规格建议</a>',
        ),
    )
));



// 自定义列表页面
add_filter('manage_zhuige_activity_posts_columns', 'zhuige_activity_columns');
function zhuige_activity_columns($columns)
{
    $new_columns = [];
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = '活动名称';
    $new_columns['activity_logo'] = 'LOGO';
    $new_columns['activity_time'] = '活动时间';
    $new_columns['activity_address'] = '活动地址';
    $new_columns['activity_cost'] = '费用';
    $new_columns['activity_enrolls'] = '报名人数';
    $new_columns['activity_category'] = '分类';
    $new_columns['author'] = '作者';
    $new_columns['date'] = '发布时间';

    return $new_columns;
}

// 自定义列内容
add_action('manage_zhuige_activity_posts_custom_column', 'zhuige_activity_column_content', 10, 2);
function zhuige_activity_column_content($column, $post_id)
{
    $options = get_post_meta($post_id, 'zhuige-activity-option', true);
    if (!$options) {
        $options = [];
    }

    switch ($column) {
        case 'activity_logo':
            if (!empty($options['logo']['url'])) {
                echo '<img src="' . esc_url($options['logo']['url']) . '" style="width: 50px; height: 50px; object-fit: cover;" />';
            } else {
                echo '-';
            }
            break;

        case 'activity_time':
            $time_from = $options['time_from'] ?? '';
            $time_to = $options['time_to'] ?? '';
            if ($time_from && $time_to) {
                echo esc_html(date('m-d H:i', strtotime($time_from))) . '<br>' . esc_html(date('m-d H:i', strtotime($time_to)));
            } else {
                echo '-';
            }
            break;

        case 'activity_address':
            echo esc_html($options['address'] ?? '-');
            break;

        case 'activity_cost':
            $cost = $options['cost'] ?? 0;
            echo $cost > 0 ? '¥' . $cost : '免费';
            break;

        case 'activity_enrolls':
            global $wpdb;
            $table_name = $wpdb->prefix . 'zhuige_activity_enrolls';

            // 检查表是否存在
            $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
            if (!$table_exists) {
                echo '0';
                break;
            }

            $count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $table_name WHERE activity_id = %d AND status = 1",
                $post_id
            ));

            $max_enrolls = $options['max_enrolls'] ?? 0;
            if ($max_enrolls > 0) {
                echo $count . '/' . $max_enrolls;
            } else {
                echo $count . '/不限';
            }
            break;

        case 'activity_category':
            $terms = wp_get_post_terms($post_id, 'zhuige_activity_cat');
            if (!empty($terms)) {
                echo esc_html($terms[0]->name);
            } else {
                echo '-';
            }
            break;
    }
}

// 添加报名记录元框
add_action('add_meta_boxes', 'zhuige_activity_add_enrolls_meta_box');
function zhuige_activity_add_enrolls_meta_box()
{
    add_meta_box(
        'zhuige_activity_enrolls',
        '报名记录',
        'zhuige_activity_enrolls_meta_box_callback',
        'zhuige_activity',
        'normal',
        'high'
    );
}

// 报名记录元框回调函数
function zhuige_activity_enrolls_meta_box_callback($post)
{
    global $wpdb;
    $enroll_table = $wpdb->prefix . 'zhuige_activity_enrolls';

    // 检查表是否存在
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enroll_table'") == $enroll_table;
    if (!$table_exists) {
        echo '<p>报名表不存在，请先有用户报名后再查看。</p>';
        return;
    }

    // 获取报名记录
    $enrolls = $wpdb->get_results($wpdb->prepare("
        SELECT e.*, u.display_name, u.user_email
        FROM $enroll_table e
        LEFT JOIN {$wpdb->users} u ON e.user_id = u.ID
        WHERE e.activity_id = %d AND e.status = 1
        ORDER BY e.time DESC
    ", $post->ID));

    if (empty($enrolls)) {
        echo '<p>暂无报名记录。</p>';
        return;
    }

    echo '<table class="wp-list-table widefat fixed striped" style="margin-top: 10px;">
        <thead>
            <tr>
                <th style="width: 15%;">报名用户</th>
                <th style="width: 20%;">邮箱</th>
                <th style="width: 15%;">报名时间</th>
                <th style="width: 10%;">支付状态</th>
                <th style="width: 10%;">支付金额</th>
                <th style="width: 30%;">表单数据</th>
            </tr>
        </thead>
        <tbody>';

    foreach ($enrolls as $enroll) {
        $form_data = '';
        if (!empty($enroll->form_data)) {
            $form_array = json_decode($enroll->form_data, true);
            if ($form_array && is_array($form_array)) {
                $form_items = array();
                foreach ($form_array as $item) {
                    if (isset($item['label']) && isset($item['value'])) {
                        $form_items[] = esc_html($item['label']) . ': ' . esc_html($item['value']);
                    }
                }
                $form_data = implode('<br>', $form_items);
            }
        }

        $pay_status = $enroll->pay_status ? '<span style="color: green;">已支付</span>' : '<span style="color: orange;">未支付</span>';
        $pay_amount = $enroll->pay_amount > 0 ? '￥' . number_format($enroll->pay_amount, 2) : '<span style="color: #666;">免费</span>';
        $enroll_time = $enroll->time ? date('Y-m-d H:i:s', $enroll->time) : '未知';

        echo '<tr>
            <td>' . esc_html($enroll->display_name ?: '用户' . $enroll->user_id) . '</td>
            <td>' . esc_html($enroll->user_email ?: '-') . '</td>
            <td>' . esc_html($enroll_time) . '</td>
            <td>' . $pay_status . '</td>
            <td>' . $pay_amount . '</td>
            <td>' . ($form_data ?: '-') . '</td>
        </tr>';
    }

    echo '</tbody></table>';

    // 显示统计信息
    $total_count = count($enrolls);
    $paid_count = count(array_filter($enrolls, function($e) { return $e->pay_status; }));
    $total_amount = array_sum(array_map(function($e) { return $e->pay_amount; }, $enrolls));

    echo '<div style="margin-top: 15px; padding: 10px; background: #f9f9f9; border-left: 4px solid #0073aa;">
        <strong>统计信息：</strong><br>
        总报名人数: ' . $total_count . ' 人<br>
        已支付人数: ' . $paid_count . ' 人<br>
        总收入金额: ￥' . number_format($total_amount, 2) . '
    </div>';
}
