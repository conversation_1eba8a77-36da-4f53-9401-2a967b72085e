<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 格式化活动数据
 */
if (!function_exists('zhuige_activity_format')) {
    function zhuige_activity_format($post)
    {
    $meta = get_post_meta($post->ID, 'zhuige-activity-option', true);
    if (!$meta) {
        $meta = array();
    }

    // 获取活动报名人数
    global $wpdb;
    $enroll_table = $wpdb->prefix . 'zhuige_activity_enrolls';

    // 检查表是否存在
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$enroll_table'") == $enroll_table;
    $enroll_count = 0;

    if ($table_exists) {
        $enroll_count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $enroll_table WHERE activity_id = %d AND status = 1",
            $post->ID
        ));
        if (!$enroll_count) {
            $enroll_count = 0;
        }
    }

    // 检查当前用户是否已报名
    $my_enroll = false;
    $current_user_id = get_current_user_id();
    if ($current_user_id) {
        $my_enroll = zhuige_activity_check_user_enrolled($post->ID, $current_user_id);
    }

    // 处理时间格式
    $time_from = isset($meta['time_from']) ? $meta['time_from'] : '';
    $time_to = isset($meta['time_to']) ? $meta['time_to'] : '';

    if ($time_from) {
        $time_from = date('m月d日 H:i', strtotime($time_from));
    }
    if ($time_to) {
        $time_to = date('m月d日 H:i', strtotime($time_to));
    }

    // 获取WordPress taxonomy标签
    $badges = array();
    $terms = get_the_terms($post->ID, 'zhuige_activity_tag');
    if (!is_wp_error($terms) && !empty($terms)) {
        foreach ($terms as $term) {
            $badges[] = $term->name;
        }
    }

    // 判断活动是否结束
    $is_end = false;
    if (isset($meta['time_to']) && !empty($meta['time_to'])) {
        $is_end = strtotime($meta['time_to']) < time();
    }



    // 获取缩略图URL，确保有默认值
    $thumbnail_url = get_the_post_thumbnail_url($post->ID, 'full');
    if (!$thumbnail_url) {
        $thumbnail_url = '';
    }

    // 获取logo URL
    $logo_url = ZhuiGe_Xcx::option_image_url($meta['logo'] ?? null, 'placeholder.jpg');

    // 确保费用为数字类型
    $cost = isset($meta['cost']) ? floatval($meta['cost']) : 0;
    if ($cost < 0) {
        $cost = 0;
    }

    // 处理表单字段
    $form_labels = array();
    if (isset($meta['form_labels']) && is_array($meta['form_labels'])) {
        foreach ($meta['form_labels'] as $label_item) {
            if (isset($label_item['label']) && !empty($label_item['label'])) {
                $form_labels[] = array(
                    'label' => $label_item['label'],
                    'required' => isset($label_item['required']) ? $label_item['required'] : true,
                    'value' => ''
                );
            }
        }
    }

    // 构建返回数据
    $activity_data = array(
        'id' => intval($post->ID),
        'title' => $post->post_title ? $post->post_title : '未命名活动',
        'content' => $post->post_content ? $post->post_content : '',
        'thumbnail' => $thumbnail_url,
        'logo' => $logo_url,
        'time' => array(
            'from' => $time_from,
            'to' => $time_to
        ),
        'badges' => $badges,
        'cost' => $cost,
        'enroll_count' => intval($enroll_count),
        'max_enrolls' => isset($meta['max_enrolls']) ? intval($meta['max_enrolls']) : 0,
        'my_enroll' => $my_enroll,
        'is_end' => $is_end,
        'enroll_link' => isset($meta['enroll_link']) ? trim($meta['enroll_link']) : '',
        'property' => array(
            'badges' => $badges,
            'time' => array(
                'from' => $time_from,
                'to' => $time_to
            ),
            'address' => array(
                'address' => isset($meta['address']) ? trim($meta['address']) : '',
                'latitude' => isset($meta['latitude']) ? trim($meta['latitude']) : '0',
                'longitude' => isset($meta['longitude']) ? trim($meta['longitude']) : '0'
            ),
            'cost' => $cost,
            'form_labels' => $form_labels
        ),
        'created_at' => $post->post_date,
        'updated_at' => $post->post_modified
    );

        return $activity_data;
    }
}

/**
 * 获取活动分类
 */
if (!function_exists('zhuige_activity_get_nav_categories')) {
    function zhuige_activity_get_nav_categories()
    {
    // 使用WordPress taxonomy获取分类
    $categories = get_terms(array(
        'taxonomy' => 'zhuige_activity_cat',
        'hide_empty' => false,
        'orderby' => 'term_order',
        'order' => 'ASC'
    ));

    $nav_cats = array();

    // 添加默认"全部"分类
    $nav_cats[] = array(
        'id' => 0,
        'title' => '全部'
    );

    if (!is_wp_error($categories) && !empty($categories)) {
        foreach ($categories as $category) {
            $nav_cats[] = array(
                'id' => intval($category->term_id),
                'title' => $category->name
            );
        }
    }

        return $nav_cats;
    }
}

/**
 * 检查用户是否已报名
 */
if (!function_exists('zhuige_activity_check_user_enrolled')) {
    function zhuige_activity_check_user_enrolled($activity_id, $user_id)
    {
    global $wpdb;
    $table_name = $wpdb->prefix . 'zhuige_activity_enrolls';

    // 检查表是否存在
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
    if (!$table_exists) {
        return false;
    }

    $count = $wpdb->get_var($wpdb->prepare(
        "SELECT COUNT(*) FROM $table_name WHERE activity_id = %d AND user_id = %d AND status = 1",
        $activity_id,
        $user_id
    ));

        return $count > 0;
    }
}

/**
 * 用户报名活动
 */
if (!function_exists('zhuige_activity_enroll_user')) {
    function zhuige_activity_enroll_user($activity_id, $user_id, $form_data = '', $pay_amount = 0)
    {
    global $wpdb;
    $table_name = $wpdb->prefix . 'zhuige_activity_enrolls';
    
    // 检查是否已报名
    if (zhuige_activity_check_user_enrolled($activity_id, $user_id)) {
        return false;
    }
    
    $result = $wpdb->insert(
        $table_name,
        array(
            'activity_id' => $activity_id,
            'user_id' => $user_id,
            'form_data' => $form_data,
            'pay_amount' => $pay_amount,
            'pay_status' => $pay_amount > 0 ? 0 : 1,
            'status' => 1,
            'time' => time()
        ),
        array('%d', '%d', '%s', '%f', '%d', '%d', '%d')
    );
    
        return $result !== false;
    }
}
