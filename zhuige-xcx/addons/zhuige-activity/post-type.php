<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

function zhuige_xcx_activity_create_custom_post_type()
{
    /**
     * 活动
     */
    $zhuige_activity_labels = array(
        'name'               => '追格活动',
        'singular_name'      => '追格活动',
        'add_new'            => '新建活动',
        'add_new_item'       => '新建一个活动',
        'edit_item'          => '编辑活动',
        'new_item'           => '新活动',
        'all_items'          => '所有活动',
        'view_item'          => '查看活动',
        'search_items'       => '搜索活动',
        'not_found'          => '没有找到有关活动',
        'not_found_in_trash' => '回收站里面没有相关活动',
        'parent_item_colon'  => '',
        'menu_name'          => '追格活动'
    );
    $zhuige_activity_args = array(
        'labels'        => $zhuige_activity_labels,
        'description'   => '我们网站的活动信息',
        'public'        => true,
        'supports'      => array('title', 'editor', 'author', 'thumbnail'),
        'has_archive'   => true
    );
    register_post_type('zhuige_activity', $zhuige_activity_args);

    // 活动设置
    $zhuige_activity_option = 'zhuige-activity-option';
    CSF::createMetabox($zhuige_activity_option, array(
        'title'        => '追格活动设置',
        'post_type'    => 'zhuige_activity',
    ));

    CSF::createSection($zhuige_activity_option, array(
        'fields' => array(
            array(
                'id'    => 'logo',
                'type'  => 'media',
                'title' => '活动LOGO',
                'library' => 'image',
            ),

            array(
                'id'    => 'time_from',
                'type'  => 'text',
                'title' => '活动开始时间',
                'subtitle' => '格式：2024-01-01 10:00:00',
                'placeholder' => '2024-01-01 10:00:00',
            ),

            array(
                'id'    => 'time_to',
                'type'  => 'text',
                'title' => '活动结束时间',
                'subtitle' => '格式：2024-01-01 12:00:00',
                'placeholder' => '2024-01-01 12:00:00',
            ),

            array(
                'id'    => 'address',
                'type'  => 'text',
                'title' => '活动地址',
            ),

            array(
                'id'    => 'latitude',
                'type'  => 'text',
                'title' => '纬度',
            ),

            array(
                'id'    => 'longitude',
                'type'  => 'text',
                'title' => '经度',
            ),

            array(
                'id'    => 'cost',
                'type'  => 'number',
                'title' => '活动费用',
                'default' => 0,
                'unit' => '元',
            ),

            array(
                'id'    => 'max_enrolls',
                'type'  => 'number',
                'title' => '最大报名人数',
                'default' => 0,
                'desc' => '0表示不限制',
            ),

            array(
                'id'    => 'enroll_link',
                'type'  => 'text',
                'title' => '外部报名链接',
                'desc' => '如果设置了外部链接，将跳转到外部页面报名',
            ),

            array(
                'id'    => 'form_labels',
                'type'  => 'repeater',
                'title' => '报名表单字段',
                'fields' => array(
                    array(
                        'id'    => 'label',
                        'type'  => 'text',
                        'title' => '字段名称',
                    ),
                    array(
                        'id'    => 'required',
                        'type'  => 'switcher',
                        'title' => '是否必填',
                        'default' => true,
                    ),
                ),
                'default' => array(
                    array(
                        'label' => '姓名',
                        'required' => true,
                    ),
                    array(
                        'label' => '手机号',
                        'required' => true,
                    ),
                ),
            ),


        )
    ));

    /**
     * 活动分类
     */
    $zhuige_activity_cat_labels = array(
        'name'              => '分类',
        'singular_name'     => '分类',
        'search_items'      => '搜索分类',
        'all_items'         => '所有分类',
        'parent_item'       => '该分类的上级分类',
        'parent_item_colon' => '该分类的上级分类：',
        'edit_item'         => '编辑分类',
        'update_item'       => '更新分类',
        'add_new_item'      => '添加新的分类',
        'new_item_name'     => '分类',
        'menu_name'         => '分类',
    );
    $zhuige_activity_cat_args = array(
        'hierarchical' => true,
        'labels' => $zhuige_activity_cat_labels,
    );
    register_taxonomy('zhuige_activity_cat', 'zhuige_activity', $zhuige_activity_cat_args);

    /**
     * 活动标签
     */
    $zhuige_activity_tag_labels = array(
        'name'              => '标签',
        'singular_name'     => '标签',
        'search_items'      => '搜索标签',
        'all_items'         => '所有标签',
        'parent_item'       => '该标签的上级标签',
        'parent_item_colon' => '该标签的上级标签：',
        'edit_item'         => '编辑标签',
        'update_item'       => '更新标签',
        'add_new_item'      => '添加新的标签',
        'new_item_name'     => '标签',
        'menu_name'         => '标签',
        'separate_items_with_commas' => '多个标签请用英文逗号（,）分开',
        'choose_from_most_used' => '从常用标签中选择',
        'not_found' => '未找到标签'
    );
    $zhuige_activity_tag_args = array(
        'hierarchical' => false,
        'labels' => $zhuige_activity_tag_labels,
    );
    register_taxonomy('zhuige_activity_tag', 'zhuige_activity', $zhuige_activity_tag_args);
}

// 注册到系统
ZhuiGe_Xcx::$post_types[] = ['id' => 'zhuige_activity', 'name' => '活动报名', 'link' => '/pages/activity/detail/detail'];
add_action('init', 'zhuige_xcx_activity_create_custom_post_type');
