<?php {
    // 活动报名表
    $table_zhuige_activity_enrolls = $wpdb->prefix . 'zhuige_activity_enrolls';
    $activity_enrolls_sql = "CREATE TABLE IF NOT EXISTS `$table_zhuige_activity_enrolls` (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `activity_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '活动ID',
        `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
        `form_data` text COMMENT '报名表单数据',
        `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态:1=正常,0=取消',
        `pay_status` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '支付状态:0=未支付,1=已支付',
        `pay_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
        `pay_time` int(10) UNSIGNED DEFAULT '0' COMMENT '支付时间',
        `time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        KEY `activity_id` (`activity_id`),
        KEY `user_id` (`user_id`),
        KEY `status` (`status`),
        KEY `pay_status` (`pay_status`)
    ) $charset_collate;";
    dbDelta($activity_enrolls_sql);
}
