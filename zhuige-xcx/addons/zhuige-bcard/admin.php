<?php

/**
 * 追格小程序 - 商家名片管理后台
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 商家名片
//
CSF::createSection($prefix, array(
    'id'    => 'bcard',
    'title' => '商家名片',
    'icon'  => 'fas fa-id-card',
));

//
// 商家名片设置
//
CSF::createSection($prefix, array(
    'parent' => 'bcard',
    'title' => '商家名片设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(

        array(
            'id'    => 'bcard_avatar_switch',
            'type'  => 'switcher',
            'title' => '发布要求头像昵称',
            'subtitle' => '发布商家名片是否要求设置头像昵称',
            'default' => '0'
        ),

        array(
            'id'    => 'bcard_mobile_switch',
            'type'  => 'switcher',
            'title' => '发布要求手机号',
            'subtitle' => '发布商家名片是否要求绑定手机号',
            'default' => '0'
        ),

        array(
            'id'    => 'bcard_comment_switch',
            'type'  => 'switcher',
            'title' => '评论功能',
            'subtitle' => '是否开启名片评论功能',
            'default' => '0'
        ),

        array(
            'id'     => 'bcard_comment_permission',
            'type'   => 'fieldset',
            'title'  => '评论权限',
            'dependency' => array('bcard_comment_switch', '==', '1'),
            'fields' => array(
                array(
                    'id'    => 'comment_avatar_switch',
                    'type'  => 'switcher',
                    'title' => '评论要求头像昵称',
                    'subtitle' => '评论是否要求设置头像昵称',
                    'default' => '0'
                ),
                array(
                    'id'    => 'comment_mobile_switch',
                    'type'  => 'switcher',
                    'title' => '评论要求手机号',
                    'subtitle' => '评论是否要求绑定手机号',
                    'default' => '0'
                ),
            ),
        ),

        array(
            'id'          => 'bcard_cardui',
            'type'        => 'select',
            'title'       => '名片UI样式',
            'subtitle'    => '名片显示样式',
            'options'     => array(
                '0'    => '标准样式',
                '1'    => '迷你样式',
            ),
            'default' => '1'
        ),

        array(
            'id'      => 'bcard_logo',
            'type'    => 'media',
            'title'   => '首页LOGO',
            'subtitle' => '商家名片首页显示的LOGO图片',
            'library' => 'image',
        ),

        array(
            'id'      => 'bcard_background',
            'type'    => 'media',
            'title'   => '首页背景图',
            'subtitle' => '商家名片首页的背景图片',
            'library' => 'image',
        ),

        array(
            'id'     => 'bcard_icon',
            'type'   => 'group',
            'title'  => '图标导航',
            'subtitle' => '商家名片首页的快捷导航配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                    'default' => '快捷导航'
                ),

                array(
                    'id'    => 'subtitle',
                    'type'  => 'text',
                    'title' => '副标题',
                    'default' => ''
                ),

                array(
                    'id'     => 'items',
                    'type'   => 'group',
                    'title'  => '导航项目',
                    'fields' => array(
                        array(
                            'id'    => 'title',
                            'type'  => 'text',
                            'title' => '标题',
                        ),

                        array(
                            'id'      => 'logo',
                            'type'    => 'media',
                            'title'   => '图标',
                            'library' => 'image',
                        ),

                        array(
                            'id'    => 'link',
                            'type'  => 'text',
                            'title' => '链接',
                        ),

                        array(
                            'id'    => 'switch',
                            'type'  => 'switcher',
                            'title' => '启用',
                            'default' => '1'
                        ),
                    ),
                ),
            ),
        ),

        array(
            'id'      => 'bcard_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '用户分享商家名片时默认显示的图片',
            'library' => 'image',
        ),

        array(
            'id'     => 'bcard_slides',
            'type'   => 'group',
            'title'  => '轮播图',
            'subtitle' => '商家名片首页和分类页的轮播图配置',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                    'default' => 'https://www.zhuige.com',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'          => 'bcard_nav_cats',
            'type'        => 'select',
            'title'       => '导航分类',
            'subtitle'    => '在商家名片首页显示的分类导航',
            'placeholder' => '选择分类',
            'chosen'      => true,
            'multiple'    => true,
            'sortable'    => true,
            'ajax'        => true,
            'options'     => 'categories',
            'query_args'  => array(
                'taxonomy'  => 'zhuige_business_card_cat',
            ),
        ),

        array(
            'id'     => 'bcard_bottom_menu',
            'type'   => 'group',
            'title'  => '底部菜单',
            'subtitle' => '商家名片首页底部的菜单配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),



        array(
            'id'          => 'bcard_create_licence',
            'type'        => 'select',
            'title'       => '发布协议',
            'subtitle'    => '用户发布商家名片时需要同意的协议页面',
            'chosen'      => true,
            'ajax'        => true,
            'options'     => 'pages',
            'placeholder' => '选择一个页面',
        ),
    )
));

// 自定义列表页面
add_filter('manage_zhuige_business_card_posts_columns', 'zhuige_business_card_columns');
function zhuige_business_card_columns($columns)
{
    $new_columns = [];
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = '名片名称';
    $new_columns['bcard_logo'] = 'LOGO';
    $new_columns['bcard_contact'] = '联系人';
    $new_columns['bcard_phone'] = '电话';
    $new_columns['bcard_category'] = '分类';
    $new_columns['bcard_score'] = '评分';
    $new_columns['bcard_views'] = '浏览量';
    $new_columns['author'] = '作者';
    $new_columns['date'] = '发布时间';
    
    return $new_columns;
}

// 自定义列内容
add_action('manage_zhuige_business_card_posts_custom_column', 'zhuige_business_card_column_content', 10, 2);
function zhuige_business_card_column_content($column, $post_id)
{
    $options = get_post_meta($post_id, 'zhuige_business_card_options', true);
    if (!$options) {
        $options = [];
    }
    
    switch ($column) {
        case 'bcard_logo':
            if (!empty($options['logo']['url'])) {
                echo '<img src="' . esc_url($options['logo']['url']) . '" style="width: 50px; height: 50px; object-fit: cover;" />';
            } else {
                echo '-';
            }
            break;
            
        case 'bcard_contact':
            echo esc_html($options['contact'] ?? '-');
            break;
            
        case 'bcard_phone':
            echo esc_html($options['phone'] ?? '-');
            break;
            
        case 'bcard_category':
            $terms = wp_get_post_terms($post_id, 'zhuige_business_card_cat');
            if (!empty($terms)) {
                echo esc_html($terms[0]->name);
            } else {
                echo '-';
            }
            break;
            
        case 'bcard_score':
            echo zhuige_business_card_get_score($post_id);
            break;

        case 'bcard_views':
            echo zhuige_business_card_get_views($post_id);
            break;
    }
}
