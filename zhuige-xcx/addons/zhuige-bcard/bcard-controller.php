<?php

/**
 * 追格小程序 - 商家名片控制器
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

class ZhuiGe_Xcx_Bcard_Controller extends ZhuiGe_Xcx_Base_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->module = 'bcard';

        $this->routes = [
            // 首页配置
            'setting' => 'get_setting',
            'setting_cat' => 'get_setting_cat',

            // 分类相关
            'cats' => 'get_cats',

            // 列表相关
            'last' => 'get_list_last',
            'cat' => 'get_list_cat',
            'tag' => 'get_list_tag',
            'search' => 'get_list_search',

            // 创建编辑
            'create_pre' => ['callback' => 'create_pre', 'auth' => 'login'],
            'create' => ['callback' => 'create', 'auth' => 'login'],

            // 详情
            'detail' => 'get_detail',
        ];
    }

    /**
     * 商家名片首页配置
     */
    public function get_setting($request)
    {
        $data = [];

        // LOGO
        $bcard_logo = ZhuiGe_Xcx::option_value('bcard_logo');
        $data['logo'] = ZhuiGe_Xcx::option_image_url($bcard_logo, 'placeholder.jpg');

        // 背景图
        $bcard_background = ZhuiGe_Xcx::option_value('bcard_background');
        $data['background'] = ZhuiGe_Xcx::option_image_url($bcard_background, 'placeholder.jpg');

        // 图标导航
        $bcard_icon_org = ZhuiGe_Xcx::option_value('bcard_icon');
        $icon = null;
        if (is_array($bcard_icon_org) && !empty($bcard_icon_org)) {
            // 获取图标导航配置
            $bcard_icon_config = isset($bcard_icon_org[0]) ? $bcard_icon_org[0] : $bcard_icon_org;

            $icon = [
                'title' => $bcard_icon_config['title'] ?? '快捷导航',
                'subtitle' => $bcard_icon_config['subtitle'] ?? '',
                'items' => []
            ];

            if (isset($bcard_icon_config['items']) && is_array($bcard_icon_config['items'])) {
                foreach ($bcard_icon_config['items'] as $item) {
                    // 检查开关状态
                    $switch_on = isset($item['switch']) && ($item['switch'] === '1' || $item['switch'] === 1 || $item['switch'] === true);

                    // 检查必要字段
                    $has_title = isset($item['title']) && !empty($item['title']);
                    $has_logo = isset($item['logo']) && !empty($item['logo']);

                    if ($switch_on && $has_title && $has_logo) {
                        $icon['items'][] = [
                            'title' => $item['title'],
                            'logo' => ZhuiGe_Xcx::option_image_url($item['logo']),
                            'link' => $item['link'] ?? ''
                        ];
                    }
                }
            }
        }

        // 如果没有配置数据，设置默认的图标导航结构
        if ($icon === null) {
            $icon = [
                'title' => '快捷导航',
                'subtitle' => '',
                'items' => []
            ];
        }

        $data['icon'] = $icon;

        // 轮播图
        $slides_org = ZhuiGe_Xcx::option_value('bcard_slides');
        $slides = [];
        if (is_array($slides_org)) {
            foreach ($slides_org as $item) {
                // 检查开关状态
                $switch_on = isset($item['switch']) && ($item['switch'] === '1' || $item['switch'] === 1 || $item['switch'] === true);
                if ($switch_on && !empty($item['image']) && !empty($item['image']['url'])) {
                    $slides[] = [
                        'image' => $item['image']['url'],
                        'link' => $item['link'] ?? '',
                    ];
                }
            }
        }
        $data['slides'] = $slides;

        // 导航分类
        $term_args = [
            'taxonomy' => 'zhuige_business_card_cat',
            'hide_empty' => false
        ];
        $cat_ids = ZhuiGe_Xcx::option_value('bcard_nav_cats');
        if (!empty($cat_ids)) {
            $term_args['include'] = $cat_ids;
            $term_args['orderby'] = 'include';
        }
        $terms = get_terms($term_args);
        $nav_cats = [];
        foreach ($terms as $term) {
            $nav_cats[] = [
                'id' => $term->term_id,
                'title' => $term->name
            ];
        }
        $data['nav_cats'] = $nav_cats;

        // 底部菜单
        $bottom_menu_org = ZhuiGe_Xcx::option_value('bcard_bottom_menu');
        $bottom_menu = [];
        if (is_array($bottom_menu_org)) {
            foreach ($bottom_menu_org as $item) {
                // 检查开关状态
                $switch_on = isset($item['switch']) && ($item['switch'] === '1' || $item['switch'] === 1 || $item['switch'] === true);
                if ($switch_on && !empty($item['title']) && !empty($item['image'])) {
                    $bottom_menu[] = [
                        'title' => $item['title'],
                        'image' => ZhuiGe_Xcx::option_image_url($item['image']),
                        'link' => $item['link'] ?? '',
                    ];
                }
            }
        }
        $data['bottom_menu'] = $bottom_menu;

        // 分享图片
        $bcard_share_img = ZhuiGe_Xcx::option_value('bcard_share_img');
        if ($bcard_share_img) {
            $data['share_img'] = ZhuiGe_Xcx::option_image_url($bcard_share_img);
        }

        return $this->success($data);
    }

    /**
     * 商家名片分类页配置
     */
    public function get_setting_cat($request)
    {
        $data = [];

        // 轮播图
        $slides_org = ZhuiGe_Xcx::option_value('bcard_slides');
        $slides = [];
        if (is_array($slides_org)) {
            foreach ($slides_org as $item) {
                // 检查开关状态
                $switch_on = isset($item['switch']) && ($item['switch'] === '1' || $item['switch'] === 1 || $item['switch'] === true);
                if ($switch_on && !empty($item['image']) && !empty($item['image']['url'])) {
                    $slides[] = [
                        'image' => $item['image']['url'],
                        'link' => $item['link'] ?? '',
                    ];
                }
            }
        }
        $data['slides'] = $slides;

        // 分类导航
        $terms = get_terms([
            'taxonomy' => 'zhuige_business_card_cat',
            'hide_empty' => false
        ]);

        $nav_cats = [];
        foreach ($terms as $term) {
            $nav_cats[] = [
                'id' => $term->term_id,
                'title' => $term->name
            ];
        }
        $data['nav_cats'] = $nav_cats;

        return $this->success($data);
    }

    /**
     * 获取所有分类
     */
    public function get_cats($request)
    {
        $terms = get_terms([
            'taxonomy' => 'zhuige_business_card_cat',
            'hide_empty' => false
        ]);

        $cats = [];
        foreach ($terms as $term) {
            $term_meta = get_term_meta($term->term_id, 'zhuige_business_card_cat_options', true);
            $icon = '';
            if ($term_meta && isset($term_meta['icon'])) {
                $icon = ZhuiGe_Xcx::option_image_url($term_meta['icon']);
            }

            // 统计该分类下的商家名片数量
            $count = wp_count_posts('zhuige_business_card');
            $published_count = $count->publish ?? 0;

            $cats[] = [
                'id' => $term->term_id,
                'name' => $term->name,
                'icon' => $icon,
                'count' => $published_count
            ];
        }

        return $this->success(['cats' => $cats]);
    }

    /**
     * 获取最新商家名片列表
     */
    public function get_list_last($request)
    {
        $offset = $this->param_int($request, 'offset', 0);

        $args = [
            'post_type' => 'zhuige_business_card',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC'
        ];

        $query = new WP_Query($args);
        $cards = [];

        foreach ($query->posts as $post) {
            $cards[] = zhuige_business_card_format($post);
        }

        return $this->success([
            'list' => $cards,
            'more' => (count($cards) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'nomore')
        ]);
    }

    /**
     * 按分类获取商家名片列表
     */
    public function get_list_cat($request)
    {
        $cat_id = $this->param_int($request, 'cat_id', 0);
        $offset = $this->param_int($request, 'offset', 0);

        if (!$cat_id) {
            return $this->error('缺少参数');
        }

        $args = [
            'post_type' => 'zhuige_business_card',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
            'tax_query' => [
                [
                    'taxonomy' => 'zhuige_business_card_cat',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ]
            ]
        ];

        $query = new WP_Query($args);
        $cards = [];

        foreach ($query->posts as $post) {
            $cards[] = zhuige_business_card_format($post);
        }

        return $this->success([
            'list' => $cards,
            'more' => (count($cards) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'nomore')
        ]);
    }

    /**
     * 按标签获取商家名片列表
     */
    public function get_list_tag($request)
    {
        $tag_id = $this->param_int($request, 'tag_id', 0);
        $offset = $this->param_int($request, 'offset', 0);

        if (!$tag_id) {
            return $this->error('缺少参数');
        }

        $args = [
            'post_type' => 'zhuige_business_card',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'order' => 'DESC',
            'tax_query' => [
                [
                    'taxonomy' => 'zhuige_business_card_tag',
                    'field' => 'term_id',
                    'terms' => $tag_id,
                ]
            ]
        ];

        $query = new WP_Query($args);
        $cards = [];

        foreach ($query->posts as $post) {
            $cards[] = zhuige_business_card_format($post);
        }

        return $this->success([
            'list' => $cards,
            'more' => (count($cards) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'nomore')
        ]);
    }

    /**
     * 搜索商家名片
     */
    public function get_list_search($request)
    {
        $search = $this->param($request, 'search', '');
        $offset = $this->param_int($request, 'offset', 0);

        if (!$search) {
            return $this->error('缺少搜索关键词');
        }

        $args = [
            'post_type' => 'zhuige_business_card',
            'post_status' => 'publish',
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            's' => $search,
            'orderby' => 'date',
            'order' => 'DESC'
        ];

        $query = new WP_Query($args);
        $cards = [];

        foreach ($query->posts as $post) {
            $cards[] = zhuige_business_card_format($post);
        }

        return $this->success([
            'list' => $cards,
            'more' => (count($cards) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'nomore')
        ]);
    }

    /**
     * 创建商家名片前置检查
     */
    public function create_pre($request)
    {
        $card_id = $this->param_int($request, 'card_id', 0);
        $my_user_id = get_current_user_id();

        // 黑名单检查
        if (function_exists('zhuige_auth_is_black') && zhuige_auth_is_black($my_user_id)) {
            return $this->error('操作太频繁了~');
        }

        // 创建商家名片是否要求手机号
        if (ZhuiGe_Xcx::option_value('bcard_mobile_switch')) {
            if (!zhuige_xcx_is_set_mobile($my_user_id)) {
                return $this->error('', 'require_mobile');
            }
        }

        // 创建商家名片是否要求头像昵称
        if (ZhuiGe_Xcx::option_value('bcard_avatar_switch')) {
            if (!zhuige_xcx_is_set_avatar($my_user_id)) {
                return $this->error('', 'require_avatar');
            }
        }

        $data = [];

        // 分类
        $terms = get_terms([
            'taxonomy' => 'zhuige_business_card_cat',
            'hide_empty' => false
        ]);

        if (empty($terms)) {
            return $this->error('请在后台添加商家名片分类~');
        }

        $cats = [];
        foreach ($terms as $term) {
            $cats[] = [
                'id' => $term->term_id,
                'name' => $term->name
            ];
        }
        $data['cats'] = $cats;

        // 发布协议
        $fbxy = ZhuiGe_Xcx::option_value('bcard_create_licence');
        if ($fbxy) {
            $data['fbxy'] = '/pages/base/page/page?page_id=' . $fbxy;
        }

        // 如果是编辑模式，返回现有数据
        if ($card_id) {
            $post = get_post($card_id);
            if (!$post || $post->post_type !== 'zhuige_business_card') {
                return $this->error('商家名片不存在');
            }

            // 检查权限
            if ($post->post_author != $my_user_id && !current_user_can('edit_others_posts')) {
                return $this->error('无权限编辑此商家名片');
            }

            // 获取扩展信息
            $options = get_post_meta($card_id, 'zhuige_business_card_options', true);
            if (!$options) {
                $options = [];
            }

            $card_data = [
                'title' => $post->post_title,
                'content' => $post->post_content,
                'cat_id' => 0,
                'logo' => $options['logo'] ?? null,
                'images' => $options['images'] ?? [],
                'contact' => $options['contact'] ?? '',
                'phone' => $options['phone'] ?? '',
                'longitude' => $options['longitude'] ?? '',
                'latitude' => $options['latitude'] ?? '',
                'marker' => $options['marker'] ?? '',
                'address' => $options['address'] ?? '',
                'position' => $options['position'] ?? '',
                'web' => $options['web'] ?? '',
                'company' => $options['company'] ?? '',
                'qrcode' => $options['qrcode'] ?? null,
            ];

            // 获取分类
            $terms = wp_get_post_terms($card_id, 'zhuige_business_card_cat');
            if (!empty($terms)) {
                $card_data['cat_id'] = $terms[0]->term_id;
            }

            $data['card'] = $card_data;
        }

        return $this->success($data);
    }

    /**
     * 创建/编辑商家名片
     */
    public function create($request)
    {
        $card_id = $this->param_int($request, 'card_id', 0);
        $my_user_id = get_current_user_id();

        // 黑名单检查
        if (function_exists('zhuige_auth_is_black') && zhuige_auth_is_black($my_user_id)) {
            return $this->error('操作太频繁了~');
        }

        // 创建商家名片是否要求手机号
        if (ZhuiGe_Xcx::option_value('bcard_mobile_switch')) {
            if (!zhuige_xcx_is_set_mobile($my_user_id)) {
                return $this->error('', 'require_mobile');
            }
        }

        // 创建商家名片是否要求头像昵称
        if (ZhuiGe_Xcx::option_value('bcard_avatar_switch')) {
            if (!zhuige_xcx_is_set_avatar($my_user_id)) {
                return $this->error('', 'require_avatar');
            }
        }

        // 获取参数
        $logo = $this->param($request, 'logo', '');
        if (!$logo) {
            return $this->error('请设置LOGO');
        }

        $title = $this->param($request, 'title', '');
        if (!$title) {
            return $this->error('请输入产品/店铺/服务名称');
        }

        $content = $this->param($request, 'content', '');
        $cat_id = $this->param_int($request, 'cat_id', 0);
        if (!$cat_id) {
            return $this->error('请选择分类');
        }

        // 检查敏感信息
        $os = $this->param($request, 'os', 'wx');
        if (!$this->msg_sec_check($title . $content, $os)) {
            return $this->error('请勿发布敏感信息');
        }

        // 准备文章数据
        $post_data = [
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => 'pending', // 需要审核
            'post_type' => 'zhuige_business_card',
            'post_author' => $my_user_id,
        ];

        if ($card_id) {
            // 编辑模式
            $existing_post = get_post($card_id);
            if (!$existing_post || $existing_post->post_type !== 'zhuige_business_card') {
                return $this->error('商家名片不存在');
            }

            // 检查权限
            if ($existing_post->post_author != $my_user_id && !current_user_can('edit_others_posts')) {
                return $this->error('无权限编辑此商家名片');
            }

            $post_data['ID'] = $card_id;
            $result = wp_update_post($post_data);
        } else {
            // 创建模式
            $result = wp_insert_post($post_data);
            $card_id = $result;
        }

        if (!$result) {
            return $this->error('系统异常');
        }

        // 设置分类
        wp_set_post_terms($card_id, $cat_id, 'zhuige_business_card_cat');

        // 保存扩展信息
        $options = [
            'logo' => json_decode($logo, true),
            'images' => json_decode($this->param($request, 'images', '[]'), true),
            'contact' => $this->param($request, 'contact', ''),
            'phone' => $this->param($request, 'phone', ''),
            'longitude' => $this->param($request, 'longitude', ''),
            'latitude' => $this->param($request, 'latitude', ''),
            'marker' => $this->param($request, 'marker', ''),
            'address' => $this->param($request, 'address', ''),
            'position' => $this->param($request, 'position', ''),
            'web' => $this->param($request, 'web', ''),
            'company' => $this->param($request, 'company', ''),
            'qrcode' => json_decode($this->param($request, 'qrcode', '{}'), true),
        ];

        update_post_meta($card_id, 'zhuige_business_card_options', $options);

        return $this->success();
    }

    /**
     * 获取商家名片详情
     */
    public function get_detail($request)
    {
        $card_id = $this->param_int($request, 'card_id', 0);
        if (!$card_id) {
            return $this->error('缺少参数');
        }

        $post = get_post($card_id);
        if (!$post || $post->post_type !== 'zhuige_business_card') {
            return $this->error('商家名片不存在');
        }

        if ($post->post_status !== 'publish') {
            return $this->error('商家名片未发布');
        }

        // 记录浏览量
        $this->record_view($card_id);

        $data = [];
        $data['title'] = $post->post_title;
        $data['content'] = $post->post_content;

        // 获取标签
        $data['tags'] = zhuige_business_card_get_tags($card_id);

        // 获取评论数
        $data['comment_count'] = get_comments_number($card_id);

        // 获取浏览数
        $data['views'] = zhuige_business_card_get_views($card_id);

        // 获取点赞列表
        $data['like_list'] = zhuige_business_card_get_like_list($card_id);
        $data['like_count'] = count($data['like_list']);

        // 检查当前用户是否点赞
        $my_user_id = get_current_user_id();
        $data['is_like'] = 0;
        $data['is_comment'] = 0;
        if ($my_user_id) {
            $data['is_like'] = zhuige_business_card_check_user_liked($card_id, $my_user_id) ? 1 : 0;
            $data['is_comment'] = zhuige_business_card_check_user_commented($card_id, $my_user_id) ? 1 : 0;
        }

        // 获取扩展信息
        $options = get_post_meta($card_id, 'zhuige_business_card_options', true);
        if (!$options) {
            $options = [];
        }

        $data['logo'] = ZhuiGe_Xcx::option_image_url($options['logo'], 'placeholder.jpg');

        // 处理轮播图数据结构
        $images_raw = $options['images'] ?? [];
        $images = [];

        if (is_array($images_raw)) {
            foreach ($images_raw as $img) {
                if (is_array($img) && isset($img['url'])) {
                    $images[] = [
                        'image' => $img['url']
                    ];
                } elseif (is_array($img) && isset($img['image'])) {
                    // 处理已经是正确格式的数据
                    $images[] = [
                        'image' => is_array($img['image']) ? $img['image']['url'] : $img['image']
                    ];
                } elseif (is_string($img)) {
                    $images[] = [
                        'image' => $img
                    ];
                }
            }
        }
        $data['images'] = $images;

        // 名片信息
        $card_info = [
            'driect_link_switch' => $options['driect_link_switch'] ?? '0',
            'driect_link' => $options['driect_link'] ?? '',
            'contact' => $options['contact'] ?? '',
            'phone' => $options['phone'] ?? '',
            'position' => $options['position'] ?? '',
            'web' => $options['web'] ?? '',
            'company' => $options['company'] ?? '',
            'qrcode' => $options['qrcode'] ?? null,
            'cardui' => get_option('zhuige-xcx')['bcard_cardui'] ?? '1',
        ];

        // 位置信息
        if (!empty($options['longitude']) && !empty($options['latitude'])) {
            $card_info['location'] = [
                'marker' => $options['marker'] ?? '',
                'address' => $options['address'] ?? '',
                'longitude' => $options['longitude'],
                'latitude' => $options['latitude'],
            ];
        }

        $data['card'] = $card_info;

        // 评分
        $data['score'] = (float)zhuige_business_card_get_score($card_id);

        // 评论开关
        $data['comment_switch'] = ZhuiGe_Xcx::option_value('bcard_comment_switch') ? 1 : 0;

        // 评论权限检查
        $data['comment_require_avatar'] = 0;
        $data['comment_require_mobile'] = 0;
        if ($data['comment_switch'] && $my_user_id) {
            $comment_permission = ZhuiGe_Xcx::option_value('bcard_comment_permission') ?: [];

            // 评论是否要求头像昵称
            if (($comment_permission['comment_avatar_switch'] ?? '0') == '1' && !zhuige_xcx_is_set_avatar($my_user_id)) {
                $data['comment_require_avatar'] = 1;
            }

            // 评论是否要求手机号
            if (($comment_permission['comment_mobile_switch'] ?? '0') == '1' && !zhuige_xcx_is_set_mobile($my_user_id)) {
                $data['comment_require_mobile'] = 1;
            }
        }

        // 推荐相关
        $cat_terms = wp_get_post_terms($card_id, 'zhuige_business_card_cat');
        if (!empty($cat_terms)) {
            $data['rec_cat'] = [
                'id' => $cat_terms[0]->term_id,
                'name' => $cat_terms[0]->name
            ];

            // 获取相关推荐
            $data['recs'] = $this->get_related_cards($card_id, $cat_terms[0]->term_id);
        }

        // 推广相关
        $data['is_show_promotion'] = 0;
        $data['is_show_edit'] = 0;
        $data['is_promotion'] = 0;

        if ($my_user_id) {
            // 是否显示编辑按钮
            if ($post->post_author == $my_user_id || current_user_can('edit_others_posts')) {
                $data['is_show_edit'] = 1;
            }

            // 是否显示推广按钮
            if ($post->post_author == $my_user_id) {
                $data['is_show_promotion'] = 1;
                $data['is_promotion'] = $this->check_promotion_active($card_id) ? 1 : 0;
            }
        }

        return $this->success($data);
    }



    /**
     * 记录浏览量
     */
    private function record_view($card_id)
    {
        global $wpdb;
        $my_user_id = get_current_user_id();

        // 添加浏览记录
        if ($my_user_id) {
            $table_post_view = $wpdb->prefix . 'zhuige_xcx_post_view';
            $post_view_id = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT `id` FROM `$table_post_view` WHERE `user_id`=%d AND `post_id`=%d",
                    $my_user_id,
                    $card_id
                )
            );
            if (!$post_view_id) {
                $wpdb->insert($table_post_view, [
                    'user_id' => $my_user_id,
                    'post_id' => $card_id,
                    'post_status' => 'publish',
                    'time' => time()
                ]);
            }
        }

        // 更新浏览数（使用 post_meta）
        $post_views = zhuige_business_card_get_views($card_id);
        if (!update_post_meta($card_id, 'zhuige_views', ($post_views + 1))) {
            add_post_meta($card_id, 'zhuige_views', 1, true);
        }

        // 添加积分
        if (function_exists('zhuige_xcx_add_user_score_by_task')) {
            zhuige_xcx_add_user_score_by_task('view', 'zhuige_business_card,' . $card_id);
        }

        // 文章查看钩子
        $post = get_post($card_id);
        do_action('zhuige_xcx_post_view', [
            'post_id' => $card_id,
            'user_id' => $post->post_author,
            'post_type' => $post->post_type,
            'view_count' => $post_views + 1,
            'like_count' => count(zhuige_business_card_get_like_list($card_id)),
            'fav_count' => 0, // 商家名片暂不支持收藏
            'comment_count' => get_comments_number($card_id)
        ]);
    }









    /**
     * 获取相关推荐
     */
    private function get_related_cards($card_id, $cat_id, $limit = 5)
    {
        $args = [
            'post_type' => 'zhuige_business_card',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'post__not_in' => [$card_id],
            'orderby' => 'rand',
            'tax_query' => [
                [
                    'taxonomy' => 'zhuige_business_card_cat',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ]
            ]
        ];

        $query = new WP_Query($args);
        $recs = [];

        foreach ($query->posts as $post) {
            $recs[] = zhuige_business_card_format($post);
        }

        return $recs;
    }

    /**
     * 检查推广是否激活
     */
    private function check_promotion_active($card_id)
    {
        global $wpdb;
        $table_promotions = $wpdb->prefix . 'zhuige_bcard_promotions';

        $current_time = time();
        return (bool)$wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_promotions WHERE card_id = %d AND status = 'active' AND start_time <= %d AND end_time >= %d",
            $card_id, $current_time, $current_time
        ));
    }
}

// 注册控制器
ZhuiGe_Xcx::$rest_controllers[] = new ZhuiGe_Xcx_Bcard_Controller();
