<?php

/**
 * 追格小程序 - 商家名片功能函数
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 商家名片格式化
 */
if (!function_exists('zhuige_business_card_format')) {
    function zhuige_business_card_format($post)
    {
        $item = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'excerpt' => wp_trim_words($post->post_content, 20, '...'),
        ];

        // 获取扩展信息
        $options = get_post_meta($post->ID, 'zhuige_business_card_options', true);
        if (!$options) {
            $options = [];
        }

        $item['logo'] = ZhuiGe_Xcx::option_image_url($options['logo'], 'placeholder.jpg');
        $item['driect_link_switch'] = $options['driect_link_switch'] ?? '0';
        $item['driect_link'] = $options['driect_link'] ?? '';

        // 获取标签
        $item['tags'] = zhuige_business_card_get_tags($post->ID);

        // 获取评分
        $item['score'] = (float)zhuige_business_card_get_score($post->ID);

        // 置顶标识
        $item['stick'] = get_post_meta($post->ID, 'zhuige_business_card_stick', true) ? 1 : 0;

        return $item;
    }
}

/**
 * 获取商家名片评分
 * 优先从后台配置获取，当配置为0时才从数据库统计
 */
if (!function_exists('zhuige_business_card_get_score')) {
    function zhuige_business_card_get_score($card_id)
    {
        // 优先从后台配置获取评分
        $options = get_post_meta($card_id, 'zhuige_business_card_options', true);
        if (!empty($options['score']) && floatval($options['score']) > 0) {
            return floatval($options['score']);
        }

        // 配置为0时，从数据库统计评分
        global $wpdb;
        $scores = $wpdb->get_col($wpdb->prepare(
            "SELECT cm.meta_value
             FROM {$wpdb->commentmeta} cm
             INNER JOIN {$wpdb->comments} c ON cm.comment_id = c.comment_ID
             WHERE c.comment_post_ID = %d
             AND cm.meta_key = 'zhuige_xcx_score'
             AND c.comment_approved = '1'
             AND cm.meta_value > 0",
            $card_id
        ));

        if (empty($scores)) {
            return 0;
        }

        $avg_score = array_sum($scores) / count($scores);
        return (float)number_format($avg_score, 1);
    }
}

/**
 * 获取商家名片浏览量
 */
if (!function_exists('zhuige_business_card_get_views')) {
    function zhuige_business_card_get_views($card_id)
    {
        // 使用 post_meta 存储浏览数（与其他模块保持一致）
        return (int) get_post_meta($card_id, 'zhuige_views', true);
    }
}

/**
 * 获取商家名片标签
 */
if (!function_exists('zhuige_business_card_get_tags')) {
    function zhuige_business_card_get_tags($card_id)
    {
        $tags = wp_get_post_terms($card_id, 'zhuige_business_card_tag');
        $tag_list = [];
        foreach ($tags as $tag) {
            $tag_list[] = [
                'id' => $tag->term_id,
                'name' => $tag->name
            ];
        }
        return $tag_list;
    }
}

/**
 * 获取商家名片点赞列表
 */
if (!function_exists('zhuige_business_card_get_like_list')) {
    function zhuige_business_card_get_like_list($card_id)
    {
        global $wpdb;
        $table_likes = $wpdb->prefix . 'zhuige_xcx_post_like';

        $user_ids = $wpdb->get_col($wpdb->prepare(
            "SELECT user_id FROM $table_likes WHERE post_id = %d ORDER BY id DESC LIMIT 20",
            $card_id
        ));

        $like_list = [];
        foreach ($user_ids as $user_id) {
            $like_list[] = [
                'user_id' => $user_id,
                'avatar' => ZhuiGe_Xcx::user_avatar($user_id)
            ];
        }

        return $like_list;
    }
}

/**
 * 检查用户是否点赞商家名片
 */
if (!function_exists('zhuige_business_card_check_user_liked')) {
    function zhuige_business_card_check_user_liked($card_id, $user_id)
    {
        global $wpdb;
        $table_likes = $wpdb->prefix . 'zhuige_xcx_post_like';

        return (bool)$wpdb->get_var($wpdb->prepare(
            "SELECT id FROM $table_likes WHERE post_id = %d AND user_id = %d",
            $card_id, $user_id
        ));
    }
}

/**
 * 检查用户是否评论过商家名片
 */
if (!function_exists('zhuige_business_card_check_user_commented')) {
    function zhuige_business_card_check_user_commented($card_id, $user_id)
    {
        $comments = get_comments([
            'post_id' => $card_id,
            'user_id' => $user_id,
            'count' => true
        ]);
        return $comments > 0;
    }
}