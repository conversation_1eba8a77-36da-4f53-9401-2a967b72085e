<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

function zhuige_xcx_bcard_create_custom_post_type()
{
    /**
     * 商家名片
     */
    $zhuige_business_card_labels = array(
        'name'               => '追格商家名片',
        'singular_name'      => '追格商家名片',
        'add_new'            => '新建商家名片',
        'add_new_item'       => '新建一个商家名片',
        'edit_item'          => '编辑商家名片',
        'new_item'           => '新商家名片',
        'all_items'          => '所有商家名片',
        'view_item'          => '查看商家名片',
        'search_items'       => '搜索商家名片',
        'not_found'          => '没有找到有关商家名片',
        'not_found_in_trash' => '回收站里面没有相关商家名片',
        'parent_item_colon'  => '',
        'menu_name'          => '追格商家名片'
    );
    $zhuige_business_card_args = array(
        'labels'        => $zhuige_business_card_labels,
        'description'   => '我们网站的商家名片信息',
        'public'        => true,
        // 'show_in_menu'  => 'custompage',
        // 'menu_position' => 5,
        'supports'      => array('title', 'editor', 'author'),
        'has_archive'   => true
    );
    register_post_type('zhuige_business_card', $zhuige_business_card_args);

    // 追格商家名片-名片设置
    $zhuige_business_card_option = 'zhuige_business_card_options';
    CSF::createMetabox($zhuige_business_card_option, array(
        'title'        => '追格商家名片-名片设置',
        'post_type'    => 'zhuige_business_card',
        // 'show_restore' => true,
    ));

    $card_options = [
        array(
            'id'      => 'logo',
            'type'    => 'media',
            'title'   => 'LOGO',
            'library' => 'image',
        ),

        array(
            'id'     => 'images',
            'type'   => 'group',
            'title'  => '相册图片',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),
            ),
        ),

        array(
            'id'    => 'contact',
            'type'  => 'text',
            'title' => '联系人',
        ),

        array(
            'id'    => 'phone',
            'type'  => 'text',
            'title' => '电话',
        ),

        array(
            'id'    => 'position',
            'type'  => 'text',
            'title' => '职位',
        ),

        array(
            'id'    => 'company',
            'type'  => 'text',
            'title' => '公司名称',
        ),

        array(
            'id'    => 'web',
            'type'  => 'text',
            'title' => '网址',
        ),

        array(
            'id'    => 'marker',
            'type'  => 'text',
            'title' => '位置标志物',
        ),

        array(
            'id'    => 'address',
            'type'  => 'text',
            'title' => '地址',
        ),

        array(
            'id'    => 'longitude',
            'type'  => 'text',
            'title' => '经度',
        ),

        array(
            'id'    => 'latitude',
            'type'  => 'text',
            'title' => '纬度',
        ),

        array(
            'id'      => 'qrcode',
            'type'    => 'media',
            'title'   => '二维码',
            'library' => 'image',
        ),

        array(
            'id'          => 'score',
            'type'        => 'spinner',
            'title'       => '评分',
            'subtitle'    => 'max:5 | min:0 | step:0.1',
            'max'         => 5,
            'min'         => 0,
            'step'        => 0.1,
            'default'     => 5,
        ),



        array(
            'id'    => 'driect_link_switch',
            'type'  => 'switcher',
            'title' => '启用直链',
            'default' => '0'
        ),

        array(
            'id'    => 'driect_link',
            'type'  => 'text',
            'title' => '直链地址',
            'dependency' => array('driect_link_switch', '==', '1'),
        ),
    ];

    CSF::createSection($zhuige_business_card_option, array(
        'fields' => $card_options
    ));

    /**
     * 商家名片分类
     */
    $zhuige_business_card_cat_labels = array(
        'name'              => '分类',
        'singular_name'     => '分类',
        'search_items'      => '搜索分类',
        'all_items'         => '所有分类',
        'parent_item'       => '该分类的上级分类',
        'parent_item_colon' => '该分类的上级分类：',
        'edit_item'         => '编辑分类',
        'update_item'       => '更新分类',
        'add_new_item'      => '添加新的分类',
        'new_item_name'     => '分类',
        'menu_name'         => '分类',
    );
    $zhuige_business_card_cat_args = array(
        'hierarchical' => true,
        'labels' => $zhuige_business_card_cat_labels,
        // 'show_ui'           => true,
        // 'show_admin_column' => true,
        // 'query_var'         => true,
        // 'rewrite'           => array( 'slug' => 'zhuige_business_card_cat' ),
    );
    register_taxonomy('zhuige_business_card_cat', 'zhuige_business_card', $zhuige_business_card_cat_args);

    /**
     * 商家名片标签
     */
    $zhuige_business_card_tag_labels = array(
        'name'              => '标签',
        'singular_name'     => '标签',
        'search_items'      => '搜索标签',
        'all_items'         => '所有标签',
        'parent_item'       => '该标签的上级标签',
        'parent_item_colon' => '该标签的上级标签：',
        'edit_item'         => '编辑标签',
        'update_item'       => '更新标签',
        'add_new_item'      => '添加新的标签',
        'new_item_name'     => '标签',
        'menu_name'         => '标签',
        'separate_items_with_commas' => '多个标签请用英文逗号（,）分开',
        'choose_from_most_used' => '从常用标签中选择',
        'not_found' => '未找到标签'
    );
    $zhuige_business_card_tag_args = array(
        'hierarchical' => false,
        'labels' => $zhuige_business_card_tag_labels,
        // 'show_ui'           => true,
        // 'show_admin_column' => true,
        // 'query_var'         => true,
        // 'rewrite'           => array( 'slug' => 'zhuige_business_card_tag' ),
    );
    register_taxonomy('zhuige_business_card_tag', 'zhuige_business_card', $zhuige_business_card_tag_args);

    // 商家名片分类选项
    $business_card_cat_options = 'zhuige_business_card_cat_options';
    CSF::createTaxonomyOptions($business_card_cat_options, array(
        'taxonomy' => 'zhuige_business_card_cat',
    ));
    CSF::createSection($business_card_cat_options, array(
        'fields' => array(
            array(
                'id'      => 'logo',
                'type'    => 'media',
                'title'   => 'LOGO',
                'library' => 'image',
            ),
        )
    ));
}

ZhuiGe_Xcx::$post_types[] = ['id' => 'zhuige_business_card', 'name' => '商家名片', 'link' => '/pages/business-card/detail/detail'];
add_action('init', 'zhuige_xcx_bcard_create_custom_post_type');
