<?php {
    // 商家名片推广记录表
    $table_zhuige_bcard_promotions = $wpdb->prefix . 'zhuige_bcard_promotions';
    $bcard_promotions_sql = "CREATE TABLE IF NOT EXISTS `$table_zhuige_bcard_promotions` (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `card_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '名片ID',
        `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
        `amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '推广金额',
        `days` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '推广天数',
        `start_time` int(10) UNSIGNED DEFAULT '0' COMMENT '开始时间',
        `end_time` int(10) UNSIGNED DEFAULT '0' COMMENT '结束时间',
        `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态(active,expired)',
        `time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        KEY `card_id` (`card_id`),
        KEY `user_id` (`user_id`),
        KEY `status` (`status`)
    ) $charset_collate;";
    dbDelta($bcard_promotions_sql);
}
