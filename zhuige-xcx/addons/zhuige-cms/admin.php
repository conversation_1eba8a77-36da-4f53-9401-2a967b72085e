<?php

/**
 * 追格小程序 - CMS文章后台管理
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 新闻资讯
//
CSF::createSection($prefix, array(
    'id'    => 'cms',
    'title' => '新闻资讯',
    'icon'  => 'fas fa-file-alt',
));

//
// 首页设置
//
CSF::createSection($prefix, array(
    'parent' => 'cms',
    'title' => '首页设置',
    'icon'  => 'fas fa-home',
    'fields' => array(

        array(
            'id'     => 'cms_slides',
            'type'   => 'group',
            'title'  => '幻灯片',
            'subtitle' => '在文章首页顶部显示的轮播图片',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                    'after' => '<a href="https://www.zhuige.com/docs/zg/216.html" target="_blank">图片规格建议</a>',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                    'default' => 'https://www.zhuige.com',
                    'after' => '<a href="https://www.zhuige.com/docs/zg/215.html" target="_blank">链接格式说明</a>',
                ),
            ),
        ),

        array(
            'id'     => 'cms_navs',
            'type'   => 'group',
            'title'  => '导航图标',
            'subtitle' => '在文章首页显示的导航图标',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

        array(
            'id'          => 'cms_home_cats',
            'type'        => 'select',
            'title'       => '首页分类',
            'placeholder' => '选择分类',
            'chosen'      => true,
            'multiple'    => true,
            'sortable'    => true,
            'options'     => 'categories',
            'query_args'  => array(
                'taxonomy'  => 'category',
            ),
        ),

        array(
            'id'          => 'cms_home_tags',
            'type'        => 'select',
            'title'       => '热门标签',
            'placeholder' => '选择标签',
            'chosen'      => true,
            'multiple'    => true,
            'sortable'    => true,
            'options'     => 'categories',
            'query_args'  => array(
                'taxonomy'  => 'post_tag',
            ),
        ),

        array(
            'id'          => 'cms_home_tags_width',
            'type'        => 'number',
            'title'       => '标签容器宽度',
            'class'       => 'text-class',
            'description' => '热门标签容器宽度，单位rpx',
            'default'     => 750,
        ),

        array(
            'id'          => 'cms_cat_hide',
            'type'        => 'select',
            'title'       => '隐藏分类',
            'subtitle'    => '选择的分类将不在小程序中显示',
            'placeholder' => '选择要隐藏的分类',
            'chosen'      => true,
            'multiple'    => true,
            'options'     => 'categories',
            'query_args'  => array(
                'taxonomy'  => 'category',
            ),
        ),

    )
));

//
// 详情设置
//
CSF::createSection($prefix, array(
    'parent' => 'cms',
    'title' => '详情设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(

        array(
            'id'      => 'cms_ad_up',
            'type'    => 'fieldset',
            'title'   => '上方广告',
            'subtitle' => '在文章内容上方显示的广告',
            'fields'  => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

        array(
            'id'      => 'cms_ad_down',
            'type'    => 'fieldset',
            'title'   => '下方广告',
            'subtitle' => '在文章内容下方显示的广告',
            'fields'  => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

        array(
            'id'    => 'cms_copyright',
            'type'  => 'textarea',
            'title' => '版权信息',
            'subtitle' => '在文章详情页底部显示的版权信息',
        ),

        array(
            'id'      => 'cms_poster',
            'type'    => 'fieldset',
            'title'   => '分享海报',
            'subtitle' => '文章分享海报设置',
            'fields'  => array(
                array(
                    'id'      => 'bg',
                    'type'    => 'media',
                    'title'   => '背景图',
                    'library' => 'image',
                ),
                array(
                    'id'      => 'thumb',
                    'type'    => 'media',
                    'title'   => '默认缩略图',
                    'subtitle' => '当文章没有缩略图时使用',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '默认标题',
                    'subtitle' => '当作者没有签名时使用',
                    'default' => '分享精彩内容',
                ),
            ),
        ),

    )
));

//
// 微信广告设置
//
CSF::createSection($prefix, array(
    'parent' => 'cms',
    'title' => '微信广告',
    'icon'  => 'fas fa-ad',
    'fields' => array(

        array(
            'id'     => 'cms_wx_ad_list',
            'type'   => 'fieldset',
            'title'  => '列表页广告',
            'subtitle' => '首页和分类列表页的广告设置',
            'fields' => array(
                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '开启/停用',
                    'subtitle' => '是否在列表页显示广告',
                    'default' => false,
                ),
                array(
                    'id'    => 'banner',
                    'type'  => 'text',
                    'title' => '横幅广告ID',
                    'dependency' => array('switch', '==', true),
                ),
                array(
                    'id'    => 'frequency',
                    'type'  => 'number',
                    'title' => '广告频率',
                    'subtitle' => '每隔多少篇文章插入一次广告',
                    'default' => 5,
                    'dependency' => array('switch', '==', true),
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '广告标题',
                    'default' => '广告',
                    'dependency' => array('switch', '==', true),
                ),
                array(
                    'id'    => 'desc',
                    'type'  => 'text',
                    'title' => '广告描述',
                    'default' => '点击查看详情',
                    'dependency' => array('switch', '==', true),
                ),
            ),
        ),

        array(
            'id'     => 'cms_wx_ad_detail',
            'type'   => 'fieldset',
            'title'  => '详情页广告',
            'subtitle' => '文章详情页的广告设置',
            'fields' => array(
                array(
                    'id'    => 'banner_switch',
                    'type'  => 'switcher',
                    'title' => '横幅广告开关',
                    'subtitle' => '是否在详情页显示横幅广告',
                    'default' => false,
                ),
                array(
                    'id'    => 'banner',
                    'type'  => 'text',
                    'title' => '横幅广告ID',
                    'dependency' => array('banner_switch', '==', true),
                ),
                array(
                    'id'    => 'chaping_switch',
                    'type'  => 'switcher',
                    'title' => '插屏广告开关',
                    'subtitle' => '是否在详情页显示插屏广告',
                    'default' => false,
                ),
                array(
                    'id'    => 'chaping',
                    'type'  => 'text',
                    'title' => '插屏广告ID',
                    'dependency' => array('chaping_switch', '==', true),
                ),
                array(
                    'id'    => 'jili_switch',
                    'type'  => 'switcher',
                    'title' => '激励视频广告开关',
                    'subtitle' => '是否启用激励视频广告（用于阅读限制）',
                    'default' => false,
                ),
                array(
                    'id'    => 'jili',
                    'type'  => 'text',
                    'title' => '激励视频广告ID',
                    'dependency' => array('jili_switch', '==', true),
                ),
            ),
        ),

    )
));

//
// 三图广告设置
//
CSF::createSection($prefix, array(
    'parent' => 'cms',
    'title' => '三图广告',
    'icon'  => 'fas fa-th',
    'fields' => array(

        array(
            'id'    => 'cms_events_title',
            'type'  => 'text',
            'title' => '标题',
            'default' => '热门推荐',
        ),

        array(
            'id'    => 'cms_events_description',
            'type'  => 'text',
            'title' => '描述',
            'default' => '精选内容推荐',
        ),

        array(
            'id'     => 'cms_events',
            'type'   => 'group',
            'title'  => '推荐内容',
            'fields' => array(
                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),
                array(
                    'id'    => 'badge',
                    'type'  => 'text',
                    'title' => '角标',
                ),
                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),
            ),
        ),

    )
));
