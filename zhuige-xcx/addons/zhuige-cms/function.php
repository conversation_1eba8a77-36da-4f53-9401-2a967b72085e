<?php

/**
 * 追格小程序 - CMS新闻资讯功能函数
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 用户认证
 */
if (!function_exists('zhuige_xcx_certify_is_certify')) {
	function zhuige_xcx_certify_is_certify($user_id)
	{
		$certify = [
		];

		if ($user_id == '1') {
		$certify = [
			'status' => 1,
			'cid' => 'guanfang',
			'icon' => 'https://yunjucdn.oss.xiaomaicloud.com/wp-content/uploads/2024/09/lvv.png',
			'name' => '官方媒体'
		];
		}else if ($user_id == '2') {
              		$certify = [
              			'status' => 1,
              			'cid' => 'guanfang',
              			'icon' => 'https://yunjucdn.oss.xiaomaicloud.com/wp-content/uploads/2024/09/lvv.png',
              			'name' => '云居助手团队成员，美食博主'
              		];
              		}
		return $certify;
	}
}

/**
 * 新闻资讯摘要提取
 */
if (!function_exists('zhuige_cms_post_excerpt')) {
    function zhuige_cms_post_excerpt($post, $maxlen = 100)
    {
		$content = str_replace("<br />", "\n", $post->post_content);
		$lines = explode("\n", $content);
		$excerpt = '';
		$len = 0;
		$cnt = 0;
		$suffix = false;
		foreach ($lines as $line) {
			$line = wp_strip_all_tags(apply_filters('the_content', $line));
			$line_len = mb_strlen($line);
			if (($len + $line_len) > $maxlen) {
				$line = wp_trim_words($line, $maxlen - $len, '...');
				$suffix = true;
			}

			if ($excerpt != '') {
				$excerpt = $excerpt . "\n";
			}
			$excerpt = $excerpt . $line;

			$len = $len + $line_len;
			if ($len > $maxlen) {
				return $excerpt;
			}

			$cnt = $cnt + 1;
			if ($cnt >= 3) {
				if (!$suffix) {
					$excerpt = $excerpt . "\n...";
				}
				return $excerpt;
			}
		}

		return $excerpt;
    }
}

/**
 * 获取新闻资讯缩略图
 */
if (!function_exists('zhuige_cms_post_thumbnails')) {
    function zhuige_cms_post_thumbnails($post)
    {
        $thumbnails = [];

        // 首先添加特色图片
        $thumbnail_id = get_post_thumbnail_id($post->ID);
        if ($thumbnail_id) {
            $thumbnail_url = wp_get_attachment_image_url($thumbnail_id, 'large');
            if ($thumbnail_url) {
                $thumbnails[] = $thumbnail_url;
            }
        }

        // 从文章内容中提取图片
        preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $post->post_content, $matches);
        if (!empty($matches[1])) {
            foreach ($matches[1] as $img_url) {
                if (!in_array($img_url, $thumbnails)) {
                    $thumbnails[] = $img_url;
                }
                // 最多3张图片
                if (count($thumbnails) >= 3) {
                    break;
                }
            }
        }

        return $thumbnails;
    }
}

/**
 * CMS新闻资讯格式化（用于列表页）
 */
if (!function_exists('zhuige_cms_post_format')) {
    function zhuige_cms_post_format($post)
    {
        $item = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'comment_count' => strval($post->comment_count),
            'time' => get_the_date('Y-m-d', $post->ID),
            'views' => 0,
            'likes' => 0,
            'stick' => 0,
            'badge' => '',
            'read_limit' => 'free',
            'cost_price' => '',
            'cost_score' => '',
            'cost_ios_switch' => '0',
            'driect_link_switch' => '0',
            'driect_link' => ''
        ];

        // 摘要
        $item['excerpt'] = zhuige_cms_post_excerpt($post);

        // 缩略图
        $thumbnails = zhuige_cms_post_thumbnails($post);
        $item['thumbnails'] = $thumbnails;
        $item['thumbnail'] = !empty($thumbnails) ? $thumbnails[0] : '';

        // 作者信息
        $author = zhuige_xcx_author_info($post->post_author);
        if ($author) {
            // 添加签名字段
            $author['sign'] = get_user_meta($post->post_author, 'zhuige_xcx_user_sign', true) ?: '';

            // 添加关注状态（详情页需要）
            $current_user_id = get_current_user_id();
            if ($current_user_id && $current_user_id != $post->post_author) {
                global $wpdb;
                $follow_table = $wpdb->prefix . 'zhuige_xcx_follow_user';
                $is_follow = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(*) FROM $follow_table WHERE user_id = %d AND follow_user_id = %d",
                    $current_user_id, $post->post_author
                ));
                $author['is_follow'] = $is_follow > 0;
            } else {
                $author['is_follow'] = false;
            }

            $item['author'] = $author;
        } else {
            $item['author'] = [];
        }

        // 统计数据
        $item['views'] = (int) get_post_meta($post->ID, 'zhuige_post_views', true);
        $item['likes'] = (int) get_post_meta($post->ID, 'like_count', true);

        // 置顶状态
        $sticky_posts = get_option('sticky_posts');
        $item['stick'] = in_array($post->ID, $sticky_posts) ? 1 : 0;

        // 文章自定义设置
        $options = get_post_meta($post->ID, 'zhuige-cms-post-option', true);
        if ($options && is_array($options)) {
            $item['read_limit'] = $options['read_limit'] ?? 'free';
            $item['badge'] = $options['badge'] ?? '';
            $item['driect_link_switch'] = ($options['driect_link_switch'] ?? false) ? '1' : '0';
            $item['driect_link'] = $options['driect_link'] ?? '';
            $item['cost_ios_switch'] = ($options['cost_ios_switch'] ?? false) ? '1' : '0';

            // 付费设置
            if ($item['read_limit'] === 'cost') {
                $item['cost_price'] = strval($options['cost_price'] ?? '');
            }
            if ($item['read_limit'] === 'score') {
                $item['cost_score'] = strval($options['cost_score'] ?? '');
            }
        }

        return $item;
    }
}

/**
 * 获取新闻资讯点赞用户列表
 */
if (!function_exists('zhuige_cms_post_like_list')) {
    function zhuige_cms_post_like_list($post_id)
    {
        global $wpdb;
        $table_post_like = $wpdb->prefix . 'zhuige_xcx_post_like';
        $like_users = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT user_id FROM `$table_post_like` WHERE post_id=%d ORDER BY id DESC",
                $post_id,
            )
        );

        $like_list = [];
		if (!empty($like_users)) {
			foreach ($like_users as $user) {
				$like_list[] = [
					'user_id' => $user->user_id,
					'avatar' => ZhuiGe_Xcx::user_avatar($user->user_id),
				];
			}
		}

        return $like_list;
    }
}

/**
 * CMS新闻资讯详情格式化
 */
if (!function_exists('zhuige_cms_post_detail_format')) {
    function zhuige_cms_post_detail_format($post)
    {
        $item = zhuige_cms_post_format($post);
        // 用户状态
        $current_user_id = get_current_user_id();

        // 详情页特有字段
        $item['content'] = apply_filters('the_content', $post->post_content);
        $item['like_list'] = zhuige_cms_post_like_list($post->ID);

        // 文章标签
        $tags = [];
        $post_tags = get_the_tags($post->ID);
        if ($post_tags) {
            foreach ($post_tags as $tag) {
                $tags[] = [
                    'id' => $tag->term_id,
                    'name' => $tag->name
                ];
            }
        }
        $item['tags'] = $tags;

        // 收藏数
        $item['favorites'] = (int) get_post_meta($post->ID, 'zhuige_favorites', true);

        // 评论设置
        $item['comment_switch'] = ZhuiGe_Xcx::option_value('comment_switch') ? 1 : 0;
        $item['comment_require_mobile'] = ZhuiGe_Xcx::option_value('comment_mobile_switch') ? 1 : 0;
        // 评论权限检查
        if ($item['comment_switch'] && $current_user_id) {
            // 评论是否要求头像昵称
            if (ZhuiGe_Xcx::option_value('comment_avatar_switch')) {
                if (!zhuige_xcx_is_set_avatar($current_user_id)) {
                    $item['comment_require_avatar2'] = 1;
                }
            }

            // 评论是否要求手机号
            if (ZhuiGe_Xcx::option_value('comment_mobile_switch')) {
                if (!zhuige_xcx_is_set_mobile($current_user_id)) {
                    $item['comment_require_mobile2'] = 1;
                }
            }
        }

        // 阅读限制开关和提示
        $item['read_limit_switch'] = 1;
        $options = get_post_meta($post->ID, 'zhuige-cms-post-option', true);
        if ($options && isset($options['read_limit_tip'])) {
            $item['read_limit_tip'] = $options['read_limit_tip'];
        } else {
            $item['read_limit_tip'] = '';
        }

        if ($current_user_id) {
            global $wpdb;

            // 检查是否点赞
            $like_table = $wpdb->prefix . 'zhuige_xcx_post_like';
            $is_like = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $like_table WHERE post_id = %d AND user_id = %d",
                $post->ID, $current_user_id
            ));

            // 检查是否收藏
            $favorite_table = $wpdb->prefix . 'zhuige_xcx_post_favorite';
            $is_favorite = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $favorite_table WHERE post_id = %d AND user_id = %d",
                $post->ID, $current_user_id
            ));

            // 检查是否评论过
            $comments = get_comments([
                'post_id' => $post->ID,
                'user_id' => $current_user_id,
                'count' => true
            ]);

            $item['user'] = [
                'is_like' => $is_like > 0,
                'is_favorite' => $is_favorite > 0,
                'is_comment' => $comments > 0 ? 1 : 0
            ];
        } else {
            $item['user'] = [
                'is_like' => false,
                'is_favorite' => false,
                'is_comment' => 0
            ];
        }

        // 底部按钮
        if ($options && is_array($options)) {
            if (!empty($options['button1']['title']) && !empty($options['button1']['link'])) {
                $item['button1'] = [
                    'title' => $options['button1']['title'],
                    'link' => $options['button1']['link']
                ];
            }
            if (!empty($options['button2']['title']) && !empty($options['button2']['link'])) {
                $item['button2'] = [
                    'title' => $options['button2']['title'],
                    'link' => $options['button2']['link']
                ];
            }
        }

        // 获取相关推荐
        $item['recs'] = zhuige_cms_get_related_posts($post->ID, 4);

        return $item;
    }
}

/**
 * 获取相关推荐新闻资讯
 */
if (!function_exists('zhuige_cms_get_related_posts')) {
    function zhuige_cms_get_related_posts($post_id, $limit = 4)
    {
        $post = get_post($post_id);
        if (!$post) {
            return [];
        }

        // 获取当前文章的分类
        $categories = wp_get_post_categories($post_id);

        $args = [
            'posts_per_page' => $limit,
            'post_type' => 'post',
            'post_status' => 'publish',
            'post__not_in' => [$post_id],
            'orderby' => 'rand'
        ];

        if (!empty($categories)) {
            $args['category__in'] = $categories;
        }

        // 过滤隐藏分类
        $hide_cats = ZhuiGe_Xcx::option_value('cms_cat_hide');
        if (!empty($hide_cats)) {
            $args['category__not_in'] = $hide_cats;
        }

        $query = new WP_Query($args);
        $posts = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $posts[] = zhuige_cms_post_format(get_post());
            }
            wp_reset_postdata();
        }

        return $posts;
    }
}

/**
 * 增加新闻资讯浏览量
 */
if (!function_exists('zhuige_cms_increase_views')) {
    function zhuige_cms_increase_views($post_id)
    {
        $views = get_post_meta($post_id, 'zhuige_post_views', true);
        $views = $views ? intval($views) + 1 : 1;
        update_post_meta($post_id, 'zhuige_post_views', $views);
        return $views;
    }
}

