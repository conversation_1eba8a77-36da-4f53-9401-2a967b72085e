<?php

/**
 * 追格小程序 - CMS新闻资讯控制器
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

class ZhuiGe_Xcx_Cms_Controller extends ZhuiGe_Xcx_Base_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->module = 'cms';
        $this->routes = [
            'setting_home' => 'get_setting_home',
            'setting_detail' => 'get_setting_detail',
            'setting_list' => 'get_setting_list',

            'last' => 'get_last_posts',
            'category' => 'get_category_posts',
            'tag_list' => 'get_tag_posts',

            'detail' => 'get_post_detail',
            'content' => 'get_post_content',

            'cats' => 'get_cats',
            'tags' => 'get_tags',

            'wx_post_pay' => 'wx_post_pay',
            'score_post_pay' => 'score_post_pay',
        ];
    }

    /**
     * 获取首页设置
     */
    public function get_setting_home($request)
    {
        $data = [];

        // 轮播图
        $slides = ZhuiGe_Xcx::option_value('cms_slides');
        $data['slides'] = [];
        if ($slides && is_array($slides)) {
            foreach ($slides as $slide) {
                if (!empty($slide['image'])) {
                    $data['slides'][] = [
                        'image' => ZhuiGe_Xcx::option_image_url($slide['image']),
                        'link' => $slide['link'] ?? ''
                    ];
                }
            }
        }

        // 导航图标
        $navs = ZhuiGe_Xcx::option_value('cms_navs');
        $data['navs'] = [];
        if ($navs && is_array($navs)) {
            foreach ($navs as $nav) {
                if (!empty($nav['image']) && !empty($nav['title'])) {
                    $data['navs'][] = [
                        'image' => ZhuiGe_Xcx::option_image_url($nav['image']),
                        'title' => $nav['title'],
                        'link' => $nav['link'] ?? ''
                    ];
                }
            }
        }

        // 首页分类
        $home_cats = ZhuiGe_Xcx::option_value('cms_home_cats');
        $hide_cats = ZhuiGe_Xcx::option_value('cms_cat_hide');
        $hide_cat_ids = $hide_cats && is_array($hide_cats) ? $hide_cats : [];

        $data['nav_cats'] = [];
        if ($home_cats && is_array($home_cats)) {
            $data['nav_cats'][] = ['id' => 0, 'title' => '最新'];
            foreach ($home_cats as $cat_id) {
                // 跳过隐藏的分类
                if (in_array($cat_id, $hide_cat_ids)) {
                    continue;
                }

                $category = get_category($cat_id);
                if ($category && !is_wp_error($category)) {
                    $data['nav_cats'][] = [
                        'id' => $category->term_id,
                        'title' => $category->name
                    ];
                }
            }
        } else {
            $data['nav_cats'][] = ['id' => 0, 'title' => '最新'];
        }

        // 热门标签
        $home_tags = ZhuiGe_Xcx::option_value('cms_home_tags');
        $data['tags'] = [];
        if ($home_tags && is_array($home_tags)) {
            foreach ($home_tags as $tag_id) {
                $tag = get_tag($tag_id);
                if ($tag && !is_wp_error($tag)) {
                    $data['tags'][] = [
                        'id' => $tag->term_id,
                        'name' => $tag->name
                    ];
                }
            }
        }

        // 标签容器宽度
        $tags_width = ZhuiGe_Xcx::option_value('cms_home_tags_width');
        $data['tags_width'] = $tags_width ? intval($tags_width) : 750;

        // 三图广告
        $events_title = ZhuiGe_Xcx::option_value('cms_events_title');
        $events_description = ZhuiGe_Xcx::option_value('cms_events_description');
        $events = ZhuiGe_Xcx::option_value('cms_events');
        if ($events && is_array($events) && count($events) > 0) {
            $events_data = [];
            foreach ($events as $event) {
                if (!empty($event['image']) && !empty($event['title'])) {
                    $events_data[] = [
                        'image' => ZhuiGe_Xcx::option_image_url($event['image']),
                        'title' => $event['title'],
                        'badge' => $event['badge'] ?? '',
                        'link' => $event['link'] ?? ''
                    ];
                }
            }

            if (count($events_data) > 0) {
                $data['events'] = [
                    'title' => $events_title ?: '热门推荐',
                    'description' => $events_description ?: '精选内容推荐',
                    'items' => $events_data
                ];
            }
        }

        // 微信广告 - 列表页广告
        $wx_ad_list = ZhuiGe_Xcx::option_value('cms_wx_ad_list');
        if ($wx_ad_list && $wx_ad_list['switch'] && !empty($wx_ad_list['banner'])) {
            $data['wx_ad'] = [
                'banner' => $wx_ad_list['banner'],
                'frequency' => $wx_ad_list['frequency'] ?? 5,
                'title' => $wx_ad_list['title'] ?? '广告',
                'desc' => $wx_ad_list['desc'] ?? '点击查看详情'
            ];
        }

        // 分享缩略图
        if (!empty($data['slides']) && count($data['slides']) > 0) {
            $data['thumb'] = $data['slides'][0]['image'];
        }

        return $this->success($data);
    }

    /**
     * 获取详情页设置
     */
    public function get_setting_detail($request)
    {
        $data = [];

        // 版权信息
        $copyright = ZhuiGe_Xcx::option_value('cms_copyright');
        if ($copyright) {
            $data['copyright'] = $copyright;
        }

        // 上方广告
        $ad_up = ZhuiGe_Xcx::option_value('cms_ad_up');
        if ($ad_up && !empty($ad_up['image'])) {
            $data['ad_up'] = [
                'image' => ZhuiGe_Xcx::option_image_url($ad_up['image']),
                'link' => $ad_up['link'] ?? ''
            ];
        }

        // 下方广告
        $ad_down = ZhuiGe_Xcx::option_value('cms_ad_down');
        if ($ad_down && !empty($ad_down['image'])) {
            $data['ad_down'] = [
                'image' => ZhuiGe_Xcx::option_image_url($ad_down['image']),
                'link' => $ad_down['link'] ?? ''
            ];
        }

        // 微信广告 - 详情页广告
        $wx_ad_detail = ZhuiGe_Xcx::option_value('cms_wx_ad_detail');
        if ($wx_ad_detail && is_array($wx_ad_detail)) {
            $wx_ad = [];

            // 横幅广告
            if ($wx_ad_detail['banner_switch'] && !empty($wx_ad_detail['banner'])) {
                $wx_ad['banner'] = $wx_ad_detail['banner'];
            }

            // 插屏广告
            if ($wx_ad_detail['chaping_switch'] && !empty($wx_ad_detail['chaping'])) {
                $wx_ad['chaping'] = $wx_ad_detail['chaping'];
            }

            // 激励视频广告
            if ($wx_ad_detail['jili_switch'] && !empty($wx_ad_detail['jili'])) {
                $wx_ad['jili'] = $wx_ad_detail['jili'];
            }

            if (!empty($wx_ad)) {
                $data['wx_ad'] = $wx_ad;
            }
        }

        // 分享海报
        $poster = ZhuiGe_Xcx::option_value('cms_poster');
        if ($poster && !empty($poster['bg'])) {
            $data['poster'] = [
                'bg' => ZhuiGe_Xcx::option_image_url($poster['bg']),
                'thumb' => !empty($poster['thumb']) ? ZhuiGe_Xcx::option_image_url($poster['thumb']) : ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg',
                'title' => !empty($poster['title']) ? $poster['title'] : '分享精彩内容'
            ];
        }

        // 关注公众号开关
        $data['official_switch'] = ZhuiGe_Xcx::option_value('official_switch');

        return $this->success($data);
    }

    /**
     * 获取列表页设置
     */
    public function get_setting_list($request)
    {
        $data = [];

        // 微信广告 - 列表页广告
        $wx_ad_list = ZhuiGe_Xcx::option_value('cms_wx_ad_list');
        if ($wx_ad_list && $wx_ad_list['switch'] && !empty($wx_ad_list['banner'])) {
            $data['wx_ad'] = [
                'banner' => $wx_ad_list['banner'],
                'frequency' => $wx_ad_list['frequency'] ?? 5,
                'title' => $wx_ad_list['title'] ?? '广告',
                'desc' => $wx_ad_list['desc'] ?? '点击查看详情'
            ];
        }

        return $this->success($data);
    }

    /**
     * 获取最新新闻资讯
     */
    public function get_last_posts($request)
    {
        $offset = $this->param_int($request, 'offset', 0);

        $args = [
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'post_type' => 'post',
            'post_status' => 'publish',
        ];

        // 过滤隐藏的分类
        $cms_cat_hide = ZhuiGe_Xcx::option_value('cms_cat_hide');
        if (!empty($cms_cat_hide)) {
            $args['category__not_in'] = $cms_cat_hide;
        }

        $query = new WP_Query($args);
        $posts = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post = get_post();
                $posts[] = zhuige_cms_post_format($post);
            }
            wp_reset_postdata();
        }

        $data = [
            'posts' => $posts,
            'more' => count($posts) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore'
        ];

        return $this->success($data);
    }



    /**
     * 获取分类新闻资讯
     */
    public function get_category_posts($request)
    {
        $offset = $this->param_int($request, 'offset', 0);
        $cat_id = $this->param_int($request, 'cat_id', 0);

        if (!$cat_id) {
            return $this->error('分类ID不能为空');
        }

        $args = [
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'post_type' => 'post',
            'post_status' => 'publish',
            'cat' => $cat_id,
        ];

        $query = new WP_Query($args);
        $posts = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post = get_post();
                $posts[] = zhuige_cms_post_format($post);
            }
            wp_reset_postdata();
        }

        $data = [
            'posts' => $posts,
            'more' => count($posts) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore'
        ];

        return $this->success($data);
    }

    /**
     * 获取标签新闻资讯
     */
    public function get_tag_posts($request)
    {
        $offset = $this->param_int($request, 'offset', 0);
        $tag_id = $this->param_int($request, 'tag_id', 0);

        if (!$tag_id) {
            return $this->error('标签ID不能为空');
        }

        $args = [
            'posts_per_page' => ZhuiGe_Xcx::POSTS_PER_PAGE,
            'offset' => $offset,
            'orderby' => 'date',
            'post_type' => 'post',
            'post_status' => 'publish',
            'tag_id' => $tag_id,
        ];

        // 过滤隐藏的分类
        $cms_cat_hide = ZhuiGe_Xcx::option_value('cms_cat_hide');
        if (!empty($cms_cat_hide)) {
            $args['category__not_in'] = $cms_cat_hide;
        }

        $query = new WP_Query($args);
        $posts = [];

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $post = get_post();
                $posts[] = zhuige_cms_post_format($post);
            }
            wp_reset_postdata();
        }

        $data = [
            'posts' => $posts,
            'more' => count($posts) >= ZhuiGe_Xcx::POSTS_PER_PAGE ? 'more' : 'noMore'
        ];

        return $this->success($data);
    }

    /**
     * 获取新闻资讯详情
     */
    public function get_post_detail($request)
    {
        $post_id = $this->param_int($request, 'post_id', 0);
        if (!$post_id) {
            return $this->error('缺少参数');
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'post') {
            return $this->error('文章不存在');
        }

        $user_id = get_current_user_id();

        // 检查发布状态
        if ($post->post_status !== 'publish' && $post->post_author != $user_id) {
            return $this->error('文章不存在');
        }

        // 增加浏览量
        zhuige_cms_increase_views($post_id);

        // 格式化数据
        $data = zhuige_cms_post_detail_format($post);

        // 检查阅读权限
        $can_read_full = $this->check_read_permission($post_id, $user_id, $data['read_limit']);
        if (!$can_read_full && $data['read_limit'] !== 'free') {
            $data['content'] = zhuige_cms_post_excerpt($post, 100);
        }

        return $this->success($data);
    }

    /**
     * 获取新闻资讯内容（激励视频后）
     */
    public function get_post_content($request)
    {
        $post_id = $this->param_int($request, 'post_id', 0);
        if (!$post_id) {
            return $this->error('缺少参数');
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'post' || $post->post_status !== 'publish') {
            return $this->error('文章不存在');
        }

        return $this->success([
            'content' => apply_filters('the_content', $post->post_content)
        ]);
    }



    /**
     * 获取分层分类列表（用于分类页面）
     */
    public function get_cats($request)
    {
        $parent = $this->param_int($request, 'parent', 0);

        // 获取隐藏分类列表
        $hide_cats = ZhuiGe_Xcx::option_value('cms_cat_hide');
        $hide_cat_ids = $hide_cats && is_array($hide_cats) ? $hide_cats : [];

        // 获取顶级分类
        $top_categories = get_categories([
            'hide_empty' => false,
            'parent' => $parent,
            'orderby' => 'count',
            'order' => 'DESC',
            'exclude' => $hide_cat_ids // 排除隐藏分类
        ]);

        $data = [];
        foreach ($top_categories as $top_category) {
            $top_category_options = get_term_meta($top_category->term_id, 'zhuige-cms-category-options', true);

            // 检查是否启用
            if ($top_category_options && isset($top_category_options['switch']) && !$top_category_options['switch']) {
                continue;
            }

            // 获取子分类
            $sub_categories = get_categories([
                'hide_empty' => false,
                'parent' => $top_category->term_id,
                'orderby' => 'count',
                'order' => 'DESC',
                'exclude' => $hide_cat_ids // 排除隐藏分类
            ]);

            $sub_cats = [];
            foreach ($sub_categories as $sub_category) {
                $sub_category_options = get_term_meta($sub_category->term_id, 'zhuige-cms-category-options', true);

                // 检查是否启用
                if ($sub_category_options && isset($sub_category_options['switch']) && !$sub_category_options['switch']) {
                    continue;
                }

                $sub_item = [
                    'id' => $sub_category->term_id,
                    'name' => $sub_category->name,
                    'description' => $sub_category->description ?: '暂无描述',
                    'count' => $sub_category->count,
                    'cover' => ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg'
                ];

                if ($sub_category_options && !empty($sub_category_options['cover'])) {
                    $sub_item['cover'] = ZhuiGe_Xcx::option_image_url($sub_category_options['cover']);
                }

                $sub_cats[] = $sub_item;
            }

            // 如果没有子分类，将自己作为子分类
            if (empty($sub_cats)) {
                $top_item = [
                    'id' => $top_category->term_id,
                    'name' => $top_category->name,
                    'description' => $top_category->description ?: '暂无描述',
                    'count' => $top_category->count,
                    'cover' => ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg'
                ];

                if ($top_category_options && !empty($top_category_options['cover'])) {
                    $top_item['cover'] = ZhuiGe_Xcx::option_image_url($top_category_options['cover']);
                }

                $sub_cats[] = $top_item;
            }

            $cat_item = [
                'id' => $top_category->term_id,
                'name' => $top_category->name,
                'cats' => $sub_cats
            ];

            $data[] = $cat_item;
        }

        return $this->success(['cats' => $data]);
    }

    /**
     * 获取标签列表
     */
    public function get_tags($request)
    {
        $tags = get_tags([
            'hide_empty' => false,
            'orderby' => 'count',
            'order' => 'DESC'
        ]);

        $data = [];
        foreach ($tags as $tag) {
            $tag_options = get_term_meta($tag->term_id, 'zhuige-cms-tag-options', true);

            // 检查是否启用
            if ($tag_options && isset($tag_options['switch']) && !$tag_options['switch']) {
                continue;
            }

            $item = [
                'id' => $tag->term_id,
                'name' => $tag->name,
                'count' => $tag->count,
                'logo' => ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg'
            ];

            if ($tag_options && !empty($tag_options['logo'])) {
                $item['logo'] = ZhuiGe_Xcx::option_image_url($tag_options['logo']);
            }

            $data[] = $item;
        }

        return $this->success(['tags' => $data]);
    }

    /**
     * 微信支付
     */
    public function wx_post_pay($request)
    {
        $post_id = $this->param_int($request, 'post_id', 0);
        if (!$post_id) {
            return $this->error('缺少参数');
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录', 'require_login');
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'post' || $post->post_status !== 'publish') {
            return $this->error('文章不存在');
        }

        // 获取价格设置
        $options = get_post_meta($post_id, 'zhuige-cms-post-option', true);
        $cost_price = 0;
        if ($options && isset($options['cost_price'])) {
            $cost_price = floatval($options['cost_price']);
        }

        if ($cost_price <= 0) {
            return $this->error('文章未设置付费价格');
        }

        // 检查是否已购买
        global $wpdb;
        $table_cost_log = $wpdb->prefix . 'zhuige_xcx_post_cost_log';
        $cost_log = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(id) FROM $table_cost_log WHERE user_id = %d AND post_id = %d",
            $user_id, $post_id
        ));

        if ($cost_log > 0) {
            return $this->error('您已经购买过此文章');
        }

        // 调用微信支付接口
        if (function_exists('zhuige_xcx_wx_pay')) {
            return zhuige_xcx_wx_pay($user_id, $post_id, $cost_price, 'cms_post');
        }

        return $this->error('支付功能未开启');
    }

    /**
     * 积分支付
     */
    public function score_post_pay($request)
    {
        $post_id = $this->param_int($request, 'post_id', 0);
        if (!$post_id) {
            return $this->error('缺少参数');
        }

        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录', 'require_login');
        }

        $post = get_post($post_id);
        if (!$post || $post->post_type !== 'post' || $post->post_status !== 'publish') {
            return $this->error('文章不存在');
        }

        // 获取积分设置
        $options = get_post_meta($post_id, 'zhuige-cms-post-option', true);
        $cost_score = 0;
        if ($options && isset($options['cost_score'])) {
            $cost_score = intval($options['cost_score']);
        }

        if ($cost_score <= 0) {
            return $this->error('文章未设置积分价格');
        }

        global $wpdb;

        // 检查是否已购买
        $table_cost_log = $wpdb->prefix . 'zhuige_xcx_post_cost_log';
        $cost_log = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(id) FROM $table_cost_log WHERE user_id = %d AND post_id = %d",
            $user_id, $post_id
        ));

        if ($cost_log > 0) {
            return $this->error('您已经购买过此文章');
        }

        // 检查用户积分
        if (function_exists('zhuige_xcx_get_user_score')) {
            $user_score = zhuige_xcx_get_user_score($user_id);
        } else {
            $user_score = (int) get_user_meta($user_id, 'zhuige_user_score', true);
        }

        if ($user_score < $cost_score) {
            return $this->error('积分不足');
        }

        // 扣除积分并记录
        if (function_exists('zhuige_xcx_add_user_score')) {
            zhuige_xcx_add_user_score($user_id, -$cost_score, 'cms_post_pay', $post_id);
        } else {
            update_user_meta($user_id, 'zhuige_user_score', $user_score - $cost_score);
        }

        // 记录购买日志
        $wpdb->insert(
            $table_cost_log,
            [
                'user_id' => $user_id,
                'post_id' => $post_id,
                'cost_type' => 'score',
                'cost_value' => $cost_score,
                'create_time' => current_time('mysql')
            ]
        );

        return $this->success('兑换成功');
    }

    /**
     * 检查阅读权限
     */
    private function check_read_permission($post_id, $user_id, $read_limit)
    {
        if ($read_limit === 'free') {
            return true;
        }

        if (!$user_id) {
            return false;
        }

        $post = get_post($post_id);
        if ($post && $post->post_author == $user_id) {
            return true; // 作者可以查看自己的文章
        }

        global $wpdb;

        switch ($read_limit) {
            case 'cost':
            case 'score':
                // 检查是否已购买
                $table_cost_log = $wpdb->prefix . 'zhuige_xcx_post_cost_log';
                $cost_log = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(id) FROM $table_cost_log WHERE user_id = %d AND post_id = %d",
                    $user_id, $post_id
                ));
                return $cost_log > 0;

            case 'like':
                // 检查是否已点赞
                $table_like = $wpdb->prefix . 'zhuige_xcx_post_like';
                $like_log = $wpdb->get_var($wpdb->prepare(
                    "SELECT COUNT(id) FROM $table_like WHERE user_id = %d AND post_id = %d",
                    $user_id, $post_id
                ));
                return $like_log > 0;

            case 'certify':
                // 检查是否已认证
                if (function_exists('zhuige_xcx_certify_is_certify')) {
                    $certify = zhuige_xcx_certify_is_certify($user_id);
                    return $certify && $certify['status'] == 1;
                }
                return false;

            case 'jiliv':
                // 激励视频需要前端处理
                return false;

            default:
                return false;
        }
    }
}

// 注册控制器
ZhuiGe_Xcx::$rest_controllers[] = new ZhuiGe_Xcx_Cms_Controller();
