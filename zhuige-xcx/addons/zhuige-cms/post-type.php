<?php

/**
 * 追格小程序 - CMS新闻资讯类型配置
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

function zhuige_xcx_cms_create_custom_post_type()
{
    // 为WordPress默认的post类型添加自定义字段
    $cms_post_option = 'zhuige-cms-post-option';
    CSF::createMetabox($cms_post_option, array(
        'title'        => '追格新闻资讯设置',
        'post_type'    => 'post',
    ));

    CSF::createSection($cms_post_option, array(
        'fields' => array(

            array(
                'id'    => 'badge',
                'type'  => 'text',
                'title' => '角标',
                'subtitle' => '在新闻资讯列表中显示的角标文字',
            ),

            array(
                'id'      => 'read_limit',
                'type'    => 'select',
                'title'   => '阅读限制',
                'options' => array(
                    'free'    => '免费阅读',
                    'cost'    => '付费阅读',
                    'score'   => '积分阅读',
                    'like'    => '点赞阅读',
                    'certify' => '认证阅读',
                    'jiliv'   => '激励视频',
                ),
                'default' => 'free',
            ),

            array(
                'id'         => 'cost_price',
                'type'       => 'number',
                'title'      => '付费价格',
                'subtitle'   => '单位：元',
                'dependency' => array('read_limit', '==', 'cost'),
                'default'    => 0,
            ),

            array(
                'id'         => 'cost_score',
                'type'       => 'number',
                'title'      => '积分价格',
                'subtitle'   => '单位：积分',
                'dependency' => array('read_limit', '==', 'score'),
                'default'    => 0,
            ),

            array(
                'id'         => 'cost_ios_switch',
                'type'       => 'switcher',
                'title'      => 'iOS付费开关',
                'subtitle'   => '是否在iOS设备上显示付费功能',
                'dependency' => array('read_limit', '==', 'cost'),
                'default'    => true,
            ),

            array(
                'id'         => 'read_limit_tip',
                'type'       => 'text',
                'title'      => '限制提示',
                'subtitle'   => '当用户不满足阅读条件时显示的提示文字',
                'dependency' => array('read_limit', '==', 'certify'),
            ),

            array(
                'id'    => 'driect_link_switch',
                'type'  => 'switcher',
                'title' => '直接链接',
                'subtitle' => '开启后点击新闻资讯将直接跳转到指定链接',
                'default' => false,
            ),

            array(
                'id'         => 'driect_link',
                'type'       => 'text',
                'title'      => '链接地址',
                'dependency' => array('driect_link_switch', '==', true),
            ),

            array(
                'id'      => 'button1',
                'type'    => 'fieldset',
                'title'   => '底部按钮1',
                'fields'  => array(
                    array(
                        'id'    => 'title',
                        'type'  => 'text',
                        'title' => '按钮文字',
                    ),
                    array(
                        'id'    => 'link',
                        'type'  => 'text',
                        'title' => '按钮链接',
                    ),
                ),
            ),

            array(
                'id'      => 'button2',
                'type'    => 'fieldset',
                'title'   => '底部按钮2',
                'fields'  => array(
                    array(
                        'id'    => 'title',
                        'type'  => 'text',
                        'title' => '按钮文字',
                    ),
                    array(
                        'id'    => 'link',
                        'type'  => 'text',
                        'title' => '按钮链接',
                    ),
                ),
            ),



        )
    ));

    // 为分类添加自定义字段
    $category_options = 'zhuige-cms-category-options';
    CSF::createTaxonomyOptions($category_options, array(
        'taxonomy'  => 'category',
    ));

    CSF::createSection($category_options, array(
        'fields' => array(
            array(
                'id'      => 'cover',
                'type'    => 'media',
                'title'   => '封面',
                'library' => 'image',
            ),

            array(
                'id'    => 'switch',
                'type'  => 'switcher',
                'title' => '启用',
                'subtitle' => '是否在分类列表中显示',
                'default' => true,
            ),
        )
    ));

    // 为标签添加自定义字段
    $tag_options = 'zhuige-cms-tag-options';
    CSF::createTaxonomyOptions($tag_options, array(
        'taxonomy'  => 'post_tag',
    ));

    CSF::createSection($tag_options, array(
        'fields' => array(
            array(
                'id'      => 'logo',
                'type'    => 'media',
                'title'   => 'Logo',
                'subtitle' => '标签Logo图片',
                'library' => 'image',
            ),

            array(
                'id'    => 'switch',
                'type'  => 'switcher',
                'title' => '启用',
                'subtitle' => '是否在标签列表中显示',
                'default' => true,
            ),
        )
    ));
}

// 注册新闻资讯类型到系统
ZhuiGe_Xcx::$post_types[] = ['id' => 'post', 'name' => '新闻资讯', 'link' => '/pages/cms/detail/detail'];
add_action('init', 'zhuige_xcx_cms_create_custom_post_type');
