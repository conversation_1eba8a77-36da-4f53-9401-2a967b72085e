<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 闲置物品
//
CSF::createSection($prefix, array(
    'id'    => 'idle',
    'title' => '闲置物品',
    'icon'  => 'fas fa-shopping-bag',
));

//
// 闲置物品设置
//
CSF::createSection($prefix, array(
    'parent' => 'idle',
    'title' => '设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(

        array(
            'id'      => 'idle_background',
            'type'    => 'media',
            'title'   => '背景图片',
            'subtitle' => '首页背景图片',
            'library' => 'image',
        ),

        array(
            'id'     => 'idle_slides',
            'type'   => 'group',
            'title'  => '轮播图',
            'subtitle' => '首页轮播图配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图片',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'     => 'idle_icons',
            'type'   => 'group',
            'title'  => '图标导航',
            'subtitle' => '首页图标导航配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'     => 'idle_list_ad',
            'type'   => 'group',
            'title'  => '列表广告',
            'subtitle' => '商品列表中的广告配置',
            'fields' => array(
                array(
                    'id'    => 'first',
                    'type'  => 'number',
                    'title' => '首次出现位置',
                    'default' => 3
                ),

                array(
                    'id'    => 'frequency',
                    'type'  => 'number',
                    'title' => '出现频率',
                    'default' => 10
                ),

                array(
                    'id'     => 'items',
                    'type'   => 'group',
                    'title'  => '广告项目',
                    'fields' => array(
                        array(
                            'id'    => 'title',
                            'type'  => 'text',
                            'title' => '标题',
                        ),

                        array(
                            'id'      => 'image',
                            'type'    => 'media',
                            'title'   => '图片',
                            'library' => 'image',
                        ),

                        array(
                            'id'    => 'link',
                            'type'  => 'text',
                            'title' => '链接',
                        ),
                    ),
                ),
            ),
        ),

        array(
            'id'     => 'idle_bottom_menu',
            'type'   => 'group',
            'title'  => '底部菜单',
            'subtitle' => '首页底部菜单配置',
            'fields' => array(
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '图标',
                    'library' => 'image',
                ),

                array(
                    'id'    => 'link',
                    'type'  => 'text',
                    'title' => '链接',
                ),

                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '启用',
                    'default' => '1'
                ),
            ),
        ),

        array(
            'id'      => 'idle_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '首页分享时使用的默认图片',
            'library' => 'image',
        ),

        array(
            'id'     => 'idle_detail_poster',
            'type'   => 'fieldset',
            'title'  => '分享海报设置',
            'subtitle' => '详情页分享海报设置',
            'fields' => array(
                array(
                    'id'      => 'background',
                    'type'    => 'media',
                    'title'   => '背景图',
                    'library' => 'image',
                    'after' => '建议尺寸：750x1334px',
                ),

                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '显示名称',
                    'default' => '给你推荐我的闲置好物，扫码看看吧',
                ),

                array(
                    'id'      => 'thumb_image',
                    'type'    => 'media',
                    'title'   => '默认商品图',
                    'subtitle' => '当商品没有图片时使用的默认图片',
                    'library' => 'image',
                ),
            ),
        ),

        array(
            'id'    => 'idle_create_require_mobile',
            'type'  => 'switcher',
            'title' => '发布要求手机号',
            'subtitle' => '发布商品是否要求绑定手机号',
            'default' => '0'
        ),

        array(
            'id'    => 'idle_create_require_avatar',
            'type'  => 'switcher',
            'title' => '发布要求头像昵称',
            'subtitle' => '发布商品是否要求设置头像昵称',
            'default' => '0'
        ),

        array(
            'id'    => 'idle_create_require_weixin',
            'type'  => 'switcher',
            'title' => '发布要求微信二维码',
            'subtitle' => '发布商品是否要求设置微信二维码',
            'default' => '0'
        ),

        array(
            'id'          => 'idle_create_licence',
            'type'        => 'select',
            'title'       => '发布协议',
            'subtitle'    => '用户发布商品时需要同意的协议页面',
            'chosen'      => true,
            'ajax'        => true,
            'options'     => 'pages',
            'placeholder' => '选择一个页面',
        ),
    )
));



// 自定义列表页面
add_filter('manage_zhuige_idle_goods_posts_columns', 'zhuige_idle_goods_columns');
function zhuige_idle_goods_columns($columns)
{
    $new_columns = [];
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = '商品名称';
    $new_columns['idle_thumb'] = '缩略图';
    $new_columns['idle_price'] = '价格';
    $new_columns['idle_category'] = '分类';
    $new_columns['idle_stick'] = '推广';
    $new_columns['author'] = '发布者';
    $new_columns['date'] = '发布时间';

    return $new_columns;
}

add_action('manage_zhuige_idle_goods_posts_custom_column', 'zhuige_idle_goods_custom_column', 10, 2);
function zhuige_idle_goods_custom_column($column, $post_id)
{
    $options = get_post_meta($post_id, 'zhuige_idle_goods_options', true);
    if (!$options) {
        $options = [];
    }

    switch ($column) {
        case 'idle_thumb':
            $thumb_url = zhuige_idle_get_thumb($post_id);

            if ($thumb_url) {
                echo '<img src="' . esc_url($thumb_url) . '" style="width: 50px; height: 50px; object-fit: cover;" />';
            } else {
                echo '-';
            }
            break;

        case 'idle_price':
            $price = isset($options['price']) ? $options['price'] : '';
            echo $price ? '￥' . esc_html($price) : '-';
            break;

        case 'idle_category':
            $terms = get_the_terms($post_id, 'zhuige_idle_goods_cat');
            if (!is_wp_error($terms) && !empty($terms)) {
                $cat_names = array();
                foreach ($terms as $term) {
                    $cat_names[] = $term->name;
                }
                echo esc_html(implode(', ', $cat_names));
            } else {
                echo '-';
            }
            break;

        case 'idle_stick':
            $stick = isset($options['stick']) ? $options['stick'] : false;
            echo $stick ? '是' : '否';
            break;
    }
}