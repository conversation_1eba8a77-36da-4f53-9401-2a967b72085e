<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 闲置物品摘要
 */
if (!function_exists('zhuige_idle_get_excerpt')) {
	function zhuige_idle_get_excerpt($post, $maxlen = 50)
	{
		$content = str_replace("<br />", "\n", $post->post_content);
		$lines = explode("\n", $content);
		$excerpt = '';
		$len = 0;
		$cnt = 0;
		$suffix = false;
		foreach ($lines as $line) {
			$line = wp_strip_all_tags(apply_filters('the_content', $line));
			$line_len = mb_strlen($line);
			if (($len + $line_len) > $maxlen) {
				$line = wp_trim_words($line, $maxlen - $len, '...');
				$suffix = true;
			}

			if ($excerpt != '') {
				$excerpt = $excerpt . "\n";
			}
			$excerpt = $excerpt . $line;

			$len = $len + $line_len;
			if ($len > $maxlen) {
				return $excerpt;
			}

			$cnt = $cnt + 1;
			if ($cnt >= 3) {
				if (!$suffix) {
					$excerpt = $excerpt . "\n...";
				}
				return $excerpt;
			}
		}

		return $excerpt;
	}
}

/**
* 闲置物品格式化
*/
if (!function_exists('zhuige_idle_shop_goods_format')) {
    function zhuige_idle_shop_goods_format($post, $detail = false)
    {
        if (!$post) {
            return null;
        }
        
        $user_id = $post->post_author;
        $my_user_id = get_current_user_id();

        $user = [
            'user_id' => $user_id,
            'nickname' => get_user_meta($user_id, 'nickname', true),
            'avatar' => ZhuiGe_Xcx::user_avatar($user_id),
            'weixin' => get_user_meta($user_id, 'weixin', true)
        ];

        // 检查是否关注了作者
        if ($my_user_id) {
            global $wpdb;
            $table_follow_user = $wpdb->prefix . 'zhuige_xcx_follow_user';
            $follow_user_id_exist = $wpdb->get_var(
                $wpdb->prepare(
                    "SELECT id FROM `$table_follow_user` WHERE user_id=%d AND follow_user_id=%d",
                    $my_user_id,
                    $user_id
                )
            );
            $user['is_follow'] = ($follow_user_id_exist ? 1 : 0);
        } else {
            $user['is_follow'] = 0;
        }
        
        // 获取自定义字段
        $metabox_data = get_post_meta($post->ID, 'zhuige_idle_goods_options', true);
        if (!$metabox_data) {
            $metabox_data = [];
        }
        $price = isset($metabox_data['price']) ? $metabox_data['price'] : '0';
        $stick = isset($metabox_data['stick']) ? $metabox_data['stick'] : false;

        // 解析图片数据
        $images = [];
        if (!empty($metabox_data['images'])) {
            if (is_array($metabox_data['images'])) {
                foreach ($metabox_data['images'] as $img) {
                    if (isset($img['image']['url'])) {
                        $images[] = $img['image']['url'];
                    }
                }
            }
        }

        // 获取缩略图
        $thumb = zhuige_idle_get_thumb($post->ID);
        
        // 获取分类信息
        $cat = ['id' => 0, 'name' => ''];
        $post_cats = get_the_terms($post->ID, 'zhuige_idle_goods_cat');
        if ($post_cats && !is_wp_error($post_cats)) {
            $cat_data = $post_cats[0]; // 取第一个分类
            $cat = [
                'id' => $cat_data->term_id,
                'name' => $cat_data->name
            ];
        }

        // 获取标签
        $tags = [];
        $post_tags = get_the_terms($post->ID, 'zhuige_idle_goods_tag');
        if ($post_tags && !is_wp_error($post_tags)) {
            foreach ($post_tags as $tag) {
                $tags[] = [
                    'id' => $tag->term_id,
                    'name' => $tag->name
                ];
            }
        }
        
        $data = [
            'id' => $post->ID,
            'title' => $post->post_title,
            'content' => $detail ? apply_filters('the_content', $post->post_content) : zhuige_idle_get_excerpt($post),
            'excerpt' => zhuige_idle_get_excerpt($post),
            'price' => $price,
            'thumb' => $thumb,
            'images' => $images,
            'stick' => $stick ? 1 : 0,
            'user' => $user,
            'author' => $user,
            'cat' => $cat,
            'rec_cat' => $cat,
            'tags' => $tags,
            'time' => zhuige_xcx_time_beautify($post->post_date_gmt),
            'comment_count' => $post->comment_count
        ];
        
        if ($detail) {
            // 添加认证和VIP信息到用户数据
            if (function_exists('zhuige_xcx_certify_is_certify')) {
                $user['certify'] = zhuige_xcx_certify_is_certify($user_id);
            }
            if (function_exists('zhuige_xcx_vip_is_vip')) {
                $user['vip'] = zhuige_xcx_vip_is_vip($user_id);
            }
            $data['author'] = $user;

            // 点赞相关
            if ($my_user_id) {
                global $wpdb;
                $table_like = $wpdb->prefix . 'zhuige_xcx_post_like';
                $is_like = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT id FROM `$table_like` WHERE user_id=%d AND post_id=%d",
                        $my_user_id,
                        $post->ID
                    )
                );
                $data['is_like'] = $is_like ? 1 : 0;
            } else {
                $data['is_like'] = 0;
            }

            // 点赞列表
            global $wpdb;
            $table_post_like = $wpdb->prefix . 'zhuige_xcx_post_like';
            $users = $wpdb->get_results(
                $wpdb->prepare(
                    "SELECT user_id FROM `$table_post_like` WHERE post_id=%d ORDER BY id DESC",
                    $post->ID
                )
            );
            $like_list = [];
            if (!empty($users)) {
                foreach ($users as $user_like) {
                    $like_list[] = [
                        'user_id' => $user_like->user_id,
                        'avatar' => ZhuiGe_Xcx::user_avatar($user_like->user_id),
                    ];
                }
            }
            $data['like_list'] = $like_list;

            // 收藏状态
            if ($my_user_id) {
                $table_post_favorite = $wpdb->prefix . 'zhuige_xcx_post_favorite';
                $post_favorite_id = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT id FROM `$table_post_favorite` WHERE user_id=%d AND post_id=%d",
                        $my_user_id,
                        $post->ID
                    )
                );
                $data['is_favorite'] = ($post_favorite_id ? 1 : 0);
            } else {
                $data['is_favorite'] = 0;
            }

            // 收藏数量
            $data['favorites'] = (int) get_post_meta($post->ID, 'zhuige_favorites', true);

            // 评论设置
            $data['comment_switch'] = ZhuiGe_Xcx::option_value('comment_switch') ? 1 : 0;
            $data['comment_require_mobile'] = ZhuiGe_Xcx::option_value('comment_mobile_switch') ? 1 : 0;
            if ($data['comment_switch']) {
                // 评论是否要求头像昵称
                if ($my_user_id && ZhuiGe_Xcx::option_value('comment_avatar_switch')) {
                    if (!zhuige_xcx_is_set_avatar($my_user_id)) {
                        $data['comment_require_avatar2'] = 1;
                    }
                }

                // 评论是否要求手机号
                if ($my_user_id && ZhuiGe_Xcx::option_value('comment_mobile_switch')) {
                    if (!zhuige_xcx_is_set_mobile($my_user_id)) {
                        $data['comment_require_mobile2'] = 1;
                    }
                }
            }

            // 是否已评论
            if ($my_user_id) {
                $table_comments = $wpdb->prefix . 'comments';
                $post_comment_id = $wpdb->get_var(
                    $wpdb->prepare(
                        "SELECT COUNT(`comment_ID`) FROM `$table_comments` WHERE `user_id`=%d AND `comment_post_ID`=%d AND `comment_approved` IN ('0','1')",
                        $my_user_id,
                        $post->ID
                    )
                );
                $data['is_comment'] = ($post_comment_id ? 1 : 0);
            } else {
                $data['is_comment'] = 0;
            }

            // 评论列表
            $data['comments'] = zhuige_xcx_get_comment_tree($post->ID, 0, 5);

            // 推广和编辑按钮
            $data['is_show_promotion'] = 0;
            $data['is_show_edit'] = 0;

            // 获取推荐商品
            $cat_id = $cat ? $cat['id'] : 0;
            $data['recs'] = zhuige_idle_get_recommendations($post->ID, $cat_id, 4);

            // 如果是当前用户的商品，显示编辑按钮
            if (is_user_logged_in() && get_current_user_id() == $user_id) {
                $data['is_show_edit'] = 1;
            }

            // 海报配置
            $poster_config = ZhuiGe_Xcx::option_value('idle_detail_poster', []);
            $data['poster'] = [
                'background' => ZhuiGe_Xcx::option_image_url($poster_config['background'] ?? '', 'placeholder.jpg'),
                'thumb' => $thumb ?: ZhuiGe_Xcx::option_image_url($poster_config['thumb_image'] ?? '', 'placeholder.jpg'),
                'title' => $poster_config['title'] ?? '给你推荐我的闲置好物，扫码看看吧'
            ];
        }
        
        return $data;
    }
}

if (!function_exists('zhuige_idle_get_thumb')) {
    /**
     * 获取商品缩略图
     */
    function zhuige_idle_get_thumb($goods_id)
    {
        $thumb = '';

        $options = get_post_meta($goods_id, 'zhuige_idle_goods_options', true);
        if (!empty($options['images']) && is_array($options['images'])) {
            if (!empty($options['images'][0]['image']['url'])) {
                $thumb = $options['images'][0]['image']['url'];
            }
        }

        // 备用：使用特色图片
        if (empty($thumb) && has_post_thumbnail($goods_id)) {
            $thumb = get_the_post_thumbnail_url($goods_id, 'medium');
        }

        // 默认图片
        if (empty($thumb)) {
			$thumb = ZHUIGE_XCX_BASE_URL . 'public/images/placeholder.jpg';
        }

        return $thumb;
    }
}

if (!function_exists('zhuige_idle_get_recommendations')) {
    /**
     * 获取推荐商品
     */
    function zhuige_idle_get_recommendations($exclude_id, $cat_id, $limit = 4)
    {
        $args = [
            'post_type' => 'zhuige_idle_goods',
            'post_status' => 'publish',
            'posts_per_page' => $limit,
            'post__not_in' => [$exclude_id],
            'orderby' => 'rand'
        ];
        
        if ($cat_id) {
            $args['tax_query'] = [
                [
                    'taxonomy' => 'zhuige_idle_goods_cat',
                    'field' => 'term_id',
                    'terms' => $cat_id,
                ]
            ];
        }
        
        $posts = get_posts($args);
        $recommendations = [];
        
        foreach ($posts as $post) {
            $recommendations[] = zhuige_idle_shop_goods_format($post);
        }
        
        return $recommendations;
    }
}

if (!function_exists('zhuige_idle_get_categories')) {
    /**
     * 获取分类列表
     */
    function zhuige_idle_get_categories($with_count = false)
    {
        $args = [
            'taxonomy' => 'zhuige_idle_goods_cat',
            'hide_empty' => false,
            'orderby' => 'term_order',
            'order' => 'ASC'
        ];

        $terms = get_terms($args);
        $result = [];

        if (!is_wp_error($terms) && !empty($terms)) {
            foreach ($terms as $term) {
                $term_meta = get_term_meta($term->term_id, 'zhuige_idle_goods_cat_options', true);
                $image = '';
                if ($term_meta && isset($term_meta['image'])) {
                    $image = ZhuiGe_Xcx::option_image_url($term_meta['image']);
                }

                $cat_data = [
                    'id' => intval($term->term_id),
                    'name' => $term->name,
                    'title' => $term->name,
                    'description' => $term->description,
                    'image' => $image
                ];

                if ($with_count) {
                    $cat_data['count'] = intval($term->count);

                    // 获取该分类下的部分商品
                    $posts = get_posts([
                        'post_type' => 'zhuige_idle_goods',
                        'post_status' => 'publish',
                        'posts_per_page' => 6,
                        'tax_query' => [
                            [
                                'taxonomy' => 'zhuige_idle_goods_cat',
                                'field' => 'term_id',
                                'terms' => $term->term_id
                            ]
                        ]
                    ]);

                    $cat_data['list'] = [];
                    foreach ($posts as $post) {
                        $cat_data['list'][] = zhuige_idle_shop_goods_format($post);
                    }
                }

                $result[] = $cat_data;
            }
        }

        return $result;
    }
}



