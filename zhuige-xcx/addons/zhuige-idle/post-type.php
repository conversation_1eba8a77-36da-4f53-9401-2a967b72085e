<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

function zhuige_xcx_idle_create_custom_post_type()
{
    /**
     * 闲置物品
     */
    $zhuige_idle_goods_labels = array(
        'name'               => '追格闲置物品',
        'singular_name'      => '追格闲置物品',
        'add_new'            => '新建闲置物品',
        'add_new_item'       => '新建一个闲置物品',
        'edit_item'          => '编辑闲置物品',
        'new_item'           => '新闲置物品',
        'all_items'          => '所有闲置物品',
        'view_item'          => '查看闲置物品',
        'search_items'       => '搜索闲置物品',
        'not_found'          => '没有找到有关闲置物品',
        'not_found_in_trash' => '回收站里面没有相关闲置物品',
        'parent_item_colon'  => '',
        'menu_name'          => '追格闲置物品'
    );
    $zhuige_idle_goods_args = array(
        'labels'        => $zhuige_idle_goods_labels,
        'description'   => '我们网站的闲置物品信息',
        'public'        => true,
        'supports'      => array('title', 'editor', 'author', 'thumbnail'),
        'has_archive'   => true
    );
    register_post_type('zhuige_idle_goods', $zhuige_idle_goods_args);

    // 闲置物品设置
    $zhuige_idle_goods_option = 'zhuige_idle_goods_options';
    CSF::createMetabox($zhuige_idle_goods_option, array(
        'title'        => '追格闲置物品设置',
        'post_type'    => 'zhuige_idle_goods',
    ));

    CSF::createSection($zhuige_idle_goods_option, array(
        'fields' => array(
            array(
                'id'    => 'price',
                'type'  => 'number',
                'title' => '商品价格',
                'default' => 0,
                'unit' => '元',
            ),

            array(
                'id'     => 'images',
                'type'   => 'group',
                'title'  => '商品图片',
                'fields' => array(
                    array(
                        'id'      => 'image',
                        'type'    => 'media',
                        'title'   => '图片',
                        'library' => 'image',
                    ),
                ),
            ),

            array(
                'id'    => 'stick',
                'type'  => 'switcher',
                'title' => '推广商品',
                'default' => false,
            ),
        )
    ));

    /**
     * 闲置物品分类
     */
    $zhuige_idle_goods_cat_labels = array(
        'name'              => '分类',
        'singular_name'     => '分类',
        'search_items'      => '搜索分类',
        'all_items'         => '所有分类',
        'parent_item'       => '该分类的上级分类',
        'parent_item_colon' => '该分类的上级分类：',
        'edit_item'         => '编辑分类',
        'update_item'       => '更新分类',
        'add_new_item'      => '添加新的分类',
        'new_item_name'     => '分类',
        'menu_name'         => '分类',
    );
    $zhuige_idle_goods_cat_args = array(
        'hierarchical' => true,
        'labels' => $zhuige_idle_goods_cat_labels,
    );
    register_taxonomy('zhuige_idle_goods_cat', 'zhuige_idle_goods', $zhuige_idle_goods_cat_args);

    /**
     * 闲置物品标签
     */
    $zhuige_idle_goods_tag_labels = array(
        'name'              => '标签',
        'singular_name'     => '标签',
        'search_items'      => '搜索标签',
        'all_items'         => '所有标签',
        'parent_item'       => '该标签的上级标签',
        'parent_item_colon' => '该标签的上级标签：',
        'edit_item'         => '编辑标签',
        'update_item'       => '更新标签',
        'add_new_item'      => '添加新的标签',
        'new_item_name'     => '标签',
        'menu_name'         => '标签',
        'separate_items_with_commas' => '多个标签请用英文逗号（,）分开',
        'choose_from_most_used' => '从常用标签中选择',
        'not_found' => '未找到标签'
    );
    $zhuige_idle_goods_tag_args = array(
        'hierarchical' => false,
        'labels' => $zhuige_idle_goods_tag_labels,
    );
    register_taxonomy('zhuige_idle_goods_tag', 'zhuige_idle_goods', $zhuige_idle_goods_tag_args);

    // 闲置物品分类选项
    $idle_goods_cat_options = 'zhuige_idle_goods_cat_options';
    CSF::createTaxonomyOptions($idle_goods_cat_options, array(
        'taxonomy' => 'zhuige_idle_goods_cat',
    ));
    CSF::createSection($idle_goods_cat_options, array(
        'fields' => array(
            array(
                'id'      => 'image',
                'type'    => 'media',
                'title'   => '分类图片',
                'library' => 'image',
            ),
        )
    ));
}

ZhuiGe_Xcx::$post_types[] = ['id' => 'zhuige_idle_goods', 'name' => '闲置物品', 'link' => '/pages/idle-shop/detail/detail'];
add_action('init', 'zhuige_xcx_idle_create_custom_post_type');
