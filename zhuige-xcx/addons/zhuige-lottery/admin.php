<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 抽奖
//
CSF::createSection(ZHUIGE_XCX, array(
    'id'    => 'lottery',
    'title' => '抽奖',
    'icon'  => 'fas fa-gift',
));

//
// 基本设置
//
CSF::createSection(ZHUIGE_XCX, array(
    'parent' => 'lottery',
    'title' => '基本设置',
    'icon'  => 'fas fa-cog',
    'fields' => array(

        array(
            'id'      => 'lottery_title',
            'type'    => 'text',
            'title'   => '活动标题',
            'default' => '幸运大转盘',
        ),

        array(
            'id'      => 'lottery_background',
            'type'    => 'media',
            'title'   => '背景图片',
            'subtitle' => '抽奖页面背景图片',
            'library' => 'image',
        ),

        array(
            'id'      => 'lottery_share_img',
            'type'    => 'media',
            'title'   => '分享图片',
            'subtitle' => '分享时显示的图片',
            'library' => 'image',
        ),

        array(
            'id'      => 'lottery_banner_switch',
            'type'    => 'switcher',
            'title'   => '显示横幅',
            'subtitle' => '是否在抽奖页面显示横幅广告',
            'default' => false,
        ),

        array(
            'id'         => 'lottery_banner_image',
            'type'       => 'media',
            'title'      => '横幅图片',
            'library'    => 'image',
            'dependency' => array('lottery_banner_switch', '==', true),
        ),

        array(
            'id'         => 'lottery_banner_link',
            'type'       => 'text',
            'title'      => '横幅链接',
            'subtitle'   => '点击横幅跳转的链接',
            'dependency' => array('lottery_banner_switch', '==', true),
        ),

    )
));

//
// 活动设置
//
CSF::createSection(ZHUIGE_XCX, array(
    'parent' => 'lottery',
    'title' => '活动设置',
    'icon'  => 'fas fa-calendar',
    'fields' => array(

        array(
            'id'      => 'lottery_time_from',
            'type'    => 'date',
            'title'   => '活动开始时间',
            'subtitle' => '留空表示不限制开始时间',
            'settings' => array(
                'dateFormat' => 'yy-mm-dd',
                'timeFormat' => 'HH:mm:ss',
                'showTimepicker' => true,
            ),
        ),

        array(
            'id'      => 'lottery_time_to',
            'type'    => 'date',
            'title'   => '活动结束时间',
            'subtitle' => '留空表示不限制结束时间',
            'settings' => array(
                'dateFormat' => 'yy-mm-dd',
                'timeFormat' => 'HH:mm:ss',
                'showTimepicker' => true,
            ),
        ),

        array(
            'id'      => 'lottery_score_cost',
            'type'    => 'number',
            'title'   => '抽奖消耗积分',
            'subtitle' => '每次抽奖需要消耗的积分，0表示免费',
            'default' => 0,
            'unit'    => '积分',
        ),

        array(
            'id'      => 'lottery_explain',
            'type'    => 'wp_editor',
            'title'   => '活动说明',
            'subtitle' => '活动规则和说明内容',
        ),

        array(
            'id'      => 'lottery_rules_link',
            'type'    => 'text',
            'title'   => '活动规则链接',
            'subtitle' => '点击"活动规则"跳转的链接',
        ),

        array(
            'id'      => 'lottery_contact_link',
            'type'    => 'text',
            'title'   => '联系兑奖链接',
            'subtitle' => '点击"联系兑奖"跳转的链接',
        ),

    )
));

//
// 转盘设置
//
CSF::createSection(ZHUIGE_XCX, array(
    'parent' => 'lottery',
    'title' => '转盘设置',
    'icon'  => 'fas fa-circle-notch',
    'fields' => array(

        array(
            'id'      => 'lottery_wheel_bg',
            'type'    => 'media',
            'title'   => '转盘背景图',
            'subtitle' => '大转盘的背景图片',
            'library' => 'image',
        ),

        array(
            'id'      => 'lottery_wheel_btn',
            'type'    => 'media',
            'title'   => '抽奖按钮图',
            'subtitle' => '转盘中间的抽奖按钮图片',
            'library' => 'image',
        ),

    )
));

//
// 奖品管理
//
CSF::createSection(ZHUIGE_XCX, array(
    'parent' => 'lottery',
    'title' => '奖品管理',
    'icon'  => 'fas fa-trophy',
    'fields' => array(

        array(
            'id'     => 'lottery_prizes',
            'type'   => 'group',
            'title'  => '奖品配置',
            'subtitle' => '配置抽奖转盘的奖品信息',
            'fields' => array(
                array(
                    'id'    => 'name',
                    'type'  => 'text',
                    'title' => '奖品名称',
                    'placeholder' => '如：一等奖、iPhone 15',
                ),

                array(
                    'id'      => 'image',
                    'type'    => 'media',
                    'title'   => '奖品图片',
                    'library' => 'image',
                ),

                array(
                    'id'      => 'bg_color',
                    'type'    => 'color',
                    'title'   => '背景颜色',
                    'default' => '#FFE4B5',
                ),

                array(
                    'id'      => 'type',
                    'type'    => 'select',
                    'title'   => '奖品类型',
                    'options' => array(
                        'virtual'  => '虚拟奖品',
                        'physical' => '实物奖品',
                        'coupon'   => '优惠券',
                    ),
                    'default' => 'virtual',
                ),

                array(
                    'id'    => 'value',
                    'type'  => 'textarea',
                    'title' => '奖品描述',
                    'placeholder' => '奖品的详细描述或兑换说明',
                ),

                array(
                    'id'      => 'probability',
                    'type'    => 'number',
                    'title'   => '中奖概率(%)',
                    'default' => 10.00,
                    'step'    => 0.01,
                    'min'     => 0,
                    'max'     => 100,
                ),

                array(
                    'id'      => 'total_count',
                    'type'    => 'number',
                    'title'   => '总数量',
                    'subtitle' => '0表示无限数量',
                    'default' => 0,
                    'min'     => 0,
                ),

                array(
                    'id'      => 'sort_order',
                    'type'    => 'number',
                    'title'   => '排序',
                    'subtitle' => '数字越小越靠前',
                    'default' => 0,
                ),

                array(
                    'id'      => 'status',
                    'type'    => 'switcher',
                    'title'   => '启用状态',
                    'default' => true,
                ),
            ),
        ),

        array(
            'type'    => 'content',
            'content' => '
                <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007cba;">
                    <h4>💡 使用说明：</h4>
                    <ul>
                        <li><strong>概率设置：</strong>所有奖品的概率总和建议为100%</li>
                        <li><strong>排序规则：</strong>按照排序数字从小到大在转盘上顺时针排列</li>
                        <li><strong>库存管理：</strong>设置总数量后系统会自动扣减库存</li>
                        <li><strong>奖品类型：</strong>虚拟奖品通常为积分、优惠券等；实物奖品需要线下发放</li>
                    </ul>

                    <h4>🎯 推荐配置：</h4>
                    <p>建议设置8个奖品位置，包含1-2个高价值奖品（低概率）、3-4个中等奖品（中等概率）、2-3个安慰奖品（高概率）</p>
                </div>
            ',
        ),

    )
));