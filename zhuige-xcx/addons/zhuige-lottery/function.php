<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

/**
 * 添加抽奖记录
 */
if (!function_exists('zhuige_lottery_add_log')) {
    function zhuige_lottery_add_log($user_id, $prize_index, $prize_name, $prize_type = 'virtual', $prize_value = '', $score_cost = 0)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'zhuige_xcx_lottery_log';

        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => intval($user_id),
                'prize_index' => intval($prize_index),
                'prize_name' => sanitize_text_field($prize_name),
                'prize_type' => sanitize_text_field($prize_type),
                'prize_value' => sanitize_textarea_field($prize_value),
                'score_cost' => intval($score_cost),
                'time' => current_time('timestamp')
            ),
            array('%d', '%d', '%s', '%s', '%s', '%d', '%d')
        );

        return $result !== false;
    }
}

/**
 * 获取用户抽奖记录
 */
if (!function_exists('zhuige_lottery_get_user_logs')) {
    function zhuige_lottery_get_user_logs($user_id, $limit = 10)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'zhuige_xcx_lottery_log';

        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table_name WHERE user_id = %d AND status = 1 ORDER BY time DESC LIMIT %d",
            intval($user_id),
            intval($limit)
        ), ARRAY_A);

        return $results ? $results : array();
    }
}

/**
 * 获取最新中奖记录
 */
if (!function_exists('zhuige_lottery_get_latest_logs')) {
    function zhuige_lottery_get_latest_logs($limit = 20)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'zhuige_xcx_lottery_log';
        $users_table = $wpdb->users;
        $usermeta_table = $wpdb->usermeta;

        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT l.*, u.display_name as name, l.user_id
             FROM $table_name l
             LEFT JOIN $users_table u ON l.user_id = u.ID
             WHERE l.status = 1 AND l.prize_name != %s
             ORDER BY l.time DESC
             LIMIT %d",
            '谢谢参与',
            intval($limit)
        ), ARRAY_A);

        // 处理头像，使用项目标准的头像处理方式
        if ($results) {
            foreach ($results as &$result) {
                $result['avatar'] = ZhuiGe_Xcx::user_avatar($result['user_id']);
            }
            unset($result); // 解除引用
        }

        return $results ? $results : array();
    }
}

/**
 * 获取抽奖参与总数
 */
if (!function_exists('zhuige_lottery_get_total_count')) {
    function zhuige_lottery_get_total_count()
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'zhuige_xcx_lottery_log';

        $count = $wpdb->get_var(
            "SELECT COUNT(*) FROM $table_name WHERE status = 1"
        );

        return intval($count);
    }
}