<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

class ZhuiGe_Xcx_Lottery_Controller extends ZhuiGe_Xcx_Base_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->module = 'lottery';
        $this->routes = [
            'setting' => 'get_setting',
            'logs' => 'get_logs',
            'draw' => ['callback' => 'post_draw', 'auth' => 'login'],
        ];
    }

    /**
     * 获取抽奖设置
     */
    public function get_setting($request)
    {
        $data = [];

        // 基本设置
        $data['title'] = ZhuiGe_Xcx::option_value('lottery_title', '幸运大转盘');

        // 背景图片处理
        $background_media = ZhuiGe_Xcx::option_value('lottery_background', '');
        $data['background'] = is_array($background_media) && isset($background_media['url']) ? $background_media['url'] : $background_media;

        // 分享图片处理
        $share_img_media = ZhuiGe_Xcx::option_value('lottery_share_img', '');
        $data['share_img'] = is_array($share_img_media) && isset($share_img_media['url']) ? $share_img_media['url'] : $share_img_media;

        $data['banner'] = null;

        // 横幅设置
        $banner_switch = ZhuiGe_Xcx::option_value('lottery_banner_switch', false);
        if ($banner_switch) {
            $banner_image_media = ZhuiGe_Xcx::option_value('lottery_banner_image', '');
            $banner_image = is_array($banner_image_media) && isset($banner_image_media['url']) ? $banner_image_media['url'] : $banner_image_media;
            $banner_link = ZhuiGe_Xcx::option_value('lottery_banner_link', '');

            if (!empty($banner_image)) {
                $data['banner'] = [
                    'image' => $banner_image,
                    'link' => $banner_link
                ];
            }
        }

        // 活动说明
        $data['explain'] = apply_filters('the_content', ZhuiGe_Xcx::option_value('lottery_explain', ''));
        $data['hdgz'] = ZhuiGe_Xcx::option_value('lottery_rules_link', '');
        $data['lxdj'] = ZhuiGe_Xcx::option_value('lottery_contact_link', '');

        // 活动时间
        $time_from = ZhuiGe_Xcx::option_value('lottery_time_from', '');
        $time_to = ZhuiGe_Xcx::option_value('lottery_time_to', '');
        if (!empty($time_from) && !empty($time_to)) {
            $data['lottery_time'] = [
                'from' => $time_from,
                'to' => $time_to
            ];
        }

        // 积分设置
        $data['lottery_score'] = intval(ZhuiGe_Xcx::option_value('lottery_score_cost', 0));

        // 转盘背景和按钮
        $wheel_bg_media = ZhuiGe_Xcx::option_value('lottery_wheel_bg', '');
        $data['lottery_bg'] = is_array($wheel_bg_media) && isset($wheel_bg_media['url']) ? $wheel_bg_media['url'] : $wheel_bg_media;

        $wheel_btn_media = ZhuiGe_Xcx::option_value('lottery_wheel_btn', '');
        $data['lottery_chou'] = is_array($wheel_btn_media) && isset($wheel_btn_media['url']) ? $wheel_btn_media['url'] : $wheel_btn_media;

        // 获取奖品列表 - 从后台配置中读取
        $prizes_config = ZhuiGe_Xcx::option_value('lottery_prizes', []);
        $formatted_prizes = [];

        if (!empty($prizes_config)) {
            foreach ($prizes_config as $index => $prize) {
                if (empty($prize['status'])) {
                    continue; // 跳过未启用的奖品
                }

                // 处理图片URL
                $image_url = '';
                if (!empty($prize['image'])) {
                    $image_url = is_array($prize['image']) && isset($prize['image']['url']) ? $prize['image']['url'] : $prize['image'];
                }

                $formatted_prizes[] = [
                    'id' => $index,
                    'name' => $prize['name'] ?: '奖品',
                    'image' => $image_url,
                    'bg_color' => $prize['bg_color'] ?: '#FFE4B5',
                    'type' => $prize['type'] ?: 'virtual',
                    'value' => $prize['value'] ?: '',
                    'probability' => floatval($prize['probability'] ?: 10),
                    'total_count' => intval($prize['total_count'] ?: 0),
                    'used_count' => intval($this->get_prize_used_count($index)),
                    'sort_order' => intval($prize['sort_order'] ?: $index)
                ];
            }

            // 按排序字段排序
            usort($formatted_prizes, function($a, $b) {
                return $a['sort_order'] - $b['sort_order'];
            });
        }

        $data['prizes'] = $formatted_prizes;

        // 获取参与人数
        $data['count'] = zhuige_lottery_get_total_count();

        // 获取最新中奖记录
        $latest_logs = zhuige_lottery_get_latest_logs(10);
        $formatted_latest_logs = [];
        foreach ($latest_logs as $log) {
            $formatted_latest_logs[] = [
                'name' => $log['name'] ?: '匿名用户',
                'avatar' => $log['avatar'],
                'prize' => $log['prize_name']
            ];
        }
        $data['lucy_logs'] = $formatted_latest_logs;

        // 获取用户抽奖记录
        $user_id = get_current_user_id();
        if ($user_id) {
            $user_logs = zhuige_lottery_get_user_logs($user_id, 10);
            $formatted_logs = [];
            foreach ($user_logs as $log) {
                $formatted_logs[] = [
                    'createtime' => wp_date('Y-m-d H:i:s', $log['time']),
                    'prize' => $log['prize_name']
                ];
            }
            $data['my_logs'] = $formatted_logs;
        } else {
            $data['my_logs'] = [];
        }

        return $this->success($data);
    }

    /**
     * 获取抽奖记录
     */
    public function get_logs($request)
    {
        $data = [];

        // 获取参与人数
        $data['count'] = zhuige_lottery_get_total_count();

        // 获取最新中奖记录
        $latest_logs = zhuige_lottery_get_latest_logs(10);
        $formatted_latest_logs = [];
        foreach ($latest_logs as $log) {
            $formatted_latest_logs[] = [
                'name' => $log['name'] ?: '匿名用户',
                'avatar' => $log['avatar'],
                'prize' => $log['prize_name']
            ];
        }
        $data['lucy_logs'] = $formatted_latest_logs;

        // 获取用户抽奖记录
        $user_id = get_current_user_id();
        if ($user_id) {
            $user_logs = zhuige_lottery_get_user_logs($user_id, 10);
            $formatted_logs = [];
            foreach ($user_logs as $log) {
                $formatted_logs[] = [
                    'createtime' => wp_date('Y-m-d H:i:s', $log['time']),
                    'prize' => $log['prize_name']
                ];
            }
            $data['my_logs'] = $formatted_logs;
        } else {
            $data['my_logs'] = [];
        }

        return $this->success($data);
    }

    /**
     * 执行抽奖
     */
    public function post_draw($request)
    {
        $user_id = get_current_user_id();
        if (!$user_id) {
            return $this->error('请先登录');
        }

        // 检查活动时间
        $time_from = ZhuiGe_Xcx::option_value('lottery_time_from', '');
        $time_to = ZhuiGe_Xcx::option_value('lottery_time_to', '');
        $current_time = time();

        if (!empty($time_from) && zhuige_xcx_strtotime($time_from) > $current_time) {
            return $this->error('活动尚未开始');
        }

        if (!empty($time_to) && zhuige_xcx_strtotime($time_to) < $current_time) {
            return $this->error('活动已结束');
        }

        // 检查积分
        $score_cost = intval(ZhuiGe_Xcx::option_value('lottery_score_cost', 0));
        if ($score_cost > 0) {
            $user_score = intval(get_user_meta($user_id, 'zhuige_score', true));
            if ($user_score < $score_cost) {
                return $this->error('积分不足，当前积分：' . $user_score);
            }
        }

        // 获取奖品列表 - 从后台配置中读取
        $prizes_config = ZhuiGe_Xcx::option_value('lottery_prizes', []);
        if (empty($prizes_config)) {
            return $this->error('暂无奖品配置');
        }

        // 格式化奖品数据
        $prizes = [];
        foreach ($prizes_config as $index => $prize) {
            if (empty($prize['status'])) {
                continue; // 跳过未启用的奖品
            }

            $prizes[$index] = [
                'id' => $index,
                'name' => $prize['name'] ?: '奖品',
                'type' => $prize['type'] ?: 'virtual',
                'value' => $prize['value'] ?: '',
                'probability' => floatval($prize['probability'] ?: 10),
                'total_count' => intval($prize['total_count'] ?: 0),
                'used_count' => intval($this->get_prize_used_count($index)),
                'sort_order' => intval($prize['sort_order'] ?: $index)
            ];
        }

        if (empty($prizes)) {
            return $this->error('暂无可用奖品');
        }

        // 检查奖品库存
        foreach ($prizes as $prize) {
            if ($prize['total_count'] > 0 && $prize['used_count'] >= $prize['total_count']) {
                // 如果有奖品库存不足，从列表中移除
                continue;
            }
        }

        // 执行抽奖
        $result = $this->draw_prize($prizes);
        if (!$result) {
            return $this->error('抽奖失败，请稍后重试');
        }

        $prize_index = $result['index'];
        $prize = $result['prize'];

        // 再次检查该奖品库存
        if ($prize['total_count'] > 0 && $prize['used_count'] >= $prize['total_count']) {
            return $this->error('很遗憾，该奖品库存不足');
        }

        // 扣除积分
        if ($score_cost > 0) {
            $new_score = $user_score - $score_cost;
            update_user_meta($user_id, 'zhuige_score', $new_score);
        }

        // 记录抽奖日志
        $log_result = zhuige_lottery_add_log(
            $user_id,
            $prize_index,
            $prize['name'],
            $prize['type'],
            $prize['value'],
            $score_cost
        );

        if (!$log_result) {
            // 如果记录失败，回滚积分
            if ($score_cost > 0) {
                update_user_meta($user_id, 'zhuige_score', $user_score);
            }
            return $this->error('抽奖记录失败，请稍后重试');
        }

        // 更新奖品使用数量（存储在用户元数据中）
        if ($prize['total_count'] > 0) {
            $this->update_prize_used_count($prize['id']);
        }

        return $this->success([
            'prize' => [
                'index' => $prize_index,
                'name' => $prize['name'],
                'type' => $prize['type'],
                'value' => $prize['value']
            ]
        ]);
    }

    /**
     * 获取奖品使用数量
     */
    private function get_prize_used_count($prize_id)
    {
        global $wpdb;
        $table_name = $wpdb->prefix . 'zhuige_xcx_lottery_log';

        // 检查表是否存在
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;
        if (!$table_exists) {
            return 0;
        }

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table_name WHERE prize_index = %d AND status = 1",
            $prize_id
        ));

        return intval($count);
    }

    /**
     * 更新奖品使用数量（这里实际上不需要更新，因为是通过日志统计的）
     */
    private function update_prize_used_count($prize_id)
    {
        // 由于使用数量是通过抽奖日志统计的，这里不需要额外操作
        // 保留此方法以便将来扩展
        return true;
    }

    /**
     * 抽奖算法
     */
    private function draw_prize($prizes)
    {
        if (empty($prizes)) {
            return false;
        }

        // 过滤掉库存不足的奖品
        $available_prizes = [];
        foreach ($prizes as $index => $prize) {
            if ($prize['total_count'] == 0 || $prize['used_count'] < $prize['total_count']) {
                $available_prizes[$index] = $prize;
            }
        }

        if (empty($available_prizes)) {
            return false;
        }

        // 计算总概率
        $total_probability = 0;
        foreach ($available_prizes as $prize) {
            $total_probability += floatval($prize['probability']);
        }

        if ($total_probability <= 0) {
            return false;
        }

        // 生成随机数
        $random = mt_rand(1, intval($total_probability * 100)) / 100;

        // 确定中奖奖品
        $current_probability = 0;
        foreach ($available_prizes as $index => $prize) {
            $current_probability += floatval($prize['probability']);
            if ($random <= $current_probability) {
                return [
                    'index' => $index,
                    'prize' => $prize
                ];
            }
        }

        // 默认返回最后一个可用奖品
        $last_index = array_key_last($available_prizes);
        return [
            'index' => $last_index,
            'prize' => $available_prizes[$last_index]
        ];
    }
}

// 注册控制器
ZhuiGe_Xcx::$rest_controllers[] = new ZhuiGe_Xcx_Lottery_Controller();
