<?php {
    // 抽奖记录表
    $table_zhuige_xcx_lottery_log = $wpdb->prefix . 'zhuige_xcx_lottery_log';
    $lottery_log_sql = "CREATE TABLE IF NOT EXISTS `$table_zhuige_xcx_lottery_log` (
        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `user_id` bigint(20) UNSIGNED NOT NULL DEFAULT '0' COMMENT '用户ID',
        `prize_index` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '奖品索引',
        `prize_name` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品名称',
        `prize_type` varchar(50) NOT NULL DEFAULT 'virtual' COMMENT '奖品类型',
        `prize_value` text COMMENT '奖品值',
        `score_cost` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '消耗积分',
        `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1' COMMENT '状态:1=正常,0=删除',
        `time` int(10) UNSIGNED DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `status` (`status`),
        KEY `time` (`time`)
    ) $charset_collate;";
    dbDelta($lottery_log_sql);
}
