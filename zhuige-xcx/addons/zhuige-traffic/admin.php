<?php

/**
 * 追格小程序
 * 作者: 追格
 * 文档: https://www.zhuige.com/docs/zg.html
 * gitee: https://gitee.com/zhuige_com/zhuige_xcx
 * github: https://github.com/zhuige-com/zhuige_xcx
 * Copyright © 2022-2024 www.zhuige.com All rights reserved.
 */

//
// 微信流量主广告
//
CSF::createSection($prefix, array(
    'id'    => 'traffic',
    'title' => '微信流量主广告',
    'icon'  => 'fas fa-plus-circle',
));

//
// 广告设置
//
CSF::createSection($prefix, array(
    'parent' => 'traffic',
    'title' => '广告设置',
    'icon'  => 'fas fa-map-marker',
    'fields' => array(
        array(
            'id'     => 'traffic_index_list',
            'type'   => 'fieldset',
            'title'  => '首页广告',
            'fields' => array(
                array(
                    'id'    => 'ad',
                    'type'  => 'text',
                    'title' => '广告位ID',
                    'default' => '广告位ID'
                ),
                array(
                    'id'    => 'frequency',
                    'type'  => 'text',
                    'title' => '频率',
                    'default' => '4'
                ),
                array(
                    'id'    => 'title',
                    'type'  => 'text',
                    'title' => '标题',
                    'default' => '广告，也可以是生活的一部分'
                ),
                array(
                    'id'    => 'desc',
                    'type'  => 'text',
                    'title' => '描述',
                    'default' => '我是一个有温度的广告'
                ),
                array(
                    'id'    => 'switch',
                    'type'  => 'switcher',
                    'title' => '开启/停用',
                    'default' => '1'
                ),
                array(
                    'id'    => 'ad_chp',
                    'type'  => 'text',
                    'title' => '激励广告位ID',
                    'default' => '激励广告位ID'
                ),
                array(
                    'id'    => 'switch_chp',
                    'type'  => 'switcher',
                    'title' => '开启/停用',
                    'default' => '1'
                ),
            ),
        ),
        array(
            'id'     => 'traffic_topic_detail',
            'type'   => 'fieldset',
            'title'  => '帖子详情页底部广告',
            'fields' => array(
                array(
                    'id'    => 'ad_ysh',
                    'type'  => 'text',
                    'title' => '广告位ID',
                    'default' => '广告位ID'
                ),
                array(
                    'id'    => 'switch_ysh',
                    'type'  => 'switcher',
                    'title' => '开启/停用',
                    'default' => '1'
                ),
            ),
        ),
    )
));
