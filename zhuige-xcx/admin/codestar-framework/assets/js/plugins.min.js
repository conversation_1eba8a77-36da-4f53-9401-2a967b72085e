/* Codestar Framework | A Simple and Lightweight WordPress Option Framework - v2.2.1*/

!function(o){function e(t,e){this.element=o(t),this.options=e,this.init()}e.prototype.init=function(){this.element.chosen(this.options),this.container=this.element.next(".chosen-container"),this.search_field=this.container.find(".chosen-search-input"),this.is_multiple=this.container.hasClass("chosen-container-multi"),this.is_typing=!1,this.chosenXhr=null,this.events()},e.prototype.events=function(){var t=this;this.search_field.on("compositionstart",function(){t.is_typing=!0}),this.search_field.on("compositionend",function(){t.is_typing=!1,t.update_list()}),this.search_field.on("keyup",function(){t.update_list()}),this.search_field.on("focus",function(){t.search_field_focused()})},e.prototype.search_field_focused=function(){this.search_welcome_message(),0===this.options.min_length&&0===this.search_field.val().length&&this.update_list()},e.prototype.search_welcome_message=function(){var t=o.trim(this.search_field.val()),e=this.container.find(".chosen-results");0===e.children().length&&0===t.length&&e.html('<li class="no-results">'+this.options.typing_text.replace("%s",this.options.min_length-t.length)+"</li>")},e.prototype.update_list=function(){var e=this;if(this.search_welcome_message(),!this.is_typing){var t=o.trim(this.search_field.val()),s=t.length<this.options.min_length?this.options.typing_text.replace("%s",this.options.min_length-t.length):this.options.searching_text;this.container.find(".no-results").text(s),t!==this.search_field.data("prevVal")&&(this.search_field.data("prevVal",t),this.timer&&clearTimeout(this.timer),t.length<this.options.min_length||(this.timer=setTimeout(function(){e.chosenXhr&&e.chosenXhr.abort(),e.options.data.term=t,e.chosenXhr=window.wp.ajax.post("csf-chosen",e.options.data).done(function(t){e.show_results(t)}).fail(function(t){e.container.find(".no-results").text(t.error)})},this.options.type_delay)))}},e.prototype.show_results=function(t){var s=this;if(!this.is_typing&&null!==t){if(0===t.length)return this.element.data().chosen.no_results_clear(),void this.element.data().chosen.no_results(this.search_field.val());var i=[];this.element.find("option").each(function(){o(this).is(":selected")?i.push(o(this).val()+"-"+o(this).text()):o(this).attr("value").length&&o(this).remove()}),o.each(t,function(t,e){-1===o.inArray(e.value+"-"+e.text,i)&&o("<option />").attr("value",e.value).html(e.text).appendTo(s.element)});var e=this.search_field.val(),n=this.search_field.innerWidth();if(this.element.trigger("chosen:updated"),this.is_multiple){var r=this.element.parent().find(".csf-hide-select").val()||[];this.element.CSFChosenOrder(r,!0),this.search_field.css("width",n)}this.search_field.val(e),null!==this.chosenXhr.done&&this.chosenXhr.done(t)}},o.fn.CSFAjaxChosen=function(t){return this.each(function(){new e(this,t)})}}(jQuery),function(){var s,i,o=[].indexOf||function(t){for(var e=0,s=this.length;e<s;e++)if(e in this&&this[e]===t)return e;return-1},n={}.hasOwnProperty;i=function(){function t(){}return t.insertAt=function(t,e,s){return s.insertBefore(t,s.children[e].nextSibling)},t.getFlattenedOptionsAndGroups=function(t){var e,s,i,n,r,o,h,l,c;for(e=[],o=0,l=(i=Array.prototype.filter.call(t.childNodes,function(t){var e;return"OPTION"===(e=t.nodeName.toUpperCase())||"OPTGROUP"===e})).length;o<l;o++)if(s=i[o],e.push(s),"OPTGROUP"===s.nodeName.toUpperCase())for(h=0,c=(r=Array.prototype.filter.call(s.childNodes,function(t){return"OPTION"===t.nodeName.toUpperCase()})).length;h<c;h++)n=r[h],e.push(n);return e},t.isValidMultipleSelectElement=function(t){return null!=t&&"SELECT"===t.nodeName&&t.multiple},t.getChosenUIContainer=function(t){return""!==t.id?document.getElementById(t.id.replace(/-/g,"_")+"_chosen"):this.searchChosenUIContainer(t)},t.isChosenified=function(t){return null!=this.getChosenUIContainer(t)},t.forceSelection=function(t,e){var s,i,n,r;for(n=this.getFlattenedOptionsAndGroups(t),s=0;s<n.length;)r=(i=n[s]).getAttribute("value"),0<=o.call(e,r)?(i.selected=!0,i.setAttribute("selected","")):(i.selected=!1,i.removeAttribute("selected")),s++;return this.triggerEvent(t,"chosen:updated")},t.CSFChosenOrder=function(t,e,s){var i,n,r,o,h,l,c,a,u,_,d,p,f,g;if(null!=this.getDOMElement&&(t=this.getDOMElement(t)),this.isValidMultipleSelectElement(t)&&null!=(n=this.getChosenUIContainer(t))&&e instanceof Array){for(e=e.map(Function.prototype.call,String.prototype.trim),c=this.getFlattenedOptionsAndGroups(t),null!=s&&!0===s&&this.forceSelection(t,e),g=[],r=_=0,p=e.length;_<p;r=++_){for(h=e[r],a=null,o=d=0,f=c.length;d<f;o=++d)c[o].value===h&&(a=o);i=n.querySelectorAll(".search-choice"),u=this.relAttributeName,null!=(l=Array.prototype.filter.call(i,function(t){return null!=t.querySelector("a.search-choice-close["+u+'="'+a+'"]')})[0])&&(n.querySelector("ul.chosen-choices"),g.push(this.insertAt(l,r,n.querySelector("ul.chosen-choices"))))}return g}},t}(),(s=jQuery).fn.extend({CSFChosenOrder:function(t,e){return _CSFChosenOrder.CSFChosenOrder(this,t,e)}}),this._CSFChosenOrder=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return function(t,e){for(var s in e)n.call(e,s)&&(t[s]=e[s]);function i(){this.constructor=t}i.prototype=e.prototype,t.prototype=new i,t.__super__=e.prototype}(e,i),e.relAttributeName="data-option-array-index",e.isjQueryObject=function(t){return"undefined"!=typeof jQuery&&null!==jQuery&&t instanceof jQuery},e.getDOMElement=function(t){return this.isjQueryObject(t)?t.get(0):t},e.searchChosenUIContainer=function(t){return null!=s(t).data("chosen")?s(t).data("chosen").container[0]:s(t).next(".chosen-container.chosen-container-multi").get(0)},e.triggerEvent=function(t,e){return s(t).trigger(e)},e}()}.call(this),function(){var h,s,n,o,r={}.hasOwnProperty;(o=function(){function t(){this.options_index=0,this.parsed=[]}return t.prototype.add_node=function(t){return"OPTGROUP"===t.nodeName.toUpperCase()?this.add_group(t):this.add_option(t)},t.prototype.add_group=function(t){var e,s,i,n,r,o;for(e=this.parsed.length,this.parsed.push({array_index:e,group:!0,label:t.label,title:t.title?t.title:void 0,children:0,disabled:t.disabled,classes:t.className}),o=[],s=0,i=(r=t.childNodes).length;s<i;s++)n=r[s],o.push(this.add_option(n,e,t.disabled));return o},t.prototype.add_option=function(t,e,s){if("OPTION"===t.nodeName.toUpperCase())return""!==t.text?(null!=e&&(this.parsed[e].children+=1),this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,value:t.value,text:t.text,html:t.innerHTML,title:t.title?t.title:void 0,selected:t.selected,disabled:!0===s?s:t.disabled,group_array_index:e,group_label:null!=e?this.parsed[e].label:null,classes:t.className,style:t.style.cssText})):this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,empty:!0}),this.options_index+=1},t}()).select_to_array=function(t){var e,s,i,n,r;for(n=new o,s=0,i=(r=t.childNodes).length;s<i;s++)e=r[s],n.add_node(e);return n.parsed},s=function(){function n(t,e){var s,i;this.form_field=t,this.options=null!=e?e:{},this.label_click_handler=(s=this.label_click_handler,i=this,function(){return s.apply(i,arguments)}),n.browser_is_supported()&&(this.is_multiple=this.form_field.multiple,this.set_default_text(),this.set_default_values(),this.setup(),this.set_up_html(),this.register_observers(),this.on_ready())}return n.prototype.set_default_values=function(){var e,s;return this.click_test_action=(e=this,function(t){return e.test_active_click(t)}),this.activate_action=(s=this,function(t){return s.activate_field(t)}),this.active_field=!1,this.mouse_on_container=!1,this.results_showing=!1,this.result_highlighted=null,this.is_rtl=this.options.rtl||/\bchosen-rtl\b/.test(this.form_field.className),this.allow_single_deselect=null!=this.options.allow_single_deselect&&null!=this.form_field.options[0]&&""===this.form_field.options[0].text&&this.options.allow_single_deselect,this.disable_search_threshold=this.options.disable_search_threshold||0,this.disable_search=this.options.disable_search||!1,this.enable_split_word_search=null==this.options.enable_split_word_search||this.options.enable_split_word_search,this.group_search=null==this.options.group_search||this.options.group_search,this.search_contains=this.options.search_contains||!1,this.single_backstroke_delete=null==this.options.single_backstroke_delete||this.options.single_backstroke_delete,this.max_selected_options=this.options.max_selected_options||1/0,this.inherit_select_classes=this.options.inherit_select_classes||!1,this.display_selected_options=null==this.options.display_selected_options||this.options.display_selected_options,this.display_disabled_options=null==this.options.display_disabled_options||this.options.display_disabled_options,this.include_group_label_in_selected=this.options.include_group_label_in_selected||!1,this.max_shown_results=this.options.max_shown_results||Number.POSITIVE_INFINITY,this.case_sensitive_search=this.options.case_sensitive_search||!1,this.hide_results_on_select=null==this.options.hide_results_on_select||this.options.hide_results_on_select},n.prototype.set_default_text=function(){return this.form_field.getAttribute("data-placeholder")?this.default_text=this.form_field.getAttribute("data-placeholder"):this.is_multiple?this.default_text=this.options.placeholder_text_multiple||this.options.placeholder_text||n.default_multiple_text:this.default_text=this.options.placeholder_text_single||this.options.placeholder_text||n.default_single_text,this.default_text=this.escape_html(this.default_text),this.results_none_found=this.form_field.getAttribute("data-no_results_text")||this.options.no_results_text||n.default_no_result_text},n.prototype.choice_label=function(t){return this.include_group_label_in_selected&&null!=t.group_label?"<b class='group-name'>"+this.escape_html(t.group_label)+"</b>"+t.html:t.html},n.prototype.mouse_enter=function(){return this.mouse_on_container=!0},n.prototype.mouse_leave=function(){return this.mouse_on_container=!1},n.prototype.input_focus=function(t){if(this.is_multiple){if(!this.active_field)return setTimeout((e=this,function(){return e.container_mousedown()}),50)}else if(!this.active_field)return this.activate_field();var e},n.prototype.input_blur=function(t){if(!this.mouse_on_container)return this.active_field=!1,setTimeout((e=this,function(){return e.blur_test()}),100);var e},n.prototype.label_click_handler=function(t){return this.is_multiple?this.container_mousedown(t):this.activate_field()},n.prototype.results_option_build=function(t){var e,s,i,n,r,o,h;for(e="",n=h=0,r=(o=this.results_data).length;n<r&&((i="")!==(i=(s=o[n]).group?this.result_add_group(s):this.result_add_option(s))&&(h++,e+=i),(null!=t?t.first:void 0)&&(s.selected&&this.is_multiple?this.choice_build(s):s.selected&&!this.is_multiple&&this.single_set_selected_text(this.choice_label(s))),!(h>=this.max_shown_results));n++);return e},n.prototype.result_add_option=function(t){var e,s;return t.search_match&&this.include_option_in_results(t)?(e=[],t.disabled||t.selected&&this.is_multiple||e.push("active-result"),!t.disabled||t.selected&&this.is_multiple||e.push("disabled-result"),t.selected&&e.push("result-selected"),null!=t.group_array_index&&e.push("group-option"),""!==t.classes&&e.push(t.classes),(s=document.createElement("li")).className=e.join(" "),t.style&&(s.style.cssText=t.style),s.setAttribute("data-option-array-index",t.array_index),s.innerHTML=t.highlighted_html||t.html,t.title&&(s.title=t.title),this.outerHTML(s)):""},n.prototype.result_add_group=function(t){var e,s;return(t.search_match||t.group_match)&&0<t.active_options?((e=[]).push("group-result"),t.classes&&e.push(t.classes),(s=document.createElement("li")).className=e.join(" "),s.innerHTML=t.highlighted_html||this.escape_html(t.label),t.title&&(s.title=t.title),this.outerHTML(s)):""},n.prototype.results_update_field=function(){if(this.set_default_text(),this.is_multiple||this.results_reset_cleanup(),this.result_clear_highlight(),this.results_build(),this.results_showing)return this.winnow_results()},n.prototype.reset_single_select_options=function(){var t,e,s,i,n;for(n=[],t=0,e=(s=this.results_data).length;t<e;t++)(i=s[t]).selected?n.push(i.selected=!1):n.push(void 0);return n},n.prototype.results_toggle=function(){return this.results_showing?this.results_hide():this.results_show()},n.prototype.results_search=function(t){return this.results_showing?this.winnow_results():this.results_show()},n.prototype.winnow_results=function(t){var e,s,i,n,r,o,h,l,c,a,u,_,d,p,f;for(this.no_results_clear(),a=0,e=(h=this.get_search_text()).replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),c=this.get_search_regex(e),i=0,n=(l=this.results_data).length;i<n;i++)(r=l[i]).search_match=!1,_=u=null,r.highlighted_html="",this.include_option_in_results(r)&&(r.group&&(r.group_match=!1,r.active_options=0),null!=r.group_array_index&&this.results_data[r.group_array_index]&&(0===(u=this.results_data[r.group_array_index]).active_options&&u.search_match&&(a+=1),u.active_options+=1),f=r.group?r.label:r.text,r.group&&!this.group_search||(_=this.search_string_match(f,c),r.search_match=null!=_,r.search_match&&!r.group&&(a+=1),r.search_match?(h.length&&(d=_.index,o=f.slice(0,d),s=f.slice(d,d+h.length),p=f.slice(d+h.length),r.highlighted_html=this.escape_html(o)+"<em>"+this.escape_html(s)+"</em>"+this.escape_html(p)),null!=u&&(u.group_match=!0)):null!=r.group_array_index&&this.results_data[r.group_array_index].search_match&&(r.search_match=!0)));return this.result_clear_highlight(),a<1&&h.length?(this.update_results_content(""),this.no_results(h)):(this.update_results_content(this.results_option_build()),(null!=t?t.skip_highlight:void 0)?void 0:this.winnow_results_set_highlight())},n.prototype.get_search_regex=function(t){var e,s;return s=this.search_contains?t:"(^|\\s|\\b)"+t+"[^\\s]*",this.enable_split_word_search||this.search_contains||(s="^"+s),e=this.case_sensitive_search?"":"i",new RegExp(s,e)},n.prototype.search_string_match=function(t,e){var s;return s=e.exec(t),!this.search_contains&&(null!=s?s[1]:void 0)&&(s.index+=1),s},n.prototype.choices_count=function(){var t,e,s;if(null!=this.selected_option_count)return this.selected_option_count;for(t=this.selected_option_count=0,e=(s=this.form_field.options).length;t<e;t++)s[t].selected&&(this.selected_option_count+=1);return this.selected_option_count},n.prototype.choices_click=function(t){if(t.preventDefault(),this.activate_field(),!this.results_showing&&!this.is_disabled)return this.results_show()},n.prototype.keydown_checker=function(t){var e,s;switch(s=null!=(e=t.which)?e:t.keyCode,this.search_field_scale(),8!==s&&this.pending_backstroke&&this.clear_backstroke(),s){case 8:this.backstroke_length=this.get_search_field_value().length;break;case 9:this.results_showing&&!this.is_multiple&&this.result_select(t),this.mouse_on_container=!1;break;case 13:case 27:this.results_showing&&t.preventDefault();break;case 32:this.disable_search&&t.preventDefault();break;case 38:t.preventDefault(),this.keyup_arrow();break;case 40:t.preventDefault(),this.keydown_arrow()}},n.prototype.keyup_checker=function(t){var e,s;switch(s=null!=(e=t.which)?e:t.keyCode,this.search_field_scale(),s){case 8:this.is_multiple&&this.backstroke_length<1&&0<this.choices_count()?this.keydown_backstroke():this.pending_backstroke||(this.result_clear_highlight(),this.results_search());break;case 13:t.preventDefault(),this.results_showing&&this.result_select(t);break;case 27:this.results_showing&&this.results_hide();break;case 9:case 16:case 17:case 18:case 38:case 40:case 91:break;default:this.results_search()}},n.prototype.clipboard_event_checker=function(t){var e;if(!this.is_disabled)return setTimeout((e=this,function(){return e.results_search()}),50)},n.prototype.container_width=function(){return null!=this.options.width?this.options.width:this.form_field.offsetWidth+"px"},n.prototype.include_option_in_results=function(t){return!(this.is_multiple&&!this.display_selected_options&&t.selected)&&(!(!this.display_disabled_options&&t.disabled)&&!t.empty)},n.prototype.search_results_touchstart=function(t){return this.touch_started=!0,this.search_results_mouseover(t)},n.prototype.search_results_touchmove=function(t){return this.touch_started=!1,this.search_results_mouseout(t)},n.prototype.search_results_touchend=function(t){if(this.touch_started)return this.search_results_mouseup(t)},n.prototype.outerHTML=function(t){var e;return t.outerHTML?t.outerHTML:((e=document.createElement("div")).appendChild(t),e.innerHTML)},n.prototype.get_single_html=function(){return'<a class="chosen-single chosen-default">\n  <span>'+this.default_text+'</span>\n  <div><b></b></div>\n</a>\n<div class="chosen-drop">\n  <div class="chosen-search">\n    <input class="chosen-search-input" type="text" autocomplete="off" />\n  </div>\n  <ul class="chosen-results"></ul>\n</div>'},n.prototype.get_multi_html=function(){return'<ul class="chosen-choices">\n  <li class="search-field">\n    <input class="chosen-search-input" type="text" autocomplete="off" value="'+this.default_text+'" />\n  </li>\n</ul>\n<div class="chosen-drop">\n  <ul class="chosen-results"></ul>\n</div>'},n.prototype.get_no_results_html=function(t){return'<li class="no-results">\n  '+this.results_none_found+" <span>"+this.escape_html(t)+"</span>\n</li>"},n.browser_is_supported=function(){return"Microsoft Internet Explorer"===window.navigator.appName?8<=document.documentMode:!(/iP(od|hone)/i.test(window.navigator.userAgent)||/IEMobile/i.test(window.navigator.userAgent)||/Windows Phone/i.test(window.navigator.userAgent)||/BlackBerry/i.test(window.navigator.userAgent)||/BB10/i.test(window.navigator.userAgent)||/Android.*Mobile/i.test(window.navigator.userAgent))},n.default_multiple_text="Select Some Options",n.default_single_text="Select an Option",n.default_no_result_text="No results match",n}(),(h=jQuery).fn.extend({chosen:function(i){return s.browser_is_supported()?this.each(function(t){var e,s;s=(e=h(this)).data("chosen"),"destroy"!==i?s instanceof n||e.data("chosen",new n(this,i)):s instanceof n&&s.destroy()}):this}}),n=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return function(t,e){for(var s in e)r.call(e,s)&&(t[s]=e[s]);function i(){this.constructor=t}i.prototype=e.prototype,t.prototype=new i,t.__super__=e.prototype}(e,s),e.prototype.setup=function(){return this.form_field_jq=h(this.form_field),this.current_selectedIndex=this.form_field.selectedIndex},e.prototype.set_up_html=function(){var t,e;return(t=["chosen-container"]).push("chosen-container-"+(this.is_multiple?"multi":"single")),this.inherit_select_classes&&this.form_field.className&&t.push(this.form_field.className),this.is_rtl&&t.push("chosen-rtl"),e={class:t.join(" "),title:this.form_field.title},this.form_field.id.length&&(e.id=this.form_field.id.replace(/[^\w]/g,"_")+"_chosen"),this.container=h("<div />",e),this.container.width(this.container_width()),this.is_multiple?this.container.html(this.get_multi_html()):this.container.html(this.get_single_html()),this.form_field_jq.hide().after(this.container),this.dropdown=this.container.find("div.chosen-drop").first(),this.search_field=this.container.find("input").first(),this.search_results=this.container.find("ul.chosen-results").first(),this.search_field_scale(),this.search_no_results=this.container.find("li.no-results").first(),this.is_multiple?(this.search_choices=this.container.find("ul.chosen-choices").first(),this.search_container=this.container.find("li.search-field").first()):(this.search_container=this.container.find("div.chosen-search").first(),this.selected_item=this.container.find(".chosen-single").first()),this.results_build(),this.set_tab_index(),this.set_label_behavior()},e.prototype.on_ready=function(){return this.form_field_jq.trigger("chosen:ready",{chosen:this})},e.prototype.register_observers=function(){var e,s,i,n,r,o,h,l,c,a,u,_,d,p,f,g,m,v,y,b,w,x,k,C;return this.container.on("touchstart.chosen",(e=this,function(t){e.container_mousedown(t)})),this.container.on("touchend.chosen",(s=this,function(t){s.container_mouseup(t)})),this.container.on("mousedown.chosen",(i=this,function(t){i.container_mousedown(t)})),this.container.on("mouseup.chosen",(n=this,function(t){n.container_mouseup(t)})),this.container.on("mouseenter.chosen",(r=this,function(t){r.mouse_enter(t)})),this.container.on("mouseleave.chosen",(o=this,function(t){o.mouse_leave(t)})),this.search_results.on("mouseup.chosen",(h=this,function(t){h.search_results_mouseup(t)})),this.search_results.on("mouseover.chosen",(l=this,function(t){l.search_results_mouseover(t)})),this.search_results.on("mouseout.chosen",(c=this,function(t){c.search_results_mouseout(t)})),this.search_results.on("mousewheel.chosen DOMMouseScroll.chosen",(a=this,function(t){a.search_results_mousewheel(t)})),this.search_results.on("touchstart.chosen",(u=this,function(t){u.search_results_touchstart(t)})),this.search_results.on("touchmove.chosen",(_=this,function(t){_.search_results_touchmove(t)})),this.search_results.on("touchend.chosen",(d=this,function(t){d.search_results_touchend(t)})),this.form_field_jq.on("chosen:updated.chosen",(p=this,function(t){p.results_update_field(t)})),this.form_field_jq.on("chosen:activate.chosen",(f=this,function(t){f.activate_field(t)})),this.form_field_jq.on("chosen:open.chosen",(g=this,function(t){g.container_mousedown(t)})),this.form_field_jq.on("chosen:close.chosen",(m=this,function(t){m.close_field(t)})),this.search_field.on("blur.chosen",(v=this,function(t){v.input_blur(t)})),this.search_field.on("keyup.chosen",(y=this,function(t){y.keyup_checker(t)})),this.search_field.on("keydown.chosen",(b=this,function(t){b.keydown_checker(t)})),this.search_field.on("focus.chosen",(w=this,function(t){w.input_focus(t)})),this.search_field.on("cut.chosen",(x=this,function(t){x.clipboard_event_checker(t)})),this.search_field.on("paste.chosen",(k=this,function(t){k.clipboard_event_checker(t)})),this.is_multiple?this.search_choices.on("click.chosen",(C=this,function(t){C.choices_click(t)})):this.container.on("click.chosen",function(t){t.preventDefault()})},e.prototype.destroy=function(){return h(this.container[0].ownerDocument).off("click.chosen",this.click_test_action),0<this.form_field_label.length&&this.form_field_label.off("click.chosen"),this.search_field[0].tabIndex&&(this.form_field_jq[0].tabIndex=this.search_field[0].tabIndex),this.container.remove(),this.form_field_jq.removeData("chosen"),this.form_field_jq.show()},e.prototype.search_field_disabled=function(){return this.is_disabled=this.form_field.disabled||this.form_field_jq.parents("fieldset").is(":disabled"),this.container.toggleClass("chosen-disabled",this.is_disabled),this.search_field[0].disabled=this.is_disabled,this.is_multiple||this.selected_item.off("focus.chosen",this.activate_field),this.is_disabled?this.close_field():this.is_multiple?void 0:this.selected_item.on("focus.chosen",this.activate_field)},e.prototype.container_mousedown=function(t){var e;if(!this.is_disabled)return!t||"mousedown"!==(e=t.type)&&"touchstart"!==e||this.results_showing||t.preventDefault(),null!=t&&h(t.target).hasClass("search-choice-close")?void 0:(this.active_field?this.is_multiple||!t||h(t.target)[0]!==this.selected_item[0]&&!h(t.target).parents("a.chosen-single").length||(t.preventDefault(),this.results_toggle()):(this.is_multiple&&this.search_field.val(""),h(this.container[0].ownerDocument).on("click.chosen",this.click_test_action),this.results_show()),this.activate_field())},e.prototype.container_mouseup=function(t){if("ABBR"===t.target.nodeName&&!this.is_disabled)return this.results_reset(t)},e.prototype.search_results_mousewheel=function(t){var e;if(t.originalEvent&&(e=t.originalEvent.deltaY||-t.originalEvent.wheelDelta||t.originalEvent.detail),null!=e)return t.preventDefault(),"DOMMouseScroll"===t.type&&(e*=40),this.search_results.scrollTop(e+this.search_results.scrollTop())},e.prototype.blur_test=function(t){if(!this.active_field&&this.container.hasClass("chosen-container-active"))return this.close_field()},e.prototype.close_field=function(){return h(this.container[0].ownerDocument).off("click.chosen",this.click_test_action),this.active_field=!1,this.results_hide(),this.container.removeClass("chosen-container-active"),this.clear_backstroke(),this.show_search_field_default(),this.search_field_scale(),this.search_field.blur()},e.prototype.activate_field=function(){if(!this.is_disabled)return this.container.addClass("chosen-container-active"),this.active_field=!0,this.search_field.val(this.search_field.val()),this.search_field.focus()},e.prototype.test_active_click=function(t){var e;return(e=h(t.target).closest(".chosen-container")).length&&this.container[0]===e[0]?this.active_field=!0:this.close_field()},e.prototype.results_build=function(){return this.parsing=!0,this.selected_option_count=null,this.results_data=o.select_to_array(this.form_field),this.is_multiple?this.search_choices.find("li.search-choice").remove():(this.single_set_selected_text(),this.disable_search||this.form_field.options.length<=this.disable_search_threshold?(this.search_field[0].readOnly=!0,this.container.addClass("chosen-container-single-nosearch")):(this.search_field[0].readOnly=!1,this.container.removeClass("chosen-container-single-nosearch"))),this.update_results_content(this.results_option_build({first:!0})),this.search_field_disabled(),this.show_search_field_default(),this.search_field_scale(),this.parsing=!1},e.prototype.result_do_highlight=function(t){var e,s,i,n;if(t.length){if(this.result_clear_highlight(),this.result_highlight=t,this.result_highlight.addClass("highlighted"),(i=parseInt(this.search_results.css("maxHeight"),10))+(n=this.search_results.scrollTop())<=(e=(s=this.result_highlight.position().top+this.search_results.scrollTop())+this.result_highlight.outerHeight()))return this.search_results.scrollTop(0<e-i?e-i:0);if(s<n)return this.search_results.scrollTop(s)}},e.prototype.result_clear_highlight=function(){return this.result_highlight&&this.result_highlight.removeClass("highlighted"),this.result_highlight=null},e.prototype.results_show=function(){return this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.container.addClass("chosen-with-drop"),this.results_showing=!0,this.search_field.focus(),this.search_field.val(this.get_search_field_value()),this.winnow_results(),this.form_field_jq.trigger("chosen:showing_dropdown",{chosen:this}))},e.prototype.update_results_content=function(t){return this.search_results.html(t)},e.prototype.results_hide=function(){return this.results_showing&&(this.result_clear_highlight(),this.container.removeClass("chosen-with-drop"),this.form_field_jq.trigger("chosen:hiding_dropdown",{chosen:this})),this.results_showing=!1},e.prototype.set_tab_index=function(t){var e;if(this.form_field.tabIndex)return e=this.form_field.tabIndex,this.form_field.tabIndex=-1,this.search_field[0].tabIndex=e},e.prototype.set_label_behavior=function(){if(this.form_field_label=this.form_field_jq.parents("label"),!this.form_field_label.length&&this.form_field.id.length&&(this.form_field_label=h("label[for='"+this.form_field.id+"']")),0<this.form_field_label.length)return this.form_field_label.on("click.chosen",this.label_click_handler)},e.prototype.show_search_field_default=function(){return this.is_multiple&&this.choices_count()<1&&!this.active_field?(this.search_field.val(this.default_text),this.search_field.addClass("default")):(this.search_field.val(""),this.search_field.removeClass("default"))},e.prototype.search_results_mouseup=function(t){var e;if((e=h(t.target).hasClass("active-result")?h(t.target):h(t.target).parents(".active-result").first()).length)return this.result_highlight=e,this.result_select(t),this.search_field.focus()},e.prototype.search_results_mouseover=function(t){var e;if(e=h(t.target).hasClass("active-result")?h(t.target):h(t.target).parents(".active-result").first())return this.result_do_highlight(e)},e.prototype.search_results_mouseout=function(t){if(h(t.target).hasClass("active-result")||h(t.target).parents(".active-result").first())return this.result_clear_highlight()},e.prototype.choice_build=function(t){var e,s,i;return e=h("<li />",{class:"search-choice"}).html("<span>"+this.choice_label(t)+"</span>"),t.disabled?e.addClass("search-choice-disabled"):((s=h("<a />",{class:"search-choice-close","data-option-array-index":t.array_index})).on("click.chosen",(i=this,function(t){return i.choice_destroy_link_click(t)})),e.append(s)),this.search_container.before(e)},e.prototype.choice_destroy_link_click=function(t){if(t.preventDefault(),t.stopPropagation(),!this.is_disabled)return this.choice_destroy(h(t.target))},e.prototype.choice_destroy=function(t){if(this.result_deselect(t[0].getAttribute("data-option-array-index")))return this.active_field?this.search_field.focus():this.show_search_field_default(),this.is_multiple&&0<this.choices_count()&&this.get_search_field_value().length<1&&this.results_hide(),t.parents("li").first().remove(),this.search_field_scale()},e.prototype.results_reset=function(){if(this.reset_single_select_options(),this.form_field.options[0].selected=!0,this.single_set_selected_text(),this.show_search_field_default(),this.results_reset_cleanup(),this.trigger_form_field_change(),this.active_field)return this.results_hide()},e.prototype.results_reset_cleanup=function(){return this.current_selectedIndex=this.form_field.selectedIndex,this.selected_item.find("abbr").remove()},e.prototype.result_select=function(t){var e,s;if(this.result_highlight)return e=this.result_highlight,this.result_clear_highlight(),this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.is_multiple?e.removeClass("active-result"):this.reset_single_select_options(),e.addClass("result-selected"),(s=this.results_data[e[0].getAttribute("data-option-array-index")]).selected=!0,this.form_field.options[s.options_index].selected=!0,this.selected_option_count=null,this.is_multiple?this.choice_build(s):this.single_set_selected_text(this.choice_label(s)),this.is_multiple&&(!this.hide_results_on_select||t.metaKey||t.ctrlKey)?t.metaKey||t.ctrlKey?this.winnow_results({skip_highlight:!0}):(this.search_field.val(""),this.winnow_results()):(this.results_hide(),this.show_search_field_default()),(this.is_multiple||this.form_field.selectedIndex!==this.current_selectedIndex)&&this.trigger_form_field_change({selected:this.form_field.options[s.options_index].value}),this.current_selectedIndex=this.form_field.selectedIndex,t.preventDefault(),this.search_field_scale())},e.prototype.single_set_selected_text=function(t){return null==t&&(t=this.default_text),t===this.default_text?this.selected_item.addClass("chosen-default"):(this.single_deselect_control_build(),this.selected_item.removeClass("chosen-default")),this.selected_item.find("span").html(t)},e.prototype.result_deselect=function(t){var e;return e=this.results_data[t],!this.form_field.options[e.options_index].disabled&&(e.selected=!1,this.form_field.options[e.options_index].selected=!1,this.selected_option_count=null,this.result_clear_highlight(),this.results_showing&&this.winnow_results(),this.trigger_form_field_change({deselected:this.form_field.options[e.options_index].value}),this.search_field_scale(),!0)},e.prototype.single_deselect_control_build=function(){if(this.allow_single_deselect)return this.selected_item.find("abbr").length||this.selected_item.find("span").first().after('<abbr class="search-choice-close"></abbr>'),this.selected_item.addClass("chosen-single-with-deselect")},e.prototype.get_search_field_value=function(){return this.search_field.val()},e.prototype.get_search_text=function(){return h.trim(this.get_search_field_value())},e.prototype.escape_html=function(t){return h("<div/>").text(t).html()},e.prototype.winnow_results_set_highlight=function(){var t,e;if(null!=(t=(e=this.is_multiple?[]:this.search_results.find(".result-selected.active-result")).length?e.first():this.search_results.find(".active-result").first()))return this.result_do_highlight(t)},e.prototype.no_results=function(t){var e;return e=this.get_no_results_html(t),this.search_results.append(e),this.form_field_jq.trigger("chosen:no_results",{chosen:this})},e.prototype.no_results_clear=function(){return this.search_results.find(".no-results").remove()},e.prototype.keydown_arrow=function(){var t;return this.results_showing&&this.result_highlight?(t=this.result_highlight.nextAll("li.active-result").first())?this.result_do_highlight(t):void 0:this.results_show()},e.prototype.keyup_arrow=function(){var t;return this.results_showing||this.is_multiple?this.result_highlight?(t=this.result_highlight.prevAll("li.active-result")).length?this.result_do_highlight(t.first()):(0<this.choices_count()&&this.results_hide(),this.result_clear_highlight()):void 0:this.results_show()},e.prototype.keydown_backstroke=function(){var t;return this.pending_backstroke?(this.choice_destroy(this.pending_backstroke.find("a").first()),this.clear_backstroke()):(t=this.search_container.siblings("li.search-choice").last()).length&&!t.hasClass("search-choice-disabled")?(this.pending_backstroke=t,this.single_backstroke_delete?this.keydown_backstroke():this.pending_backstroke.addClass("search-choice-focus")):void 0},e.prototype.clear_backstroke=function(){return this.pending_backstroke&&this.pending_backstroke.removeClass("search-choice-focus"),this.pending_backstroke=null},e.prototype.search_field_scale=function(){var t,e,s,i,n,r,o;if(this.is_multiple){for(n={position:"absolute",left:"-1000px",top:"-1000px",display:"none",whiteSpace:"pre"},e=0,s=(r=["fontSize","fontStyle","fontWeight","fontFamily","lineHeight","textTransform","letterSpacing"]).length;e<s;e++)n[i=r[e]]=this.search_field.css(i);return(t=h("<div />").css(n)).text(this.get_search_field_value()),h("body").append(t),o=t.width()+25,t.remove(),this.container.is(":visible")&&(o=Math.min(this.container.outerWidth()-10,o)),this.search_field.width(o)}},e.prototype.trigger_form_field_change=function(t){return this.form_field_jq.trigger("input",t),this.form_field_jq.trigger("change",t)},e}()}.call(this),function(o){"use strict";function n(t,e,s){this.init(t,e,s)}function t(){this.rules=[]}o.extend(n.prototype,{init:function(t,e,s){this.controller=t,this.condition=e,this.value=s,this.rules=[],this.controls=[]},evalCondition:function(t,e,s,i,n){if("=="==s)return this.checkBoolean(i)==this.checkBoolean(n);if("!="==s)return this.checkBoolean(i)!=this.checkBoolean(n);if(">="==s)return Number(n)>=Number(i);if("<="==s)return Number(n)<=Number(i);if(">"==s)return Number(n)>Number(i);if("<"==s)return Number(n)<Number(i);if("()"==s)return window[i](t,e,n);if("any"==s){if(o.isArray(n)){for(var r=n.length-1;0<=r;r--)if(-1!==o.inArray(n[r],i.split(",")))return!0}else if(-1!==o.inArray(n,i.split(",")))return!0}else if("not-any"==s)if(o.isArray(n)){for(r=n.length-1;0<=r;r--)if(-1==o.inArray(n[r],i.split(",")))return!0}else if(-1==o.inArray(n,i.split(",")))return!0;return!1},checkBoolean:function(t){switch(t){case!0:case"true":case 1:case"1":t=!0;break;case null:case!1:case"false":case 0:case"0":t=!1}return t},checkCondition:function(t){if(!this.condition)return!0;var e=t.find(this.controller),s=this.getControlValue(t,e);return void 0!==s&&(s=this.normalizeValue(e,this.value,s),this.evalCondition(t,e,this.condition,this.value,s))},normalizeValue:function(t,e,s){return"number"==typeof e?parseFloat(s):s},getControlValue:function(t,e){return 1<e.length&&("radio"==e.attr("type")||"checkbox"==e.attr("type"))?e.filter(":checked").map(function(){return this.value}).get():"checkbox"==e.attr("type")||"radio"==e.attr("type")?e.is(":checked"):e.val()},createRule:function(t,e,s){var i=new n(t,e,s);return this.rules.push(i),i},include:function(t){this.controls.push(t)},applyRule:function(s,t){var e;e=void 0===t?this.checkCondition(s):t;var i=o.map(this.controls,function(t,e){return s.find(t)});e?(o(i).each(function(){o(this).removeClass("csf-depend-on")}),o(this.rules).each(function(){this.applyRule(s)})):(o(i).each(function(){o(this).addClass("csf-depend-on")}),o(this.rules).each(function(){this.applyRule(s,!1)}))}}),o.extend(t.prototype,{createRule:function(t,e,s){var i=new n(t,e,s);return this.rules.push(i),i},applyRules:function(t){o(this.rules).each(function(){this.applyRule(t)})}}),o.csf_deps={createRuleset:function(){return new t},enable:function(s,i,n){return s.on("change keyup",function(t){var e=t.target.getAttribute("data-depend-id")||t.target.getAttribute("data-sub-depend-id");-1!==n.indexOf(e)&&i.applyRules(s)}),i.applyRules(s),!0}}}(jQuery),function(t,s){if("function"==typeof define&&define.amd)define(["exports","jquery"],function(t,e){return s(t,e)});else if("undefined"!=typeof exports){var e=require("jquery");s(exports,e)}else s(t,t.jQuery||t.Zepto||t.ender||t.$)}(this,function(t,e){var h={validate:/^(?!(_nonce|_pseudo))[a-zA-Z0-9_-]*(?:\[(?:\d*|(?!(_nonce|_pseudo))[a-zA-Z0-9_-]+)\])*$/i,key:/[a-zA-Z0-9_-]+|(?=\[\])/g,named:/^[a-zA-Z0-9_-]+$/,push:/^$/,fixed:/^\d+$/};function s(i,t){var s={},r={};function o(t,e,s){return t[e]=s,t}function e(){return s}this.addPair=function(t){if(!h.validate.test(t.name))return this;var e=function(t,e){for(var s,i=t.match(h.key);void 0!==(s=i.pop());)h.push.test(s)?e=o([],(n=t.replace(/\[\]$/,""),void 0===r[n]&&(r[n]=0),r[n]++),e):h.fixed.test(s)?e=o([],s,e):h.named.test(s)&&(e=o({},s,e));var n;return e}(t.name,t.value);return s=i.extend(!0,s,e),this},this.addPairs=function(t){if(!i.isArray(t))throw new Error("formSerializer.addPairs expects an Array");for(var e=0,s=t.length;e<s;e++)this.addPair(t[e]);return this},this.serialize=e,this.serializeJSON=function(){return JSON.stringify(e())}}return s.patterns=h,s.serializeObject=function(){return new s(e,this).addPairs(this.serializeArray()).serialize()},s.serializeJSON=function(){return new s(e,this).addPairs(this.serializeArray()).serializeJSON()},void 0!==e.fn&&(e.fn.serializeObjectCSF=s.serializeObject,e.fn.serializeJSONCSF=s.serializeJSON),t.FormSerializer=s});