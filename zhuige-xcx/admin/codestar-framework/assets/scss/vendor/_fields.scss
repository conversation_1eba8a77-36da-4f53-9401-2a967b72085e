/**
 * 03. Fields
 */
.csf-field{
  position: relative;
  padding: 30px;

  + .csf-field:not(.hidden){
    border-top: 1px solid #eee;
  }

  p:first-child{
    margin-top: 0;
  }

  p:last-child{
    margin-bottom: 0;
  }

  &:after,
  &:before{
    content: " ";
    display: table;
  }

  &:after{
    clear: both;
  }

  h4{
    margin-top: 0;
  }

  .csf-title{
    position: relative;
    width: 20%;
    float: left;

    h4{
      margin: 0;
      color: #23282d;
    }
  }

  .csf-fieldset{
    margin-left: 25%;
  }
}

.csf-pseudo-field{
  padding: 0 5px 0 0 !important;
  display: inline-block;

  + .csf-pseudo-field{
    border: 0;
  }

  pre{
    display: none;
  }
}

/**
 * 03. 02. Field: accordion
 */
.csf-field-accordion{

  .csf-accordion-item{
    position: relative;
    margin-bottom: 5px;

    h4{
      font-size: 1em;
    }
  }

  .csf-accordion-title{
    display: block;
    cursor: pointer;
    position: relative;
    margin: 0;
    padding: 15px;
    min-height: 0;
    font-size: 100%;
    -webkit-user-select: none;
    user-select: none;
    border: 1px solid #e5e5e5;
    background-color: #fafafa;
    @include transition(border-color 0.15s);

    &:active,
    &:hover,
    &:focus{
      border: 1px solid #bbb;
      background-color: #fafafa;
      outline: none;
    }
  }

  .csf-accordion-icon{
    color: #aaa;
    width: 14px;
    text-align: center;
  }

  .csf--icon{
    width: 18px;
    text-shadow: center;
  }

  .csf-accordion-content{
    display: none;
    padding: 0;
    border: 1px solid #e5e5e5;
    border-top: none;
    background-color: #fff;

    > .csf-field{
      padding: 15px;
    }
  }

  .csf-accordion-open{
    display: block;
  }
}

/**
 * 03. 03. Field: background
 */
.csf-field-background{

  select{
    width: 100%;
  }

  .csf-field{
    float: left;
    padding: 0;
    border: 0;
  }

  .csf--block{
    float: left;
    margin-bottom: 15px;
    box-sizing: border-box;
  }

  .csf--title{
    float: left;
    color: #999;
    margin-top: 3px;
    margin-right: 5px;
  }

  .csf--select{
    width: 25%;
    padding-right: 10px;
    box-sizing: border-box;

    .csf-field{
      width: 100%;
    }
  }

  .csf--media{
    width: 100%;
    padding-right: 10px;

    .csf-field{
      width: 100%;
    }
  }
}

/**
 * 03. 04. Field: backup
 */
.csf-field-backup{

  textarea{
    width: 100%;
    min-height: 200px;
    margin-bottom: 5px;
  }

  small{
    display: inline-block;
    margin: 5px;
  }

  hr{
    margin: 20px 0;
    border: none;
    border-bottom: 1px solid #e5e5e5;
  }
}

/**
 * 03. 05. Field: border, spacing, dimensions
 */
.csf-field-border,
.csf-field-spacing,
.csf-field-dimensions{

  .csf--left{
    float: left;
  }

  .csf--input{
    float: left;
    margin-right: 10px;
    margin-bottom: 7px;

    select{
      margin: 0;
      line-height: 26px;
    }

    input{
      line-height: 26px;
      float: left;
      margin: 0;
      padding: 0;
      width: 65px;
      max-width: 100%;
      text-align: center;
    }
  }

  .csf--label{
    float: left;
    max-width: 100%;
    font-size: 12px;
    line-height: 26px;
    vertical-align: top;
    text-align: center;
    color: #555;
    border: 1px solid #ddd;
    background-color: #eee;
    padding: 0 6px;
  }

  .csf--label-icon{
    min-width: 20px;
    border-right: 0;
    border-radius: 2px 0 0 2px;
  }

  .csf--label-unit{
    color: #999;
    border-left: 0;
    border-radius: 0 2px 2px 0;
  }
}

/**
 * 03. 06. Field: button_set
 */
.csf-field-button_set{

  .csf--buttons{
    display: inline-block;
  }

  .csf--button{
    position: relative;
    z-index: 1;
    float: left;
    cursor: pointer;
    padding: 7px 14px;
    min-width: 40px;
    text-align: center;
    color: #555;
    border: 1px solid #cccccc;
    background-color: #f7f7f7;
    user-select: none;
    -webkit-user-select: none;
    box-shadow: 0 1px 0 rgba(0,0,0,0.1);

    &:first-child{
      border-radius: 4px 0 0 4px;
    }

    &:last-child{
      border-radius: 0 4px 4px 0;
    }

    &:not(:first-child){
      margin-left: -1px;
    }

    &:hover{
      background-color: #eee;
    }
  }

  .csf--active:hover,
  .csf--active{
    z-index: 2;
    color: #fff;
    border-color: #006799;
    background-color: #0085ba;
  }

  input{
    display: none;
  }
}

/**
 * 03. 07. Field: checkbox, radio
 */
.csf-field-checkbox,
.csf-field-radio{

  ul{
    margin: 0;
    padding: 0;
    list-style-type: none;
    overflow-y: auto;
    max-height: 305px;

    ul{
      max-height: none;
    }
  }

  .csf--inline-list li{
    display: inline-block;
    margin-right: 15px;
  }

  input[type="radio"]:checked:before{
    line-height: 10px;
  }

  .csf-checker{
    cursor: pointer;
  }
}

/**
 * 03. 08. Field: code_editor
 */
.csf-field-code_editor{

  .CodeMirror{
    width: 100%;
    height: 400px;
    border: 1px solid #eee;
  }

  textarea{
    width: 100%;
    height: 400px;
  }
}

/**
 * 03. 09. Field: color
 */
.csf-field-color{

  > input{
    opacity: 0.75;
    width: 115px;
    max-width: 100%;
  }
}

/**
 * 03. 10. Field: color_group
 */
.csf-field-color_group{

  .csf--left{
    float: left;
    margin-right: 10px;
    margin-bottom: 5px;
  }

  .csf--title{
    color: #999;
    margin-bottom: 5px;
  }
}

/**
 * 03. 11. Field: fieldset
 */
.csf-field-fieldset{

  .csf-fieldset-content{
    border: 1px solid #eee;
    background-color: #fff;

    > .csf-field{
      padding: 15px;
    }
  }
}

/**
 * 03. 12. Field: date
 */
.csf-field-date{

  .csf--to{
    margin-left: 7px;
  }
}

.csf-datepicker-wrapper{
  margin-top: 5px;
  width: auto;
  background-color: #fff;
  z-index: 9999999 !important;
  @include box-shadow(0 0 30px rgba(black, 0.15));

  *{
    float: none;
    margin: 0;
    padding: 0;
    font-family: inherit;
    font-weight: normal;
    font-style: normal;
    text-decoration: none;
    border: none;
    background: none;
    @include box-shadow(none);
    @include border-radius(none);
  }

  .ui-widget-header,
  .ui-datepicker-header{
    color: #fff;
    background: #00a0d2;
  }

  .ui-datepicker-header .ui-state-hover{
    cursor: pointer;
  }

  .ui-datepicker-title{
    font-size: 14px;
    line-height: 40px;
    text-align: center;
  }

  .ui-datepicker-prev,
  .ui-datepicker-next{
    position: static;
    top: auto;
    left: auto;
    right: auto;
    font-family: "FontAwesome";
    font-size: 12px;
    text-align: center;
    width: 41px;
    height: 40px;
    line-height: 40px;
    color: #fff;
    background-color: rgba(white, 0.1);
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .ui-datepicker-next span,
  .ui-datepicker-prev span{
    display: none;
  }

  .ui-datepicker-prev{
    float: left;
  }

  .ui-datepicker-next{
    float: right;
  }

  .ui-datepicker-prev:before{
    content: '\f053';
  }

  .ui-datepicker-next:before{
    content: '\f054';
  }

  .ui-datepicker-prev-hover,
  .ui-datepicker-next-hover{
    opacity: 0.75;
  }

  tbody .ui-datepicker-week-col{
    background-color: #f7f7f7;
  }

  .ui-datepicker-buttonpane{
    padding: 10px;
    text-align: center;
    background-color: #f7f7f7;
  }

  .ui-datepicker-buttonpane button{
    cursor: pointer;
    margin: 0 5px;
    padding: 7px 14px;
    border: 1px solid #eee;
    background-color: #fff;
  }

  select{
    margin: 0 4px;
    color: #fff;
    border: 1px solid rgba(white, 0.5);
  }

  select option{
    color: #555;
  }

  table{
    font-size: 13px;
    border-collapse: collapse;
    width: 100%;
  }

  thead{
    color: #fff;
    background: #32373c;
  }

  th{
    text-align: center;
    padding: 7px;
    border: 1px solid #444;
  }

  td{
    text-align: center;
    border: 1px solid #f4f4f4;
  }

  td.ui-datepicker-other-month{
    border: transparent;
  }

  td .ui-state-default{
    color: #555;
    width: auto;
    display: block;
    padding: 6px 12px;
  }

  td .ui-state-active,
  td .ui-state-hover{
    color: #fff;
    background-color: #0073aa;
  }

  td.ui-state-disabled .ui-state-default{
    opacity: 0.5;
  }
}

/**
 * 03. 13. Field: gallery
 */
.csf-field-gallery{

  input{
    display: none;
  }

  ul{
    margin: 0;
    padding: 0;
    list-style-type: none;

    li{
      display: inline-block;
      position: relative;
      padding: 4px;
      margin: 0 5px 10px 0;
      border: 1px solid #ccc;
      background-color: #f9f9f9;
      @include border-radius(2px);
      @include box-shadow(0 1px 0 rgba(black, 0.08));

      img{
        max-height: 60px;
        display: inline-block;
        vertical-align: middle;
      }
    }
  }

  .button{
    margin-right: 5px;
    margin-bottom: 5px;
  }
}

/**
 * 03. 14. Field: group
 */
.csf-field-group{

  .csf-cloneable-hidden{
    display: none !important;
  }

  .csf-cloneable-wrapper{
    position: relative;
  }

  .csf-cloneable-item{
    display: none;
    position: relative;
    margin-bottom: 5px;

    h4{
      font-size: 1em;
    }
  }

  .ui-accordion .csf-cloneable-item{
    display: block;
  }

  .csf-cloneable-content{
    border: 1px solid #e5e5e5;
    background: #fff;

    > .csf-field{
      padding: 15px;
    }
  }

  .csf-cloneable-title{
    display: block;
    cursor: pointer;
    position: relative;
    margin: 0;
    padding: 15px 65px 15px 10px;
    min-height: 0;
    font-size: 100%;
    border: 1px solid #e5e5e5;
    background: #fafafa;
    -webkit-user-select: none;
    user-select: none;
    @include transition(border-color 0.15s);

    &:active,
    &:hover,
    &:focus{
      border: 1px solid #bbb;
      background: #fafafa;
      outline: none;
    }
  }

  .csf-cloneable-helper{
    position: absolute;
    top: 12px;
    right: 10px;
    z-index: 1;
    font-size: 14px;
    line-height: 1em;

    i{
      display: inline-block;
      cursor: pointer;
      padding: 5px;
      color: #999;

      &:hover{
        color: #555;
      }
    }
  }

  .csf-cloneable-content{
    padding: 0;
    border-top: 0;
  }

  .csf-cloneable-title-prefix,
  .csf-cloneable-title-number{
    margin-right: 5px;
  }

  .csf-cloneable-alert{
    display: none;
    margin-bottom: 5px;
    padding: 10px 20px;
    color: #a94442;
    border: 1px solid #ebccd1;
    background-color: #f2dede;
  }

  .widget-placeholder{
    margin-bottom: 10px;
    border: 1px dashed #f1c40f;
    background-color: #fffae4;
  }

  .csf-cloneable-header-icon{
    display: inline-block;
    text-align: center;
    font-size: 14px;
    width: 17px;
    color: #aaa;
    text-indent: 0;
    vertical-align: text-top;
  }

  .csf-cloneable-placeholder{
    background-color: #ddd;
    margin-top: 4px;
    width: 100px;
    height: 10px;
    font-size: 10px;
    line-height: 10px;
    display: inline-block;
    vertical-align: top;
    border-radius: 2px;
  }
}

/**
 * 03. 15. Field: icon
 */
.csf-field-icon{

  input{
    display: none;
  }

  .button{
    margin-right: 5px;
  }

  .csf-icon-preview i{
    display: inline-block;
    font-size: 14px;
    width: 30px;
    height: 26px;
    line-height: 26px;
    margin-right: 5px;
    text-align: center;
    vertical-align: top;
    color: #555;
    border: 1px solid #ccc;
    background-color: #f7f7f7;
    @include border-radius(3px);
    @include box-shadow(0 1px 0 rgba(black, 0.08));
    @include box-sizing(content-box);
  }
}

/**
 * 03. 16. Field: image_select
 */
.csf-field-image_select{

  .csf--image{
    cursor: pointer;
    position: relative;
    display: inline-block;
    max-width: 100%;
    margin: 0 5px 5px 0;
    vertical-align: bottom;
    border: 2px solid transparent;
    background-color: #fff;
    user-select: none;
    -webkit-user-select: none;
    @include transition(all 0.2s);

    &:before{
      position: absolute;
      top: 0;
      left: 0;
      text-align: center;
      font-size: 11px;
      font-family: FontAwesome;
      content: "\f00c";
      width: 15px;
      height: 15px;
      line-height: 15px;
      opacity: 0;
      color: #fff;
      background-color: #222;
      transition: opacity .2s;
    }
  }

  .csf--active{
    border-color: #222;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);

    &:before{
      opacity: 1;
    }
  }

  img{
    vertical-align: top;
  }

  input{
    display: none;
  }
}

/**
 * 03. 17. Field: link_color
 */
.csf-field-link_color{

  .csf--left{
    float: left;
    margin-right: 10px;
    margin-bottom: 5px;
  }

  .csf--title{
    color: #999;
    margin-bottom: 5px;
  }
}

/**
 * 03. 18. Field: media
 */
.csf-field-media{

  .csf--placeholder{
    margin-bottom: 10px;
    display: flex;

    input{
      width: 100%;
    }
  }

  .button{
    margin-left: 7px;
  }

  .hidden + .button{
    margin-left: 0;
  }

  .csf--preview{
    position: relative;

    .fa-times{
      position: absolute;
      z-index: 1;
      right: 4px;
      top: 4px;
      font-size: 14px;
      width: 22px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      text-decoration: none;
      color: #fff;
      background-color: #dd3333;
      opacity: 0.8;
      transition: all .2s;

      &:hover{
        opacity: 1;
      }

      &:focus{
        box-shadow: none;
      }
    }
  }
}

/**
 * 03. 19. Field: palette
 */
.csf-field-palette{

  .csf--palette{
    position: relative;
    display: inline-block;
    cursor: pointer;
    border: 2px solid #ddd;
    margin-right: 10px;
    margin-bottom: 10px;
    user-select: none;
    -webkit-user-select: none;
    transition: all .2s;

    span{
      vertical-align: middle;
      display: inline-block;
      width: 22px;
      height: 60px;
      line-height: 60px;
      overflow: hidden;
      text-indent: -999px;
    }

    &:before{
      position: absolute;
      top: 0;
      left: 0;
      text-align: center;
      font-size: 11px;
      font-family: FontAwesome;
      content: "\f00c";
      width: 15px;
      height: 15px;
      line-height: 15px;
      opacity: 0;
      color: #fff;
      background-color: #222;
      transition: opacity .2s;
    }
  }

  .csf--active{
    border-color: #222;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);

    &:before{
      opacity: 1;
    }
  }

  input{
    display: none;
  }
}

/**
 * 03. 20. Field: repeater
 */
.csf-field-repeater{

  .csf-field-text input{
    width: 100%;
  }

  .csf-repeater-hidden{
    display: none !important;
  }

  .csf-repeater-wrapper{

    .csf-repeater-item{
      display: table;
      width: 100%;
      margin-bottom: 5px;
      border: 1px solid #eee;

      h4{
        font-size: 1em;
      }
    }
  }

  .csf-repeater-content{
    width: 100%;
    display: table-cell;
    vertical-align: middle;
    background-color: #fff;

    > .csf-field{
      padding: 15px;
    }
  }

  .csf-repeater-helper{
    width: 100%;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    font-size: 14px;
    line-height: 1em;
    border-left: 1px solid #eee;
    background-color: #f7f7f7;

    i{
      display: inline-block;
      cursor: pointer;
      color: #999;
      padding: 5px;

      &:hover{
        color: #555;
      }
    }
  }

  .csf-repeater-helper-inner{
    width: 75px;
  }

  .csf-repeater-alert{
    display: none;
    margin-bottom: 5px;
    padding: 10px 20px;
    color: #a94442;
    border: 1px solid #ebccd1;
    background-color: #f2dede;
  }

  .widget-placeholder{
    height: 50px;
    margin-bottom: 3px;
    border: 1px dashed #f1c40f;
    background-color: #fffae4;
  }

  .ui-sortable-helper{
    height: 50px !important;
    overflow: hidden !important;
    border-color: #ccc !important;
    background-color: #eee !important;
    @include opacity(0.5);

    .csf-repeater-helper,
    .csf-repeater-content{
      display: none;
    }
  }
}

/**
 * 03. 21. Field: select
 */
.csf-field-select{

  select{
    max-width: 100%;
  }
}

/**
 * 03. 22. Field: slider
 */
.csf-field-slider{

  em{
    padding-left: 5px;
    color: #aaa;
  }

  input[type="text"]{
    width: 45px;
    margin-left: 15px;
    text-align: center;
  }

  .ui-slider{
    position: relative;
    width: 100%;
    height: 4px;
    border: none;
    background: #ddd;
    border-radius: 2px;
  }

  .ui-slider-range{
    height: 4px;
    border: none;
    background: #222;
    border-radius: 2px;
  }

  .ui-slider-handle{
    position: absolute;
    width: 18px;
    height: 18px;
    top: -7px;
    margin-left: -8px;
    border: none;
    background: #222;
    border-radius: 2px;
  }

  .ui-state-active,
  .ui-slider-handle:hover{
    cursor: pointer;
    background: #444;
  }
}

/**
 * 03. 23. Field: sortable
 */
.csf-field-sortable{

  .csf-field-text{

    input{
      width: 100%;
      max-width: 100%;
    }
  }

  .csf--sortable{

    .csf--sortable-item{
      display: table;
      width: 100%;
      margin-bottom: 5px;
      border: 1px solid #eee;

      h4{
        font-size: 1em;
      }
    }
  }

  .csf--sortable-content{
    width: 100%;
    display: table-cell;
    vertical-align: middle;
    background-color: #fff;

    > .csf-field{
      padding: 15px;
    }
  }

  .csf--sortable-helper{
    width: 100%;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    font-size: 14px;
    line-height: 1em;
    border-left: 1px solid #eee;
    background-color: #f7f7f7;

    .fa{
      display: inline-block;
      cursor: pointer;
      width: 50px;
      color: #555;

      &:hover{
        @include opacity(0.5);
      }
    }
  }

  .widget-placeholder{
    height: 50px;
    margin-bottom: 3px;
    border: 1px dashed #f1c40f;
    background-color: #fffae4;
  }

  .ui-sortable-helper{
    height: 50px !important;
    overflow: hidden !important;
    border-color: #ccc !important;
    background-color: #eee !important;
    @include opacity(0.5);

    .csf--sortable-helper,
    .csf--sortable-content{
      display: none;
    }
  }
}

/**
 * 03. 24. Field: sorter
 */
.csf-field-sorter{

  .ui-sortable-placeholder{
    height: 20px;
    border: 1px dashed #f1c40f;
    background-color: #fffae4;
  }

  .csf-modules{
    float: left;
    width: 50%;
    box-sizing: border-box;

    &:first-child{
      padding-right: 15px;
    }

    &:last-child{
      padding-left: 15px;
    }
  }

  .csf-disabled,
  .csf-enabled{
    padding: 5px 15px;
    border: 1px dashed #ddd;
    background-color: #fff;
  }

  .csf-disabled{
    li{
      @include transition(opacity 0.15s);
      @include opacity(0.5);
    }

    .ui-sortable-helper{
      @include opacity(1);
    }
  }

  .csf-sorter-title{
    font-size: 13px;
    font-weight: 600;
    padding: 10px;
    text-align: center;
    border: 1px dashed #ddd;
    border-bottom: none;
    background-color: #f8f8f8;
    text-transform: uppercase;
  }

  ul{
    list-style-type: none;
    margin: 0;
    padding: 0;
    min-height: 62px;

    li{
      margin: 10px 0;
      padding: 10px 15px;
      cursor: move;
      font-weight: bold;
      text-align: center;
      border: 1px solid #e5e5e5;
      background-color: #fafafa;
      @include transition(border-color 0.15s);

      &:hover{
        border-color: #bbb;
      }
    }
  }
}

/**
 * 03. 25. Field: spinner
 */
.csf-field-spinner{

  .csf--spin{
    float: left;
  }

  .csf--unit{
    float: right;
    height: 30px;
    line-height: 26px;
    text-align: center;
    border-left: none;
    color: #999;
    border: 1px solid #ddd;
    border-left: 0;
    background-color: #eee;
    padding: 0 6px;
    margin: 0;
    box-sizing: border-box;
  }

  .ui-spinner-button{
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin: 0;
    padding: 0;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border: 1px solid #ddd;
    color: #555;
    background-color: #eee;
    box-sizing: border-box;

    &:hover{
      background-color: #e7e7e7;
    }

    &:active{
      background-color: #ddd;
    }

    &:before{
      font-family: FontAwesome;
      font-size: 16px;
      line-height: 16px;
    }
  }

  .ui-spinner-down{
    float: left;
    border-right: 0;
    border-radius: 2px 0 0 2px;

    &:before{
      content: "\f0d9";
    }
  }

  .ui-spinner-up{
    float: right;
    border-left: 0;
    border-radius: 0 2px 2px 0;

    &:before{
      content: "\f0da";
    }
  }

  input{
    width: 50px;
    text-align: center;
    margin: 0;
    padding: 0 8px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #ddd;

    &:focus{
      outline: none;
    }
  }

  .ui-button-text{
    display: none;
  }
}

/**
 * 03. 26. Field: switcher
 */
.csf-field-switcher{

  .csf--switcher{
    float: left;
    cursor: pointer;
    position: relative;
    width: 60px;
    height: 26px;
    padding: 0;
    margin: 0;
    overflow: hidden;
    border-radius: 4px;
    background-color: #ed6f6f;
    user-select: none;
    -webkit-user-select: none;
  }

  .csf--ball{
    position: absolute;
    top: 4px;
    left: 4px;
    width: 24px;
    height: 18px;
    background-color: #fff;
    border-radius: 4px;
    transition: all .1s;
    box-shadow: 1px 1px 1px rgba(0,0,0,0.15);
  }

  .csf--on,
  .csf--off{
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    font-size: 11px;
    line-height: 26px;
    font-weight: 500;
    font-style: normal;
    text-align: center;
    text-transform: uppercase;
    color: #fff;
    padding-right: 28px;
    opacity: 0;
    transition: all .1s;
  }

  .csf--off{
    padding-right: 0;
    padding-left: 28px;
    opacity: 1;
  }

  .csf--active{
    background: #4fb845;

    .csf--on{
      opacity: 1;
    }

    .csf--off{
      opacity: 0;
    }

    .csf--ball{
      left: 100%;
      margin-left: -28px;
    }
  }

  .csf--label{
    float: left;
    margin-top: 4px;
    margin-left: 8px;
    font-weight: 400;
    color: #999;
  }

  input{
    display: none;
  }
}

/**
 * 03. 27. Field: tabbed
 */
.csf-field-tabbed{

  .csf-tabbed-section{
    border: 1px solid #eee;
    background: #fff;

    > .csf-field{
      padding: 15px;
    }
  }

  .csf-tabbed-nav{

    .csf--icon{
      padding-right: 5px;
    }

    a{
      display: inline-block;
      padding: 12px 15px;
      margin-top: 1px;
      margin-right: 5px;
      margin-bottom: -1px;
      position: relative;
      text-decoration: none;
      color: #444;
      font-weight: 600;
      background-color: #f3f3f3;
      border: 1px solid #eee;
      @include transition(all .2s);

      &:hover{
        background-color: #f9f9f9;
      }

      &.csf-tabbed-active{
        background-color: #fff;
        border-bottom-color: #fff;
      }

      &:focus{
        outline: none;
        @include box-shadow(none);
      }
    }
  }
}

/**
 * 03. 28. Field: text
 */
.csf-field-text{

  input{
    width: 340px;
    max-width: 100%;
  }
}

/**
 * 03. 29. Field: textarea
 */
.csf-field-textarea{

  textarea{
    width: 100%;
    max-width: 100%;
    min-height: 125px;
  }

  .csf-shortcode-button{
    margin-bottom: 10px;
    margin-right: 5px;
  }
}

/**
 * 03. 30. Field: typography
 */
.csf-field-typography{

  textarea,
  select{
    margin: 0;
    width: 100%;
  }

  .csf--title{
    color: #999;
    margin: 0 0 2px 0;

    small{
      vertical-align: top;
    }
  }

  .csf--blocks{
    display: flex;
    flex-wrap: wrap;
  }

  .csf--block{
    padding-right: 5px;
    padding-bottom: 10px;
    box-sizing: border-box;

    .csf--block{
      padding-right: 0;
      padding-bottom: 0;
    }
  }

  .csf--blocks-selects .csf--block{
    flex: 1 33.3%;
  }

  .csf--blocks-inputs .csf--block{
    flex: 1 25%;

    .csf--block{
      flex: 1;
    }
  }

  .csf--input{
    margin: 0;
    width: 100%;
    padding: 5px 5px;
  }

  .csf--unit{
    min-width: 40px;
    max-width: 40px;
    padding: 4px 0;
    font-size: 12px;
    font-weight: 400;
    text-align: center;
    color: #777;
    border: 1px solid #ddd;
    background-color: #eee;
    border-radius: 0 2px 2px 0;
    border-left: 0;
  }

  .csf--preview{
    font-size: 16px;
    line-height: 20px;
    padding: 20px;
    color: #222;
    border: 1px solid #eee;
    background-color: #fff;
    border-radius: 2.5px;
    user-select: none;
    -webkit-user-select: none;
    transition: background-color .2s, border-color .2s;
  }

  .csf--block-preview{
    cursor: pointer;
    position: relative;
    overflow: hidden;
    margin-top: 10px;
    max-width: 100%;
  }

  .csf--black-background{
    border-color: #000;
    background-color: #000;
  }

  .csf--toggle{
    position: absolute;
    top: 5px;
    right: 10px;
    color: #999;
  }

  .csf--block-extra-styles{
    margin-top: 5px;
  }
}

/**
 * 03. 31. Field: upload
 */
.csf-field-upload{

  input{
    width: 100%;
  }

  .csf--wrap{
    display: flex;
  }

  .csf--buttons{
    display: flex;
    margin-left: 5px;
  }

  .csf--remove{
    margin-left: 5px;
  }
}

/**
 * 03. 32. Field: wp_editor
 */
.csf-field-wp_editor{

  .csf-wp-editor{
    float: left;
    width: 100%;
  }

  .mce-toolbar-grp{
    border: none;
  }

  .mce-btn.mce-active button,
  .mce-btn.mce-active:hover button,
  .mce-btn.mce-active i,
  .mce-btn.mce-active:hover i{
    color: #23282d;
  }

  .wp-media-buttons{
    position: relative;
    z-index: 2;
  }


  .wp-editor-tabs{
    position: relative;
    z-index: 1;
  }

  .csf-no-tinymce{
    border: 1px solid #e5e5e5;
  }

  .csf-no-quicktags{

    .wp-media-buttons{
      float: none;
      display: block;
    }

    .mce-tinymce{
      box-shadow: none;
      border: 1px solid #e5e5e5;
    }
  }

  textarea{
    width: 100%;
    max-width: 100%;
    margin: 0;
    box-shadow: none;
  }
}

/**
 * 03. 33. Field: heading
 */
.csf-field-heading{
  font-size: 1.5em;
  font-weight: bold;
  color: #23282d;
  background-color: #f5f5f5;
}

/**
 * 03. 34. Field: subheading
 */
.csf-field-subheading{
  font-size: 14px;
  font-weight: bold;
  padding-top: 17px;
  padding-bottom: 17px;
  color: #23282d;
  background-color: #f7f7f7;
}

/**
 * 03. 35. Field: submessage
 */
.csf-field-submessage{
  padding: 0 !important;
  border: 0 !important;

  + .csf-field{
    border-top: 0 !important;
  }
}

.csf-submessage{
  font-size: 12px;
  padding: 17px 30px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
}

.csf-submessage-success{
  color: #3c763d;
  border-color: #d6e9c6;
  background-color: #dff0d8;
}

.csf-submessage-info{
  color: #31708f;
  border-color: #bce8f1;
  background-color: #d9edf7;
}

.csf-submessage-warning{
  color: #8a6d3b;
  border-color: #faebcc;
  background-color: #fcf8e3;
}

.csf-submessage-danger{
  color: #a94442;
  border-color: #ebccd1;
  background-color: #f2dede;
}

.csf-submessage-normal{
  color: #23282d;
  border-color: #eee;
  background-color: #f7f7f7;
}

/**
 * 03. 36. Field: notice
 */
.csf-field-notice{
  background-color: #f7f7f7;
}

.csf-notice{
  padding: 12px;
  background-color: #fff;
  border-left-style: solid;
  border-left-width: 4px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.csf-notice-success{
  border-color: #46b450;
}

.csf-notice-info{
  border-color: #339fd4;
}

.csf-notice-warning{
  border-color: #ffbc00;
}

.csf-notice-danger{
  border-color: #dc3232;
}

.csf-notice-normal{
  border-color: #222;
}

/**
 * 03. 37. others
 */
.csf-help{
  cursor: help;
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px;
  font-size: 14px;
  color: #aaa;

  .csf-help-text{
    display: none;
  }
}

.csf-image-preview{
  display: inline-block;
  position: relative;
  padding: 4px;
  min-width: 44px;
  min-height: 22px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  background-color: #f9f9f9;
  @include box-shadow(0 1px 0 rgba(black, 0.08));

  img{
    max-height: 90px;
    display: inline-block;
    vertical-align: middle;
  }
}

.csf-field-custom{

  .csf-field{
    padding: 0;
  }
}

.csf-field{

  .chosen-container-single .chosen-single{
    height: 28px;
    line-height: 26px;
  }

  .chosen-container-single .chosen-single abbr{
    top: 0;
    right: 20px;
    font-family: FontAwesome;
    font-size: 12px;
    height: 100%;
    width: 18px;
    color: #aaa;
    text-align: center;
    background: none;

    &:before{
      content: "\f00d";
    }

    &:hover{
      color: #555;
    }
  }

  .chosen-container-multi .chosen-choices li.search-choice .search-choice-close{
    font-family: FontAwesome;
    font-size: 12px;
    height: 100%;
    width: 18px;
    color: #aaa;
    text-align: center;
    background: none;

    &:before{
      content: "\f00d";
    }

    &:hover{
      color: #555;
    }
  }

  .chosen-container-single .chosen-single div b{
    font-family: FontAwesome;
    font-size: 14px;
    color: #aaa;
    background: none;

    &:before{
      content: "\f107";
    }

    &:hover{
      color: #555;
    }
  }

  .chosen-container-active.chosen-with-drop .chosen-single div b{

    &:before{
      content: "\f106";
    }
  }

  .chosen-container-single .chosen-single-with-deselect span{
    margin-right: 40px;
  }

  .chosen-container-single .chosen-search input[type="text"]{
    background: none;
  }

  .chosen-container-single .chosen-search{

    &:before{
      font-family: FontAwesome;
      position: absolute;
      content: "\f002";
      font-size: 11px;
      right: 10px;
      top: 7px;
      color: #aaa;
    }
  }

  .wp-picker-container{
    display: inline-block;
  }

  .csf--transparent-wrap{
    display: none;
    position: relative;
    top: -1px;
    width: 235px;
    padding: 9px 10px;
    border: 1px solid #dfdfdf;
    border-top: none;
    background-color: #fff;
  }

  .csf--transparent-slider{
    position: absolute;
    width: 190px;
    margin-left: 2px;
    height: 18px;

    .ui-slider-handle{
      position: absolute;
      top: -3px;
      bottom: -3px;
      z-index: 5;
      border-color: #aaa;
      border-style: solid;
      border-width: 4px 3px;
      width: 10px;
      height: 16px;
      margin: 0 -5px;
      background: none;
      cursor: ew-resize;
      @include opacity(0.9);
      @include border-radius(4px);
      @include box-shadow(0 1px 2px rgba(black, 0.2));

      &:before{
        content: " ";
        position: absolute;
        left: -2px;
        right: -2px;
        top: -3px;
        bottom: -3px;
        border: 2px solid #fff;
        @include border-radius(3px);
      }
    }
  }

  .csf--transparent-offset{
    height: 18px;
    width: 200px;
    background: url(../images/checkerboard.png) repeat-y center left scroll #fff;
    @include box-shadow(0 0 5px rgba(black, 0.4) inset);
    @include border-radius(2px);
  }

  .csf--transparent-text{
    position: absolute;
    top: 12px;
    right: 10px;
    width: 30px;
    font-size: 12px;
    line-height: 12px;
    text-align: center;
    color: #999;
  }

  .csf--transparent-button{
    display: inline-block;
    text-align: center;
    cursor: pointer;
    margin-top: 10px;
    user-select: none;
    -webkit-user-select: none;
    color: #999;
    transition: background-color .2s, border-color .2s, color .2s;
  }

  .csf--transparent-active{

    .wp-color-result{
      background-image: url(../images/checkerboard.png);
      background-size: 130%;
      background-position: center left;
      background-color: transparent !important;
    }

    .csf--transparent-button{
      color: #fff;
      border-color: #4fb845;
      background-color: #4fb845;
    }
  }
}
