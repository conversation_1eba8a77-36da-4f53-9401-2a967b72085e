/**
 * 08. Helper
 */
.csf-text-desc,
.csf-text-subtitle{
  font-weight: 400;
  margin-top: 10px;
  color: #999;
}

.csf-text-success{
  color: #2b542c;
}

.csf-text-error{
  color: #d02c21;
}

.csf-text-info{
  color: #31708f;
}

.csf-text-warning{
  color: #ffb900;
}

.csf-text-muted{
  color: #aaa;
}

.csf-text-left{
  text-align: left;
}

.csf-text-center{
  text-align: center;
}

.csf-text-right{
  text-align: right;
}

.csf-block-left{
  float: left;
}

.csf-block-right{
  float: right;
}

.csf-full-width{
  width: 100%;
}

.csf-full-half{
  width: 50%;
}

.csf-table{
  width: 100%;
  display: table;
}

.csf-table-cell{
  display: table-cell;
  vertical-align: middle;
}

.csf-table-expanded{
  width: 100%;
}

.csf-nowrap{
  white-space: nowrap;
}

.csf-text-highlight{
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  @include border-radius(2px);
}

.csf-text-highlight-gray{
  padding: 2px 4px;
  font-size: 90%;
  background-color: #f0f0f0;
  @include border-radius(2px);
}

.csf-hidden{
  display: none;
}

.csf-hide{
  display: none !important;
}

.csf-show{
  display: block !important;
}

.csf-opacity{
  opacity: 0.5;
}

.csf-warning-primary{
  color: #fff !important;
  border-color: #d02c21 #ba281e #ba281e !important;
  background: #e14d43 !important;
  @include box-shadow(0 1px 0 #ba281e !important);
  @include text-shadow(0 -1px 1px #ba281e, 1px 0 1px #ba281e, 0 1px 1px #ba281e, -1px 0 1px #ba281e !important);

  &:hover,
  &:focus{
    border-color: #ba281e !important;
    background: #e35950 !important;
    @include box-shadow(0 1px 0 #ba281e !important);
  }

  &:active{
    border-color: #ba281e !important;
    background: #d02c21 !important;
    @include box-shadow(inset 0 2px 0 #ba281e !important);
  }
}

.csf-form-result{
  display: none;
  padding: 12px;
  margin: 0 0 15px 0;
  background-color: #fff;
  border-left: 4px solid #555;
  @include box-shadow(0 1px 1px rgba(black, 0.1));
}

.csf-form-show{
  display: block;
}

.csf-form-error{
  border-left-color: #dc3232;
}

.csf-form-success{
  border-left-color: #46b450;
}

.csf-form-warning{
  border-left-color: #ffb900;
}

.csf-form-info{
  border-left-color: #00a0d2;
}

.csf-label-error{
  position: relative;
  top: -2px;
  display: inline-block;
  font-size: 10px;
  line-height: 10px;
  height: 10px;
  width: 10px;
  padding: 1px;
  font-style: normal;
  text-align: center;
  color: #fff;
  vertical-align: middle;
  background-color: #e10000;
  @include border-radius(2px);
}

.csf-no-option{
  padding: 30px;
}
