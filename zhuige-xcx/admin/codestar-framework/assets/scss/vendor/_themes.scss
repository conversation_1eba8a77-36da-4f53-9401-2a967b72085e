/**
 * 02. Themes
 */

/**
 * 02. 01. Theme Dark
 */
.csf-theme-dark{

  .csf-header-inner{
    background-color: #050505;

    h1{
      color: #fff;

      small{
        color: #555;
      }
    }
  }

  .csf-expand-all{
    color: #999;
    background-color: #222;

    &:hover{
      color: #fff;
      background-color: #333;
    }
  }

  .csf-search{

    input{
      color: #fff;
      background-color: #222;
    }

    &:focus{
      background-color: #444;
    }

    &::-webkit-input-placeholder{
      color: #666;
    }
  }

  .csf-nav{

    ul{

      li{

        a{
          color: #999;
          background-color: #222;
          border-bottom: 1px solid #2f2f2f;

          &:hover{
            color: #fff;
          }
        }

        .csf-section-active{
          color: #fff;
          background-color: #111;
        }
      }

      ul{

        li{

          a{
            background-color: #191919;
            border-bottom: 1px solid #2f2f2f;
          }

          .csf-section-active{
            background-color: #101010;
          }
        }

        &:before{
          background-color: rgba(#222, 0.75);
        }
      }
    }

    > ul > li:last-child > a{
      border-bottom: none;
    }
  }

  .csf-nav-background{
    background-color: #222;
  }

  .csf-footer{
    color: #555;
    background-color: #050505;
  }
}

/**
 * 02. 02. Theme Light
 */
.csf-theme-light{

  .csf-container{
    border: 1px solid #e5e5e5;
    @include box-shadow(0 0 15px rgba(black, 0.04));
  }

  .csf-header-inner{
    border-bottom: 1px solid #e5e5e5;
    background-color: #f5f5f5;
    background: linear-gradient(#fefefe, #f5f5f5);

    h1{

      small{
        color: #999;
      }
    }
  }

  .csf-expand-all{
    color: #999;
    background-color: #fff;
    @include box-shadow(0 1px 1px rgba(black, 0.05));

    &:hover{
      color: #555;
    }
  }

  .csf-search{

    input{
      color: #555;
      background-color: #fff;
      @include box-shadow(0px 1px 1px rgba(black, 0.05));

      &::-webkit-input-placeholder{
        color: #bbb;
      }
    }
  }

  .csf-nav{

    ul{

      li{

        a{
          color: #666;
          background-color: #f5f5f5;
          border-bottom: 1px solid #e5e5e5;

          &:hover{
            color: #222;
          }
        }

        .csf-section-active{
          color: #222;
          background-color: #fff;

          &:after{
            display: none;
          }
        }
      }

      ul{

        li{

          a{
            background-color: #eee;
            border-bottom: 1px solid #e5e5e5;
          }
        }
      }
    }

    > ul > li:last-child > a{
      border-bottom: none;
    }
  }

  .csf-nav-background{
    background-color: #f5f5f5;
  }

  .csf-footer{
    color: #555;
    border-top: 1px solid #e5e5e5;
    background-color: #f5f5f5;
    background: linear-gradient(#fafafa, #f5f5f5);
  }
}
