# Copyright (C) 2021 Codestar
# This file is distributed under the same license as the Codestar Framework package.
msgid ""
msgstr ""
"Project-Id-Version: Codestar Framework 2.2.1\n"
"Report-Msgid-Bugs-To: "
"https://wordpress.org/support/plugin/codestar-framework\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: classes/admin-options.class.php:225
msgid "Error while saving the changes."
msgstr ""

#: classes/admin-options.class.php:285
msgid "Settings successfully imported."
msgstr ""

#: classes/admin-options.class.php:297 classes/admin-options.class.php:313
msgid "Default settings restored."
msgstr ""

#: classes/admin-options.class.php:384
msgid "Settings saved."
msgstr ""

#: classes/admin-options.class.php:563
msgid "You have unsaved changes, save your changes!"
msgstr ""

#: classes/admin-options.class.php:565
msgid "show all settings"
msgstr ""

#: classes/admin-options.class.php:567 fields/icon/icon.php:57
#: fields/map/map.php:23
msgid "Search..."
msgstr ""

#: classes/admin-options.class.php:570 classes/admin-options.class.php:693
msgid "Save"
msgstr ""

#: classes/admin-options.class.php:570 classes/admin-options.class.php:693
msgid "Saving..."
msgstr ""

#: classes/admin-options.class.php:571 classes/admin-options.class.php:694
msgid "Reset Section"
msgstr ""

#: classes/admin-options.class.php:571 classes/admin-options.class.php:694
msgid "Are you sure to reset this section options?"
msgstr ""

#: classes/admin-options.class.php:572 classes/admin-options.class.php:695
msgid "Reset All"
msgstr ""

#: classes/admin-options.class.php:572 classes/admin-options.class.php:695
#: classes/comment-options.class.php:215 classes/metabox-options.class.php:293
#: fields/backup/backup.php:31
msgid "Reset"
msgstr ""

#: classes/admin-options.class.php:572 classes/admin-options.class.php:695
msgid "Are you sure you want to reset all settings to default values?"
msgstr ""

#: classes/admin-options.class.php:670 classes/comment-options.class.php:198
#: classes/metabox-options.class.php:276 fields/button_set/button_set.php:56
#: fields/checkbox/checkbox.php:76 fields/radio/radio.php:75
#: fields/select/select.php:113 functions/actions.php:41
msgid "No data available."
msgstr ""

#: classes/comment-options.class.php:216 classes/metabox-options.class.php:294
msgid "update post"
msgstr ""

#: classes/comment-options.class.php:216 classes/metabox-options.class.php:294
msgid "Cancel"
msgstr ""

#: classes/setup.class.php:529
msgid "Are you sure?"
msgstr ""

#: classes/setup.class.php:530
msgid "Please enter %s or more characters"
msgstr ""

#: classes/setup.class.php:531
msgid "Searching..."
msgstr ""

#: classes/setup.class.php:532
msgid "No results found."
msgstr ""

#: classes/setup.class.php:634
msgid "Oops! Not allowed."
msgstr ""

#: classes/setup.class.php:708 classes/setup.class.php:712
msgid "Field not found!"
msgstr ""

#: classes/shortcode-options.class.php:251 fields/group/group.php:23
msgid "Add New"
msgstr ""

#: classes/shortcode-options.class.php:288 functions/actions.php:16
#: functions/actions.php:68 functions/actions.php:106 functions/actions.php:141
#: functions/actions.php:170
msgid "Error: Invalid nonce verification."
msgstr ""

#: fields/background/background.php:36 fields/media/media.php:57
msgid "Not selected"
msgstr ""

#: fields/background/background.php:72 fields/date/date.php:31
msgid "From"
msgstr ""

#: fields/background/background.php:90 fields/date/date.php:32
msgid "To"
msgstr ""

#: fields/background/background.php:108
msgid "Direction"
msgstr ""

#: fields/background/background.php:114
msgid "Gradient Direction"
msgstr ""

#: fields/background/background.php:115
msgid "&#8659; top to bottom"
msgstr ""

#: fields/background/background.php:116
msgid "&#8658; left to right"
msgstr ""

#: fields/background/background.php:117
msgid "&#8664; corner top to right"
msgstr ""

#: fields/background/background.php:118
msgid "&#8665; corner top to left"
msgstr ""

#: fields/background/background.php:161
msgid "Background Position"
msgstr ""

#: fields/background/background.php:162
msgid "Left Top"
msgstr ""

#: fields/background/background.php:163
msgid "Left Center"
msgstr ""

#: fields/background/background.php:164
msgid "Left Bottom"
msgstr ""

#: fields/background/background.php:165
msgid "Center Top"
msgstr ""

#: fields/background/background.php:166
msgid "Center Center"
msgstr ""

#: fields/background/background.php:167
msgid "Center Bottom"
msgstr ""

#: fields/background/background.php:168
msgid "Right Top"
msgstr ""

#: fields/background/background.php:169
msgid "Right Center"
msgstr ""

#: fields/background/background.php:170
msgid "Right Bottom"
msgstr ""

#: fields/background/background.php:184
msgid "Background Repeat"
msgstr ""

#: fields/background/background.php:185
msgid "Repeat"
msgstr ""

#: fields/background/background.php:186
msgid "No Repeat"
msgstr ""

#: fields/background/background.php:187
msgid "Repeat Horizontally"
msgstr ""

#: fields/background/background.php:188
msgid "Repeat Vertically"
msgstr ""

#: fields/background/background.php:202
msgid "Background Attachment"
msgstr ""

#: fields/background/background.php:203
msgid "Scroll"
msgstr ""

#: fields/background/background.php:204
msgid "Fixed"
msgstr ""

#: fields/background/background.php:218
msgid "Background Size"
msgstr ""

#: fields/background/background.php:219
msgid "Cover"
msgstr ""

#: fields/background/background.php:220
msgid "Contain"
msgstr ""

#: fields/background/background.php:221
msgid "Auto"
msgstr ""

#: fields/background/background.php:235
msgid "Background Origin"
msgstr ""

#: fields/background/background.php:236 fields/background/background.php:254
msgid "Padding Box"
msgstr ""

#: fields/background/background.php:237 fields/background/background.php:253
msgid "Border Box"
msgstr ""

#: fields/background/background.php:238 fields/background/background.php:255
msgid "Content Box"
msgstr ""

#: fields/background/background.php:252
msgid "Background Clip"
msgstr ""

#: fields/background/background.php:269
msgid "Background Blend Mode"
msgstr ""

#: fields/background/background.php:270 fields/link_color/link_color.php:36
#: fields/typography/typography.php:186
msgid "Normal"
msgstr ""

#: fields/background/background.php:271
msgid "Multiply"
msgstr ""

#: fields/background/background.php:272
msgid "Screen"
msgstr ""

#: fields/background/background.php:273
msgid "Overlay"
msgstr ""

#: fields/background/background.php:274
msgid "Darken"
msgstr ""

#: fields/background/background.php:275
msgid "Lighten"
msgstr ""

#: fields/background/background.php:276
msgid "Color Dodge"
msgstr ""

#: fields/background/background.php:277
msgid "Saturation"
msgstr ""

#: fields/background/background.php:278
msgid "Color"
msgstr ""

#: fields/background/background.php:279
msgid "Luminosity"
msgstr ""

#: fields/backup/backup.php:26
msgid "Import"
msgstr ""

#: fields/backup/backup.php:29
msgid "Export & Download"
msgstr ""

#: fields/border/border.php:25 fields/spacing/spacing.php:25
msgid "top"
msgstr ""

#: fields/border/border.php:26 fields/spacing/spacing.php:26
msgid "right"
msgstr ""

#: fields/border/border.php:27 fields/spacing/spacing.php:27
msgid "bottom"
msgstr ""

#: fields/border/border.php:28 fields/spacing/spacing.php:28
msgid "left"
msgstr ""

#: fields/border/border.php:29 fields/spacing/spacing.php:29
msgid "all"
msgstr ""

#: fields/border/border.php:51 fields/typography/typography.php:214
msgid "Solid"
msgstr ""

#: fields/border/border.php:52 fields/typography/typography.php:217
msgid "Dashed"
msgstr ""

#: fields/border/border.php:53 fields/typography/typography.php:216
msgid "Dotted"
msgstr ""

#: fields/border/border.php:54 fields/typography/typography.php:215
msgid "Double"
msgstr ""

#: fields/border/border.php:55
msgid "Inset"
msgstr ""

#: fields/border/border.php:56
msgid "Outset"
msgstr ""

#: fields/border/border.php:57
msgid "Groove"
msgstr ""

#: fields/border/border.php:58
msgid "ridge"
msgstr ""

#: fields/border/border.php:59 fields/typography/typography.php:199
#: fields/typography/typography.php:213
msgid "None"
msgstr ""

#: fields/dimensions/dimensions.php:22
msgid "width"
msgstr ""

#: fields/dimensions/dimensions.php:23
msgid "height"
msgstr ""

#: fields/gallery/gallery.php:20
msgid "Add Gallery"
msgstr ""

#: fields/gallery/gallery.php:21
msgid "Edit Gallery"
msgstr ""

#: fields/gallery/gallery.php:22
msgid "Clear"
msgstr ""

#: fields/group/group.php:35 fields/repeater/repeater.php:27
msgid "Error: Field ID conflict."
msgstr ""

#: fields/group/group.php:46 fields/group/group.php:87
#: fields/repeater/repeater.php:48 fields/repeater/repeater.php:76
msgid "Are you sure to delete this item?"
msgstr ""

#: fields/group/group.php:121 fields/repeater/repeater.php:89
msgid "You cannot add more."
msgstr ""

#: fields/group/group.php:122 fields/repeater/repeater.php:90
msgid "You cannot remove more."
msgstr ""

#: fields/icon/icon.php:20 fields/icon/icon.php:53
msgid "Add Icon"
msgstr ""

#: fields/icon/icon.php:21
msgid "Remove Icon"
msgstr ""

#: fields/link/link.php:20
msgid "Add Link"
msgstr ""

#: fields/link/link.php:21
msgid "Edit Link"
msgstr ""

#: fields/link/link.php:22
msgid "Remove Link"
msgstr ""

#: fields/link_color/link_color.php:37
msgid "Hover"
msgstr ""

#: fields/link_color/link_color.php:38
msgid "Active"
msgstr ""

#: fields/link_color/link_color.php:39
msgid "Visited"
msgstr ""

#: fields/link_color/link_color.php:40
msgid "Focus"
msgstr ""

#: fields/map/map.php:24
msgid "Latitude"
msgstr ""

#: fields/map/map.php:25
msgid "Longitude"
msgstr ""

#: fields/media/media.php:23 fields/upload/upload.php:21
msgid "Upload"
msgstr ""

#: fields/media/media.php:24 fields/upload/upload.php:22
msgid "Remove"
msgstr ""

#: fields/sorter/sorter.php:21
msgid "Enabled"
msgstr ""

#: fields/sorter/sorter.php:22
msgid "Disabled"
msgstr ""

#: fields/switcher/switcher.php:20
msgid "On"
msgstr ""

#: fields/switcher/switcher.php:21
msgid "Off"
msgstr ""

#: fields/typography/typography.php:96
msgid "Font Family"
msgstr ""

#: fields/typography/typography.php:97
msgid "Select a font"
msgstr ""

#: fields/typography/typography.php:105
msgid "Backup Font Family"
msgstr ""

#: fields/typography/typography.php:119 fields/typography/typography.php:132
#: fields/typography/typography.php:145 fields/typography/typography.php:160
#: fields/typography/typography.php:176 fields/typography/typography.php:189
#: fields/typography/typography.php:203 fields/typography/typography.php:221
msgid "Default"
msgstr ""

#: fields/typography/typography.php:130
msgid "Font Style"
msgstr ""

#: fields/typography/typography.php:144 fields/typography/typography.php:145
msgid "Load Extra Styles"
msgstr ""

#: fields/typography/typography.php:158
msgid "Subset"
msgstr ""

#: fields/typography/typography.php:168
msgid "Text Align"
msgstr ""

#: fields/typography/typography.php:170
msgid "Inherit"
msgstr ""

#: fields/typography/typography.php:171
msgid "Left"
msgstr ""

#: fields/typography/typography.php:172
msgid "Center"
msgstr ""

#: fields/typography/typography.php:173
msgid "Right"
msgstr ""

#: fields/typography/typography.php:174
msgid "Justify"
msgstr ""

#: fields/typography/typography.php:175
msgid "Initial"
msgstr ""

#: fields/typography/typography.php:184
msgid "Font Variant"
msgstr ""

#: fields/typography/typography.php:187
msgid "Small Caps"
msgstr ""

#: fields/typography/typography.php:188
msgid "All Small Caps"
msgstr ""

#: fields/typography/typography.php:197
msgid "Text Transform"
msgstr ""

#: fields/typography/typography.php:200
msgid "Capitalize"
msgstr ""

#: fields/typography/typography.php:201
msgid "Uppercase"
msgstr ""

#: fields/typography/typography.php:202
msgid "Lowercase"
msgstr ""

#: fields/typography/typography.php:211
msgid "Text Decoration"
msgstr ""

#: fields/typography/typography.php:218
msgid "Wavy"
msgstr ""

#: fields/typography/typography.php:219
msgid "Overline"
msgstr ""

#: fields/typography/typography.php:220
msgid "Line-through"
msgstr ""

#: fields/typography/typography.php:233
msgid "Font Size"
msgstr ""

#: fields/typography/typography.php:245
msgid "Line Height"
msgstr ""

#: fields/typography/typography.php:257
msgid "Letter Spacing"
msgstr ""

#: fields/typography/typography.php:269
msgid "Word Spacing"
msgstr ""

#: fields/typography/typography.php:284
msgid "Font Color"
msgstr ""

#: fields/typography/typography.php:295
msgid "Custom Style"
msgstr ""

#: fields/typography/typography.php:362
msgid "Custom Web Fonts"
msgstr ""

#: fields/typography/typography.php:368
msgid "Safe Web Fonts"
msgstr ""

#: fields/typography/typography.php:388
msgid "Google Web Fonts"
msgstr ""

#: functions/actions.php:72 functions/actions.php:110
msgid "Error: Invalid key."
msgstr ""

#: functions/actions.php:114
msgid "Error: The response is not a valid JSON response."
msgstr ""

#: functions/actions.php:174
msgid "Error: Invalid term ID."
msgstr ""

#: functions/actions.php:180
msgid "Error: You do not have permission to do that."
msgstr ""

#: functions/validate.php:14 functions/validate.php:86
msgid "Please enter a valid email address."
msgstr ""

#: functions/validate.php:32 functions/validate.php:106
msgid "Please enter a valid number."
msgstr ""

#: functions/validate.php:50 functions/validate.php:126
msgid "This field is required."
msgstr ""

#: functions/validate.php:68 functions/validate.php:146
msgid "Please enter a valid URL."
msgstr ""
