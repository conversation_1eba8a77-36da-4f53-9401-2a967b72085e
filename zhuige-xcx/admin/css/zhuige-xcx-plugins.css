/**
 * All of the CSS for your public-facing functionality should be
 * included in this file.
 */
body {
    margin: 0;
}

.zhuige-market {
    padding: 20px;
    background: #f0f0f1;
}

.zhuige-market a {
    text-decoration: none;
    color: #010101;
}

.zhuige-market a:hover {
    opacity: 0.7;
    transition: .4s;
}

.zhuige-market a:focus {
    outline-style: none;
    -moz-outline-style: none;
}

.zhuige-market em,
.zhuige-market cite {
    font-style: normal;
}

.zhuige-market img {
    vertical-align: top;
    border: none
}

.zhuige-market ul {
    margin: 0;
    padding: 0;
}

.zhuige-market li {
    list-style: none;
}

.zhuige-market-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0 24px;
}

.zhuige-market-nav h1 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.zhuige-market-nav ul {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
}

.zhuige-market-nav li {
    padding: 4px 10px;
}

.zhuige-market-nav li a {
    font-size: 14px;
    font-weight: 400;
}

.zhuige-market-nav li.activ a {
    font-weight: 600;
}

.zhuige-market-type {
    background: #FFFFFF;
    border-radius: 6px;
    padding: 10px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
}

.zhuige-market-type ul {
    display: flex;
    align-items: center;
}

.zhuige-market-type ul li {
    padding: 0 20px;
    margin: 0;
}

.zhuige-market-type ul li a {
    font-size: 14px;
    font-weight: 400;
}

.zhuige-market-type ul li.activ a {
    color: #FF6146;
    font-weight: 500;
}

.zhuige-market-ad {
    display: flex;
    align-items: center;
}

.zhuige-market-ad ol {
    margin: 0 8px;
    padding: 0;
    width: 360px;
}

.zhuige-market-ad ol li {
    width: 360px;
    overflow: hidden;
    height: 24px;
    line-height: 24px;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 12px;
    font-weight: 400;
}

.zhuige-market-ad .dashicons-before:before {
    color: #999999;
}

.zhuige-market-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
}

.zhuige-market-view {
    margin-bottom: 20px;
    background: #FFFFFF;
    border-radius: 6px;
    width: 49.2%;
}

.zhuige-market-bg {
    padding: 16px;
    display: flex;
    flex-wrap: nowrap;
}

.zhuige-market-cover {
    height: 108px;
    width: 160px;
    margin-right: 10px;
}

.zhuige-market-cover a {
    display: block;
    height: 108px;
    width: 160px;
    overflow: hidden;
    border-radius: 6px;
    position: relative;
}

.zhuige-market-cover cite {
    position: absolute;
    bottom: 0;
    z-index: 3;
    height: 3em;
    line-height: 3em;
    font-size: 12px;
    font-weight: 400;
    color: #FFFFFF;
    background: linear-gradient(to bottom, rgba(255, 97, 70, 1), rgba(255, 97, 70, 1));
    width: 160px;
    text-align: center;
}

.zhuige-market-cover strong {
    position: absolute;
    top: 6px;
    left: 6px;
    z-index: 3;
    height: 2em;
    line-height: 2em;
    font-size: 12px;
    font-weight: 400;
    color: #FFFFFF;
    background: linear-gradient(to bottom, rgba(33, 33, 33, 1), rgba(33, 33, 33, 1));
    padding: 0 12px;
    border-radius: 3px;
}


.zhuige-market-cover a img {
    object-fit: cover;
    height: 110%;
    width: 110%;
    margin-top: -5%;
    margin-left: -5%;
}

.zhuige-market-info {
    display: flex;
    flex-wrap: nowrap;
    overflow: hidden;
    width: 76%;
}

.zhuige-market-text {
    padding-top: 10px;
    overflow: hidden;
    width: 86%;
}

.zhuige-market-text div:nth-child(1) {
    display: flex;
    align-items: center;
    padding-bottom: 10px;
    flex-wrap: wrap;
}

.zhuige-market-text div:nth-child(1) span {
    height: 20px;
    line-height: 20px;
    font-size: 11px;
    font-weight: 400;
    padding: 0 6px;
    color: #363B51;
    background: #f5f5f5;
    margin-right: 6px;
    border-radius: 2px;
    white-space: nowrap;
}

.zhuige-market-text div:nth-child(1) a.market-title {
    font-size: 16px;
    font-weight: 600;
    color: #010101;
    padding: 0;
    background: none;
    margin-right: 6px;
}

.zhuige-market-text div:nth-child(1) a.hot {
    color: #FF6146;
    background: #ffeae3;
}

.zhuige-market-text div:nth-child(2) {
    height: 1.6rem;
    line-height: 1.6rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-bottom: 8px;
    font-size: 13px;
    font-weight: 400;
    width: 100%;
}

.zhuige-market-text div:nth-child(3) {
    display: flex;
    align-items: baseline;
}

.zhuige-market-text div:nth-child(3) * {
    font-size: 12px;
    font-weight: 400;
}

.zhuige-market-text div:nth-child(3) text,
.zhuige-market-text div:nth-child(3) strong,
.zhuige-market-text div:nth-child(3) em {
    color: #FF6146;
    margin-right: 2px;
}

.zhuige-market-text div:nth-child(3) strong {
    font-size: 20px;
    font-weight: 600;
    margin-right: 12px;
}

.zhuige-market-text div:nth-child(3) cite {
    text-decoration: line-through;
    color: #999999;
}

.zhuige-market-text div:nth-child(3) a {
    height: 20px;
    line-height: 20px;
    padding: 0 4px;
    border-radius: 4px;
    margin-right: 4px;
    color: #FFFFFF;
    background: #363b51;
    opacity: 0.7;
    word-break: keep-all;
}
.zhuige-market-text div:nth-child(3) a:hover {
    opacity: 1;
    transition: .4s;
}

.zhuige-market-text div:nth-child(3) a.unpack {
    background: #FF6146;
    opacity: 1;
}
.zhuige-market-text div:nth-child(3) a.unpack:hover {
    opacity: 0.7;
}

.zhuige-market-text div:nth-child(3) a.closed {
    background: #EEEEEE;
    color: #999999;
}

.zhuige-market-text div:nth-child(3) a.btn-zhuige-xcx-plugins-market-update {
    background: #1E9FFF;
    opacity: 1;
}
.zhuige-market-text div:nth-child(3) a.btn-zhuige-xcx-plugins-market-update:hover {
    opacity: 0.7;
}





.zhuige-market-btn {
    padding-left: 12px;
}

.zhuige-market-btn text {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    white-space: nowrap;
}

.zhuige-market-btn a {
    white-space: nowrap;
    display: block;
    font-size: 12px;
    font-weight: 400;
    width: 60px;
    height: 26px;
    line-height: 26px;
    text-align: center;
    color: #555555;
    margin-top: 52px;
    border-radius: 4px;
    background: #EEEEEE;
    color: #999999;
}

.zhuige-market-btn a.update {
    background: #1E9FFF;
    color: #FFFFFF;
}
.zhuige-market-btn a.setup {
    background: #FF6146;
    color: #FFFFFF;
}
.zhuige-market-mask {
    position: fixed;
    z-index: 99;
    background: rgba(0, 0, 0, .6);
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    margin-left: -20px;
}

.zhuige-market-pop {
    width: 400px;
    background: #FFFFFF;
    border-radius: 8px;
    padding: 30px 40px;
    margin-left: 30%;
}

.zhuige-market-pop-title {
    padding: 0 20px 10px 54px;
    display: flex;
    align-items: center;
}

.zhuige-market-pop-title div:nth-child(1) {
    margin-left: -54px;
    padding-right: 8px;
}

.zhuige-market-pop-title div img {
    height: 48px;
    width: 48px;
    border-radius: 50%;
}

.zhuige-market-pop-title div h2 {
    font-size: 18px;
    margin: 0 0 4px;
}

.zhuige-market-pop-title div p {
    font-size: 2px;
    margin: 0;
    color: #666666;
}

.zhuige-market-pop-form {
    padding: 0;
}

.zhuige-market-pop-form p {
    padding: 6px 0;
    margin: 0;
}

.zhuige-market-pop-form input {
    height: 48px;
    line-height: 48px;
    padding: 0;
    width: 100%;
    border: 1px solid #999999;
    border-radius: 4px;
    background: #f5f5f5;
    font-size: 14px;
    font-weight: 400;
    text-indent: 8px;
}

.zhuige-market-pop-form p a, .zhuige-market-pop-form p span {
    font-size: 12px;
    color: #999999;
    text-decoration: none;
}
.zhuige-market-pop-form p a.market-tip {
    color: #FF6146;
}
.zhuige-market-pop-form p em {
    font-size: 12px;
    font-style: normal;
    color: #999999;
    margin: 0 8px;
}

.zhuige-market-pop-form p a:hover {
    opacity: .8;
}

.zhuige-market-pop-form p.zhuige-market-pop-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.zhuige-market-pop-form p.zhuige-market-pop-btn a {
    height: 36px;
    line-height: 36px;
    border-radius: 4px;
    background: #999999;
    color: #ffffff;
    width: 48%;
    text-align: center;
}

.zhuige-market-pop-form p.zhuige-market-pop-btn a:hover {
    opacity: .8;
}

.zhuige-market-pop-form p.zhuige-market-pop-btn a:nth-child(1) {
    background: #FF6146;
}
.authorization .zhuige-market-pop-title {
    padding: 0 20px 10px;
    flex-wrap: wrap;
}
.authorization .zhuige-market-pop-title div {
    width: 100%;
    text-align: center;
    padding: 0;
    margin: 0 0 8px 0;
}

.authorization .zhuige-market-pop-form p.zhuige-market-pop-btn a:nth-child(2) {
    background: #FF6146;
}
.zhuige-plugins-market-notice {
    background: #FFFFFF;
    border-radius: 6px;
    padding: 2px 20px;
    margin-bottom: 20px;
}

.slide-down {
    -webkit-animation: slide-down 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: slide-down 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

@-webkit-keyframes slide-down {
    0% {
        -webkit-transform: translateY(-25px);
        transform: translateY(-25px);
        opacity: 0;
    }

    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slide-down {
    0% {
        -webkit-transform: translateY(-25px);
        transform: translateY(-25px);
        opacity: 0;
    }

    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-top {
    -webkit-animation: slide-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
    animation: slide-top 0.5s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}

@-webkit-keyframes slide-top {
    0% {
        -webkit-transform: translateY(50px);
        transform: translateY(50px);
        opacity: 0;
    }

    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slide-top {
    0% {
        -webkit-transform: translateY(50px);
        transform: translateY(50px);
        opacity: 0;
    }

    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
        opacity: 1;
    }
}