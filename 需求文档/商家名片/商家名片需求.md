# 商家名片需求

## 一、功能描述

希望在当前项目中实现“商家名片”的功能，用于展示商家的相关信息。主要包括以下功能：

### 1.1 发布名片页面

发布名片页面路径：“client/pages/business-card/post/post”，该页面已创建，但里面的内容不对，请按照以下页面内容修改为正确的内容。

![](.assets/商家名片创建.png)

要求：尽量使用(复用)该页面中已有的代码、工具类、风格、样式等调整为正确的“发布名片”页面内容。

后台接口地址：bcard/create

传入的参数：

```json
{
  "logo": "{\"id\":8550,\"title\":\"\",\"description\":\"\",\"alt\":\"\",\"url\":\"https://q.zhuige.com/wp-content/uploads/2025/03/jq1741700091REEZJX.png\",\"thumbnail\":\"https://q.zhuige.com/wp-content/uploads/2025/03/jq1741700091REEZJX-150x150.png\",\"width\":646,\"height\":779}",
  "title": "产品名称",
  "content": "详细介绍",
  "images": "[]",
  "contact": "123",
  "phone": "1223333333",
  "latitude": "",
  "longitude": "",
  "marker": "",
  "address": "",
  "position": "789",
  "web": "www.zhuige.com",
  "company": "公司名称",
  "qrcode": "{\"id\":8551,\"title\":\"\",\"description\":\"\",\"alt\":\"\",\"url\":\"https://q.zhuige.com/wp-content/uploads/2025/03/jq1741700097WGNIXP.png\",\"thumbnail\":\"https://q.zhuige.com/wp-content/uploads/2025/03/jq1741700097WGNIXP-150x150.png\",\"width\":591,\"height\":783}",
  "cat_id": 143
}
```



### 1.2 商家名片详情页面

详情页面路径(已创建)：“client/pages/business-card/detail/detail”，该页面已创建，但里面的内容不对，请按照以下页面内容修改为正确的内容。

![](.assets/商家名片详情.png)

要求：尽量使用(复用)该页面中已有的代码、工具类、风格、样式等调整为正确的“详情”页面内容。

后台接口地址：bcard/detail

传入的参数：

```json
  {
    "card_id": "7909"
  }
```

返回数据：

```json
  {
    "code": 0,
    "message": "操作成功！",
    "data": {
      "title": "顺丰速运",
      "content": "<p>顺丰于1993年成立于广东顺德，总部位于深圳。</p>\n<p>业务拓展至时效快递、经济快递、快运、同城即时配送等等。</p>\n",
      "tags": [
        {
          "id": 138,
          "name": "物流"
        },
        {
          "id": 139,
          "name": "顺丰速运"
        }
      ],
      "comment_count": "3",
      "views": 1266,
      "like_list": [
        {
          "user_id": "2979",
          "avatar": "https://q.zhuige.com/wp-content/uploads/2024/10/jq1730284406FHJJBI.jpg"
        },
        {
          "user_id": "2789",
          "avatar": "https://q.zhuige.com/wp-content/uploads/2024/07/jq1720108277YHRROF.jpg"
        },
        {
          "user_id": "2781",
          "avatar": "https://q.zhuige.com/wp-content/plugins/zhuige-xcx/public/images/avatar.jpg"
        },
        {
          "user_id": "1292",
          "avatar": "https://q.zhuige.com/wp-content/plugins/zhuige-xcx/public/images/avatar.jpg"
        },
        {
          "user_id": "4",
          "avatar": "https://q.zhuige.com/wp-content/uploads/2022/11/jq1669125260UOXKMT.jpg"
        },
        {
          "user_id": "22",
          "avatar": "https://q.zhuige.com/wp-content/uploads/2022/10/22.png"
        }
      ],
      "is_like": 0,
      "is_comment": 0,
      "logo": "https://q.zhuige.com/wp-content/uploads/2024/01/443.png",
      "images": [
        {
          "image": "https://q.zhuige.com/wp-content/uploads/2024/01/WeChat63f3405d53e43a25c05b3c5f30e7894d.png"
        }
      ],
      "card": {
        "driect_link_switch": "0",
        "driect_link": "",
        "contact": "顺丰速运",
        "phone": "95338",
        "location": {
          "marker": "",
          "address": "深圳市福永街道怀德社区怀德南路46号",
          "longitude": "",
          "latitude": ""
        },
        "position": "客服电话",
        "web": "http://www.sf-express.com",
        "company": "顺丰控股股份有限公司",
        "qrcode": {
          "url": "https://q.zhuige.com/wp-content/uploads/2024/01/sf-code-img.9c3b712.png",
          "id": "7912",
          "width": "400",
          "height": "400",
          "thumbnail": "https://q.zhuige.com/wp-content/uploads/2024/01/sf-code-img.9c3b712-150x150.png",
          "alt": "",
          "title": "sf-code-img.9c3b712",
          "description": ""
        },
        "cardui": "1"
      },
      "score": "4.7",
      "comment_switch": 1,
      "rec_cat": {
        "id": 109,
        "name": "找物流"
      },
      "recs": [
        {
          "id": 7915,
          "title": "德邦快递",
          "excerpt": "A股上市物流快递企业",
          "logo": "https://q.zhuige.com/wp-content/uploads/2024/01/bd.png",
          "score": 0,
          "tags": [
            {
              "id": 140,
              "name": "德邦快递"
            },
            {
              "id": 141,
              "name": "快递"
            },
            {
              "id": 138,
              "name": "物流"
            }
          ],
          "driect_link_switch": "0",
          "driect_link": "",
          "stick": 0
        },
        {
          "id": 7854,
          "title": "京东物流",
          "excerpt": "京东集团2007年开始自建物流，2017年4月正式成立京东物流集团，2021年5月，京东物流于香港联...",
          "logo": "https://q.zhuige.com/wp-content/uploads/2023/12/962bd40735fae6cd7b89b55590f9182442a7d933a1e7.jpg",
          "score": "5.0",
          "tags": [
            {
              "id": 137,
              "name": "京东"
            },
            {
              "id": 136,
              "name": "京东物流"
            },
            {
              "id": 138,
              "name": "物流"
            }
          ],
          "driect_link_switch": "0",
          "driect_link": "",
          "stick": 0
        }
      ],
      "is_show_promotion": 0,
      "is_show_edit": 0,
      "is_promotion": 0
    }
  }
```

## 二、要求

1.根据我上传的设计稿截图开发，尽可能还原设计稿。

2.尽量使用(复用)该页面中已有的代码、工具类、风格、样式等调整为正确页面内容。

3.先实现“详情”，然后再实现“发布名片”页面。

4.先只实现小程序前端页面，暂不实现后台接口。

5.每完成一个功能后，提示用户“是否继续“。若用户回复“继续”，则根据设计步骤进行下一个功能开发。