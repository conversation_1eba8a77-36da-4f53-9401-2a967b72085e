{
  "code": 0,
  "message": "操作成功！",
  "data": {
    //"id": 71,
    //"title": "测试一下",
    // "comment_count": "1",
    // "excerpt": " 爱，始于自我欺骗，终于欺骗他人。这就是所谓的浪漫。 心相印京东自营官方旗舰店 心相印抽纸【肖战同款】茶语丝享 3层150抽*24包真S码 纸巾（整箱销售） 京东自营 ",
    "thumbnails": [
      "https://img14.360buyimg.com/pop/jfs/t1/272026/13/11546/121836/67e6617fF37a1c72d/943e2ba573250ad4.png.avif"
    ],
    //  "thumbnail": "https://img14.360buyimg.com/pop/jfs/t1/272026/13/11546/121836/67e6617fF37a1c72d/943e2ba573250ad4.png.avif",
    // "time": "2025-03-29",
    // "views": 195,
    "likes": 1,
    //    "author": {
    //      "user_id": "1",
    //      "nickname": "root",
    //      "avatar": "https://t.xiaomaicloud.com/wp-content/plugins/zhuige-xcx/public/images/avatar.jpg",
    //      "sign": "",
    //      "certify": {
    //        "status": 0
    //      },
    //      "vip": {
    //        "status": 0
    //      }
    //    },
    "stick": 0,
    "driect_link_switch": "0",
    "driect_link": "",
    "badge": "",
//    "read_limit": "free",
//    "cost_price": "",
//    "cost_score": "",
//    "cost_ios_switch": "0",
    "post_type": "post",
    "post_type_name": "文章",
    "link": "/pages/cms/detail/detail",
    "like_list": [],
//    "content": "<!-- wp:paragraph -->\n<p>爱，始于自我欺骗，终于欺骗他人。这就是所谓的浪漫。</p>\n<!-- /wp:paragraph -->\n\n<!-- wp:html -->\n<a data-link=\"appid:wx91d27dbf599dff74;page:pages/proxy/union/union?spreadUrl=https://u.jd.com/OO536wQ\">\n    <div class=\"zhuige-shortcode-box\">\n        <div><img decoding=\"async\"\n                  src=\"https://img14.360buyimg.com/pop/jfs/t1/272026/13/11546/121836/67e6617fF37a1c72d/943e2ba573250ad4.png.avif\"\n                  mode=\"aspectFill\"/></div>\n        <div class=\"zhuige-shortcode-content\">\n            <div class=\"zhuige-shortcode-title\">心相印京东自营官方旗舰店</div>\n            <div class=\"zhuige-shortcode-synopsis\">心相印抽纸【肖战同款】茶语丝享 3层150抽*24包真S码 纸巾（整箱销售）</div>\n            <div class=\"zhuige-shortcode-operation\">\n                <a class=\"button\"\n                   data-link=\"appid:wx91d27dbf599dff74;page:pages/proxy/union/union?spreadUrl=https://u.jd.com/OO536wQ\">京东自营</a>\n            </div>\n        </div>\n    </div>\n</a>\n<!-- /wp:html -->\n\n<!-- wp:paragraph -->\n<p></p>\n<!-- /wp:paragraph -->",
    "user": {
      "is_like": 0,
      "is_favorite": 0,
      "is_comment": 1
    },
//    "tags": [
//      {
//        "id": 44,
//        "name": "标签01"
//      },
//      {
//        "id": 43,
//        "name": "测试"
//      }
//    ],
    // "favorites": 0,
    // "comment_switch": 1,
    //  "comment_require_mobile": 0,
    "read_limit_switch": 1,
    "is_like": 0,
    "is_comment": 1,
    "is_favorite": 0,
//    "recs": [
//      {
//        "id": 43,
//        "title": "中秋佳节，情满人间",
//        "thumbnail": "https://yunjut.oss.xiaomaicloud.com/wp-content/uploads/2025/03/7fb51313cc3f4bda810bc6164fa7c0c6_1561973189.png",
//        "views": 14,
//        "likes": 0,
//        "driect_link_switch": "0",
//        "driect_link": ""
//      },
//      {
//        "id": 140,
//        "title": "测试追格块",
//        "thumbnail": "",
//        "views": 19,
//        "likes": 0,
//        "driect_link_switch": "0",
//        "driect_link": ""
//      }
//    ],
    "comment_require_avatar": 0
  }
}